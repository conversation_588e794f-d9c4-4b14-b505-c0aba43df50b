package com.base.framework.aspectj;

import com.base.common.annotation.RedisCacheAnno;
import com.base.common.constant.CacheConstants;
import com.base.common.core.redis.RedisCache;
import com.base.common.utils.CustomKeyParserUtils;
import com.base.common.utils.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
public class RedisCacheAspect {

    @Autowired
    private RedisCache redisCache;

    private final ParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(redisCacheAnno)")
    public Object around(ProceedingJoinPoint pjp, RedisCacheAnno redisCacheAnno) throws Throwable {
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        Object[] args = pjp.getArgs();
        String[] paramNames = nameDiscoverer.getParameterNames(method);

        // 动态生成缓存 Key
        String cacheKey = CacheConstants.REDIS_CACHE_PREFIX + CustomKeyParserUtils.parse(redisCacheAnno.key(), method, pjp.getArgs());
        Object cached = redisCache.getCacheObject(cacheKey);
        if (cached != null) {
            return cached;
        }

        Object result = pjp.proceed();
        if (result instanceof Collection && ((Collection<?>) result).isEmpty()){
            return result;
        }
        if (StringUtils.isNull(result)){
            return result;
        }
        redisCache.setCacheObject(cacheKey, result, redisCacheAnno.expire(), TimeUnit.SECONDS);
        return result;
    }
}
