package com.base.system.domain;

import lombok.Data;

import java.util.List;

@Data
public class Product {

    // 产品ID
    private int productId;

    // 产品名称
    private String name;

    // 产品版本
    private String version;

    // 产品图标
    private String icon;

    // 产品状态（如“已开通”、“告警”等）
    private String status;

    // 是否启用（如“已启用”、“未启用”）
    private String isEnabled;

    // 产品图标
    private String appImage;

    // 产品二维码
    private String qrCode;

    // 产品介绍
    private String introduction;

    // 子产品列表
    private List<Product> children;

    // 更新日志
    private List<UpdateLog> updateLogs;

    // 标签列表
    private List<String> tags;

    // 功能点列表
    private List<String> features;

    // 新增字段：已读状态，默认值为 0（未读）
    private int readStatus;

    private boolean select;

    private String externalId;

    // 新增内部类：更新日志
    @Data
    public static class UpdateLog {

        // 版本
        private String version;

        // 日志
        private String log;

        // 新增：更新时间
        private String updateTime;
    }
}
