package com.base.system.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 区域信息对象 sys_process
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
public class SysProcess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 名称 */
    @Excel(name = "名称")
    @JsonAlias("process_name")
    @NotBlank(message = "区域名称不能为空")
    @Size(message = "区域名称不能超过30字", min = 1, max = 30, groups = {SysProcessAdd.class})
    private String processName;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonAlias("e9_code")
    private String e9Code;

    /** 父级ID */
    @Excel(name = "父级ID")
    @JsonAlias("parent_id")
    @NotNull(message = "父级区域不能为空", groups = {SysProcessAdd.class})
    private Long parentId;

    /** plant:分厂 process:工序 unit:单元 */
    @Excel(name = "plant:分厂 process:工序 unit:单元")
    @JsonAlias("process_type")
    @NotBlank(message = "区域类型不能为空")
    private String processType;

    /** x坐标 */
    @Excel(name = "x坐标")
    @JsonAlias("pos_x")
    private String posX;

    /** y坐标 */
    @Excel(name = "y坐标")
    @JsonAlias("pos_y")
    private String posY;

    /** z坐标 */
    @Excel(name = "z坐标")
    @JsonAlias("pos_z")
    private String posZ;

    /** 删除标识 */
    @JsonAlias("del_flag")
    private Long delFlag;

    /** 祖级ID */
    @Excel(name = "祖级ID")
    @JsonAlias("ancestors")
    private String ancestors;

    /** 所属部门 */
    @Excel(name = "所属部门")
    @JsonAlias("dept_id")
    private Long deptId;

    /** （0正常 1停用） */
    @Excel(name = "", readConverterExp = "0=正常,1=停用")
    @JsonAlias("status")
    private Long status = 0L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonAlias("process_id")
    @NotNull(message = "区域ID不能为空", groups = SysProcessEdit.class)
    private Long processId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonAlias("sort")
    private Long sort;

    /**
     * 祖级名称
     */
    @JsonAlias("ancestors_name")
    private String ancestorsName;

    /**
     * 子节点
     */
    @JsonAlias("children")
    private List<SysProcess> children = new ArrayList<>();

    public interface SysProcessAdd {

    }

    public interface SysProcessEdit {

    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("processName", getProcessName())
                .append("e9Code", getE9Code())
                .append("parentId", getParentId())
                .append("processType", getProcessType())
                .append("posX", getPosX())
                .append("posY", getPosY())
                .append("posZ", getPosZ())
                .append("remark", getRemark())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("ancestors", getAncestors())
                .append("deptId", getDeptId())
                .append("status", getStatus())
                .append("processId", getProcessId())
                .append("sort", getSort())
                .toString();
    }
}
