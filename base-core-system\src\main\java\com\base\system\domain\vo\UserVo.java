package com.base.system.domain.vo;

import java.io.Serializable;

/**
 * flowable模块用户vo类
 */
public class UserVo implements Serializable {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 部门id
     */
    private String deptId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }


    public UserVo(Long userId, String userName, String deptId) {
        this.userId = userId;
        this.userName = userName;
        this.deptId = deptId;
    }

    public UserVo() {
    }
}
