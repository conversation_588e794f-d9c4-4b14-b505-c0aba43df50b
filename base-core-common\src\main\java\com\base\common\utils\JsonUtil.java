package com.base.common.utils;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Component;

import java.util.AbstractMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
public class JsonUtil {

    public static Set<Map.Entry<String, Object>> getEntrySet(JSONObject json) {
        Set<Map.Entry<String, Object>> set = new HashSet<>();
        for (String key : json.keySet()) {
            set.add(new AbstractMap.SimpleEntry<>(key, json.get(key)));
        }
        return set;
    }
}
