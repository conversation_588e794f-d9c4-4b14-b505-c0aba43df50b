package com.base.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.annotation.DataSource;
import com.base.common.constant.CacheConstants;
import com.base.common.constant.UserConstants;
import com.base.common.core.redis.RedisCache;
import com.base.common.core.text.Convert;
import com.base.common.enums.DataSourceType;
import com.base.common.exception.ServiceException;
import com.base.common.utils.StringUtils;
import com.base.common.utils.mqtt.MQTTUtils;
import com.base.system.domain.SysConfig;
import com.base.system.mapper.SysConfigMapper;
import com.base.system.service.ISysConfigService;
import com.base.system.service.ISysStationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 参数配置 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysConfigServiceImpl implements ISysConfigService {
    private static final Logger log = LoggerFactory.getLogger(SysConfigServiceImpl.class);
    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    MQTTUtils mqttUtils;

    @Autowired
    private ISysStationService sysStationService;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        loadingConfigCache();
    }

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public SysConfig selectConfigById(Long configId) {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        return configMapper.selectConfig(config);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue))
        {
            return configValue;
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取验证码开关
     *
     * @return true开启，false关闭
     */
    @Override
    public boolean selectCaptchaEnabled() {
        String captchaEnabled = selectConfigByKey("sys.account.captchaEnabled");
        if (StringUtils.isEmpty(captchaEnabled)) {
            return true;
        }
        return Convert.toBool(captchaEnabled);
    }

    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfig> selectConfigList(SysConfig config) {
        return configMapper.selectConfigList(config);
    }

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int insertConfig(SysConfig config) {
//        if (StringUtils.length(config.getConfigValue()) > 500){
//            throw new ServiceException(StringUtils.format("{}中内容太多了, 请尝试删减一些吧", config.getConfigName()));
//        }
        int row = configMapper.insertConfig(config);
        if (row > 0)
        {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 更新城市
     *
     * @param config
     */
    public void updateCity(SysConfig config) {
        if (StringUtils.equals(config.getConfigKey(), "sys.corp.location")) {
            try {
                String configValue = config.getConfigValue();
                JSONArray cityLocation = JSON.parseArray(configValue);
                String province = cityLocation.get(0).toString();
                String city = cityLocation.get(1).toString();
                String county = cityLocation.get(2).toString();

                SysConfig cityName = new SysConfig();
                cityName.setConfigKey("sys.corp.city");
                cityName.setConfigValue(city);
                this.updateConfig(cityName);

                ClassPathResource resource = new ClassPathResource("city_code.json");
                InputStream inputStream = resource.getInputStream();

                // 将InputStream转换为String
                String jsonString = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                        .lines()
                        .collect(Collectors.joining("\n"));

                // 使用fastjson2将json字符串转换为HashMap
                JSONObject cityCodes = JSON.parseObject(jsonString);

                JSONObject cityCode = (JSONObject)((JSONObject) ((JSONObject) cityCodes.get(province))
                        .get(city)).get(county);
                SysConfig cityCodeConfig = new SysConfig();
                cityCodeConfig.setConfigKey("sys.corp.city.code");
                cityCodeConfig.setConfigValue(cityCode.get("AREAID").toString());
                this.updateConfig(cityCodeConfig);
                sysStationService.updateStationsWhenEnterpriseLocationChanges(city);

            } catch (Exception e) {
                e.printStackTrace();
                log.error("更新城市名称和城市编号失败");
            }
        }
    }

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int updateConfig(SysConfig config) {
//        if (StringUtils.length(config.getConfigValue()) > 500){
//            throw new ServiceException(StringUtils.format("{}中内容太多了, 请尝试删减一些吧", config.getConfigName()));
//        }
        int row = 0;
        if (StringUtils.isNotNull(config.getConfigId())) {
            SysConfig temp = this.selectConfigById(config.getConfigId());
            redisCache.deleteObject(getCacheKey(temp.getConfigKey()));
            row = configMapper.updateConfig(config);
        } else if (StringUtils.isNotNull(config.getConfigKey())) {
            redisCache.deleteObject(getCacheKey(config.getConfigKey()));
            row = configMapper.updateConfigByKey(config);
        } else {
            throw new ServiceException("缺少内容, 无法更新");
        }

        if (row > 0)
        {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        updateCity(config);
        return row;
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(Long[] configIds) {
        for (Long configId : configIds) {
            SysConfig config = selectConfigById(configId);
            if (StringUtils.equals(UserConstants.YES, config.getConfigType())) {
                throw new ServiceException(String.format("内置参数【%1$s】不能删除 ", config.getConfigKey()));
            }
            configMapper.deleteConfigById(configId);
            redisCache.deleteObject(getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache() {
        List<SysConfig> configsList = configMapper.selectConfigList(new SysConfig());
        for (SysConfig config : configsList)
        {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache() {
        Collection<String> keys = redisCache.keys(CacheConstants.SYS_CONFIG_KEY + "*");
        redisCache.deleteObject(keys);
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean checkConfigKeyUnique(SysConfig config) {
        Long configId = StringUtils.isNull(config.getConfigId()) ? -1L : config.getConfigId();
        SysConfig info = configMapper.checkConfigKeyUnique(config.getConfigKey());
        if (StringUtils.isNotNull(info) && info.getConfigId().longValue() != configId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return CacheConstants.SYS_CONFIG_KEY + configKey;
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数键名
     * @return 参数键值
     */
    @Override
    public SysConfig getConfigByKey(String configKey) {
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        return configMapper.selectConfig(config);
    }
}
