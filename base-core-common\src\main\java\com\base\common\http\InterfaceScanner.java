package com.base.common.http;

import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.ClassUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/10/16
 */
public class InterfaceScanner {

    private static final String RESOURCE_PATTERN = "/**/*.class";

    public Set<Class<?>> findCandidateInterfaces(String basePackage) {
        try {
            Set<Class<?>> candidates = new HashSet<>();

            // 构建包路径
            String packageSearchPath = "classpath*:" + ClassUtils.convertClassNameToResourcePath(basePackage) + RESOURCE_PATTERN;

            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources(packageSearchPath);

            // 处理每一个找到的类
            for (Resource resource : resources) {
                String className = getClassName(resource, basePackage);
                Class<?> clazz = Class.forName(className);

                // 只处理接口
                if (clazz.isInterface()) {
//                    System.out.println("Found interface: " + clazz.getName());
                    candidates.add(clazz);
                }
            }
            return candidates;
        } catch (Exception e) {
            e.printStackTrace();
            return new HashSet<>();
        }
    }

    // 将 Resource 转换为类名
    private String getClassName(Resource resource, String basePackage) throws Exception {
        String resourcePath = resource.getURI().toString();
        String basePath = ClassUtils.convertClassNameToResourcePath(basePackage);
        int startIndex = resourcePath.indexOf(basePath);
        int endIndex = resourcePath.lastIndexOf(".class");
        String className = resourcePath.substring(startIndex, endIndex).replace("/", ".");
        return className;
    }
}
