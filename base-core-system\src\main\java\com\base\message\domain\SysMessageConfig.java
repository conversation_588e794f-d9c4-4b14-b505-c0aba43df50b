package com.base.message.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 消息通知配置对象 sys_message_config
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@ApiModel("消息通知配置对象")
public class SysMessageConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 消息通知类型，字典配置sys_message_type
     */
    @Excel(name = "消息通知类型，字典配置sys_message_type")
    @ApiModelProperty("消息通知类型，字典配置sys_message_type")
    private String type;

    /**
     * 菜单id
     */
    @Excel(name = "菜单id")
    @ApiModelProperty("菜单id")
    private Long menuId;

    /**
     * 弹窗参数
     */
    @Excel(name = "弹窗参数")
    @ApiModelProperty("弹窗参数")
    private String dialogQuery;

    /**
     * 参数
     */
    @Excel(name = "参数")
    @ApiModelProperty("参数")
    private String query;

    /**
     * 删除标志：0代表存在，2代表删除，默认0
     */
    @ApiModelProperty(hidden = true)
    private Integer delFlag;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public Long getMenuId() {
        return menuId;
    }

    public void setDialogQuery(String dialogQuery) {
        this.dialogQuery = dialogQuery;
    }

    public String getDialogQuery() {
        return dialogQuery;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getQuery() {
        return query;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("type", getType())
                .append("menuId", getMenuId())
                .append("dialogQuery", getDialogQuery())
                .append("query", getQuery())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
