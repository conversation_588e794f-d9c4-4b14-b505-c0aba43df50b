package com.base.system.service;

import com.alibaba.fastjson2.JSONArray;
import com.base.system.domain.SysStationData;

import java.util.Date;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ISysStationDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param regionId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SysStationData selectSysStationDataById(int regionId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sysStationData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SysStationData> selectSysStationDataList(SysStationData sysStationData);

    /**
     * 新增【请填写功能名称】
     *
     * @param sysStationData 【请填写功能名称】
     * @return 结果
     */
    public int insertSysStationData(SysStationData sysStationData);

    /**
     * 修改【请填写功能名称】
     *
     * @param sysStationData 【请填写功能名称】
     * @return 结果
     */
    public int updateSysStationData(SysStationData sysStationData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param regionIds 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteSysStationDataByIds(int[] regionIds);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param regionId 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysStationDataByRegionId(int regionId);

    JSONArray getStationData();

    JSONArray getStationData(Date date);

    JSONArray getStationDataChart(Date startTime);

    int batchSysStationData(List<SysStationData> sysStationDataList);
}
