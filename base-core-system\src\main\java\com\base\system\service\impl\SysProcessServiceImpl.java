package com.base.system.service.impl;

import com.base.common.annotation.RedisCacheAnno;
import com.base.common.core.redis.RedisCache;
import com.base.common.exception.ServiceException;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.TreeUtils;
import com.base.system.domain.SysProcess;
import com.base.system.mapper.SysProcessMapper;
import com.base.system.service.ISysProcessService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 区域信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Service
public class SysProcessServiceImpl implements ISysProcessService {
    @Autowired
    private SysProcessMapper sysProcessMapper;

    @Autowired
    RedisCache redisCache;

    public static final String SYS_PROCESS_CACHE_KEY = "SysProcessCache:";

    /**
     * 查询区域信息
     *
     * @param processId 区域信息主键
     * @return 区域信息
     */
    @Override
    public SysProcess selectSysProcessByProcessId(Long processId) {
        return sysProcessMapper.selectSysProcessByProcessId(processId);
    }

    /**
     * 查询区域信息列表
     *
     * @param sysProcess 区域信息
     * @return 区域信息
     */
    @Override
    public List<SysProcess> selectSysProcessList(SysProcess sysProcess) {
        return sysProcessMapper.selectSysProcessList(sysProcess);
    }

    /**
     * 新增区域信息
     *
     * @param sysProcess 区域信息
     * @return 结果
     */
    @Override
    public int insertSysProcess(SysProcess sysProcess) {
        // 设置创建时间
        sysProcess.setCreateTime(DateUtils.getNowDate());
        // 如果创建者为空，则设置为当前登录用户
        if (StringUtils.isBlank(sysProcess.getCreateBy())) {
            sysProcess.setCreateBy(SecurityUtils.getUsername());
        }

        // 获取父级区域ID
        Long parentId = sysProcess.getParentId();
        if (parentId != null && parentId != 0) {
            // 查询父级区域
            SysProcess parentProcess = sysProcessMapper.selectSysProcessByProcessId(parentId);
            // 检查父级区域是否存在且未被删除
            if (parentProcess == null || parentProcess.getDelFlag() != 0) {
                throw new ServiceException("父级区域已删除");
            }
            // 设置祖先路径
            sysProcess.setAncestors(parentProcess.getAncestors() + "," + parentProcess.getProcessId());
        } else {
            // 如果没有父级区域，则设置默认值
            sysProcess.setAncestors("0");
            sysProcess.setParentId(0L);
        }
        // 设置删除标志为0（未删除）
        sysProcess.setDelFlag(0L);

        // 插入区域信息到数据库
        return sysProcessMapper.insertSysProcess(sysProcess);
    }

    /**
     * 修改区域信息
     *
     * @param sysProcess 区域信息
     * @return 结果
     */
    @Override
    public int updateSysProcess(SysProcess sysProcess) {
        // 设置更新时间
        sysProcess.setUpdateTime(DateUtils.getNowDate());
        // 如果更新者为空，则设置为当前登录用户
        if (StringUtils.isBlank(sysProcess.getUpdateBy())) {
            sysProcess.setUpdateBy(SecurityUtils.getUsername());
        }

        // 查询旧的区域信息
        SysProcess oldProcess = sysProcessMapper.selectSysProcessByProcessId(sysProcess.getProcessId());

        // 获取新的父级区域ID
        Long parentId = sysProcess.getParentId();
        if (parentId != null && parentId != 0) {
            // 查询新的父级区域
            SysProcess newParent = sysProcessMapper.selectSysProcessByProcessId(parentId);
            // 检查新的父级区域是否存在且未被删除
            if (newParent == null || newParent.getDelFlag() != 0) {
                throw new ServiceException("父级区域已删除");
            }
            // 设置新的祖先路径
            sysProcess.setAncestors(newParent.getAncestors() + "," + newParent.getProcessId());
        } else {
            // 如果没有新的父级区域，则设置默认值
            sysProcess.setAncestors("0");
            sysProcess.setParentId(0L);
        }

        // 更新区域信息到数据库
        int updateResult = sysProcessMapper.updateSysProcess(sysProcess);
        this.clear(sysProcess.getProcessId());

        // 如果祖先路径发生变化，则更新所有子区域的祖先路径
        if (!sysProcess.getAncestors().equals(oldProcess.getAncestors())) {
            // 查询所有子区域
            List<SysProcess> childrenList = selectChildrenById(sysProcess.getProcessId());
            for (SysProcess child : childrenList) {
                // 更新子区域的祖先路径
                child.setAncestors(sysProcess.getAncestors() + "," + sysProcess.getProcessId());
                // 更新子区域到数据库
                sysProcessMapper.updateSysProcess(child);
                this.clear(child.getProcessId());
            }
        }

        return updateResult;
    }

    // 添加一个方法来查询所有子区域
    private List<SysProcess> selectChildrenById(Long parentId) {
        SysProcess processItem = new SysProcess();
        processItem.setParentId(parentId);
        return sysProcessMapper.selectSysProcessList(processItem);
    }

    /**
     * 批量删除区域信息
     *
     * @param processIds 需要删除的区域信息主键
     * @return 结果
     */
    @Override
    public int deleteSysProcessByProcessIds(Long[] processIds) {
        return sysProcessMapper.deleteSysProcessByProcessIds(processIds);
    }

    /**
     * 删除区域信息信息
     *
     * @param processId 区域信息主键
     * @return 结果
     */
    @Override
    public int deleteSysProcessByProcessId(Long processId) {
        return sysProcessMapper.deleteSysProcessByProcessId(processId);
    }

    public List<SysProcess> selectSysProcessByIdIn(List<Long> processList) {
        return sysProcessMapper.selectSysProcessByIdIn(processList);
    }

    public SysProcess selectDetailByProcessId(Long processId){
        SysProcess sysProcess = this.selectSysProcessByProcessId(processId);
        if (sysProcess != null){
            return sysProcess.setAncestorsName(this.getAncestorsName(sysProcess));
        }else{
            return null;
        }
    }

    public String getAncestorsName(Long processId) {
        // 查询指定区域ID的区域信息
        SysProcess process = this.selectSysProcessByProcessId(processId);
        return this.getAncestorsName(process);
    }

    /**
     * 根据区域ID获取祖先区域名称
     * 该方法通过递归查询祖先区域，并将它们的名称连接成一个字符串返回
     * 主要步骤包括：查询区域信息、解析祖先ID、批量查询祖先区域、构建名称列表并返回
     *
     * @param process 区域，用于查询区域及其祖先信息
     * @return 祖先区域名称字符串，用连字符连接
     */
    public String getAncestorsName(SysProcess process) {
        // 初始化祖先名称列表
        List<String> ancestorsNames = new ArrayList<>();
        // 如果区域存在，则进一步处理
        if (process != null) {
            // 分割区域的祖先字符串为数组
            String[] parentIds = process.getAncestors().split(",");
            // 将字符串数组转换为Long类型的列表
            List<Long> parentIdList = Arrays.stream(parentIds).map(Long::valueOf).collect(Collectors.toList());
            // 批量查询祖先区域信息
            List<SysProcess> sysProcesses = this.selectSysProcessByIdIn(parentIdList);
            // 将查询结果转换为Map，以便快速查找
            Map<Long, SysProcess> sysProcessMap = sysProcesses.stream().collect(Collectors.toMap(SysProcess::getProcessId, Function.identity()));
            // 遍历祖先ID列表，获取并添加祖先名称
            for (Long parentId : parentIdList) {
                // 忽略无效的祖先ID（例如0）
                if (!parentId.equals(0L)) {
                    // 根据ID获取祖先区域
                    SysProcess parentProcess = sysProcessMap.get(parentId);
                    // 如果祖先区域存在，则添加其名称
                    if (parentProcess != null) {
                        ancestorsNames.add(parentProcess.getProcessName());
                    }
                }
            }
            // 添加当前区域的名称到列表末尾
            ancestorsNames.add(process.getProcessName());
        }
        // 使用连字符连接所有祖先名称，并返回
        return String.join("-", ancestorsNames);
    }

    /**
     * 递归构建完整的名称路径
     *
     * @param processId  区域ID
     * @param processMap 区域ID到区域对象的映射
     * @return 完整的名称路径
     */
    private String buildFullName(Long processId, Map<Long, SysProcess> processMap) {
        SysProcess process = processMap.get(processId);
        if (process.getParentId() == 0) { // 顶级节点
            return process.getProcessName();
        } else {
            String parentName = buildFullName(process.getParentId(), processMap);
            return parentName + "-" + process.getProcessName();
        }
    }

    /**
     * 获取所有区域及其祖先名称列表
     *
     * @return 区域列表，包含祖先名称路径
     */
    @Override
    public List<SysProcess> getListAncestorsName() {
        List<SysProcess> processList = this.selectSysProcessList(new SysProcess());
        Map<Long, SysProcess> processMap = processList.stream().collect(Collectors.toMap(SysProcess::getProcessId, Function.identity()));
        // 应用函数构建名称路径
        for (SysProcess process : processList) {
            process.setAncestorsName(buildFullName(process.getProcessId(), processMap));
        }
        return processList;
    }

    @Override
    public List<SysProcess> selectSysProcessTree(SysProcess processItem) {
        List<SysProcess> sysProcessList = this.selectSysProcessList(processItem);
        return TreeUtils.buildTree(sysProcessList, "processId", "parentId", "children");
    }

    @Override
    public void clear() {
        sysProcessMapper.clear();
    }

    /**
     * 选择指定祖先ID的所有子区域
     *
     * @param ancestorId 祖先区域的ID
     * @return 包含所有子区域的列表
     */
    @Override
    public List<SysProcess> selectChildren(Long ancestorId) {
        return this.sysProcessMapper.selectChildren(ancestorId);
    }

    /**
     * 选择所有区域
     *
     * @return 包含所有区域的列表
     */
    @Override
    @RedisCacheAnno(key="processAll", expire=0)
    public List<SysProcess> selectAll(){
        return this.sysProcessMapper.selectSysProcessList(new SysProcess());
    }

    /**
     * 选择所有区域并将其组织成一个基于区域ID的映射
     *
     * @return 一个基于区域ID映射的区域信息
     */
    @Override
    public Map<Long, SysProcess> selectIdMap(){
        return this.selectAll().stream().collect(Collectors.toMap(SysProcess::getProcessId, Function.identity()));
    }

    /**
     * 选择所有区域并将其组织成一个基于区域名称的映射
     * 如果有重复的区域名称，保留最后一个出现的区域
     *
     * @return 一个基于区域名称映射的区域信息
     */
    @Override
    public Map<String, SysProcess> selectNameMap(){
        return this.selectAll().stream().collect(Collectors.toMap(SysProcess::getProcessName, Function.identity(), (p1, p2) -> p2));
    }

    @Override
    @RedisCacheAnno(key = SYS_PROCESS_CACHE_KEY + "${processId}", expire = 0)
    public SysProcess selectCacheById(Long processId) {
        return this.selectDetailByProcessId(processId);
    }

    public void clear(Long processId){
        redisCache.deleteObject(SYS_PROCESS_CACHE_KEY + processId);
    }
}
