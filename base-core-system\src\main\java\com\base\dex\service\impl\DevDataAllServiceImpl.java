package com.base.dex.service.impl;

import com.base.common.utils.StringUtils;
import com.base.dex.domain.DevDataAll;
import com.base.dex.mapper.DevDataAllMapper;
import com.base.dex.service.IDevDataAllService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 所有设备数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Service
public class DevDataAllServiceImpl implements IDevDataAllService
{
    @Autowired
    private DevDataAllMapper devDataAllMapper;

    /**
     * 查询所有设备数据
     *
     * @param monitorTime 所有设备数据主键
     * @return 所有设备数据
     */
    @Override
    public DevDataAll selectDevDataAllByMonitorTime(String monitorTime)
    {
        return devDataAllMapper.selectDevDataAllByMonitorTime(monitorTime);
    }

    /**
     * 查询所有设备数据列表
     *
     * @param devDataAll 所有设备数据
     * @return 所有设备数据
     */
    @Override
    public List<DevDataAll> selectDevDataAllList(DevDataAll devDataAll)
    {
        return devDataAllMapper.selectDevDataAllList(devDataAll);
    }

    /**
     * 新增所有设备数据
     *
     * @param devDataAll 所有设备数据
     * @return 结果
     */
    @Override
    public int insertDevDataAll(DevDataAll devDataAll)
    {
        return devDataAllMapper.insertDevDataAll(devDataAll);
    }

    @Override
    public int batchDevDataAll(List<DevDataAll> devDataAllList)
    {
        if (StringUtils.isNotEmpty(devDataAllList)){
            return devDataAllMapper.batchDevDataAll(devDataAllList);
        }
        return 0;
    }

    /**
     * 修改所有设备数据
     *
     * @param devDataAll 所有设备数据
     * @return 结果
     */
    @Override
    public int updateDevDataAll(DevDataAll devDataAll)
    {
        return devDataAllMapper.updateDevDataAll(devDataAll);
    }

    /**
     * 批量删除所有设备数据
     *
     * @param monitorTimes 需要删除的所有设备数据主键
     * @return 结果
     */
    @Override
    public int deleteDevDataAllByMonitorTimes(String[] monitorTimes)
    {
        return devDataAllMapper.deleteDevDataAllByMonitorTimes(monitorTimes);
    }

    /**
     * 删除所有设备数据信息
     *
     * @param monitorTime 所有设备数据主键
     * @return 结果
     */
    @Override
    public int deleteDevDataAllByMonitorTime(String monitorTime)
    {
        return devDataAllMapper.deleteDevDataAllByMonitorTime(monitorTime);
    }

    /**
     * 根据时间范围查询所有设备数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @Override
    public List<DevDataAll> selectByTimeBetween(Date startTime, Date endTime){
        return devDataAllMapper.selectByTimeBetween(startTime, endTime);
    }
}
