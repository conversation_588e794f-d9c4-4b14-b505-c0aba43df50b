package com.base.web.controller.message;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.domain.entity.SysMenu;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StringUtils;
import com.base.message.domain.SysMessage;
import com.base.message.service.ISysMessageService;
import com.base.system.service.ISysDictDataService;
import com.base.system.service.ISysMenuService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息通知Controller
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Api(tags = "消息通知管理")
@RestController
@RequestMapping("/api/sys/message")
public class SysMessageController extends BaseController {
    @Autowired
    private ISysMessageService sysMessageService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysMenuService menuService;

    /**
     * 查询字典类型列表
     *
     * @param dictType  字典类型 sys_message_type
     * @param dictLabel 字典标签
     */
    @ApiOperation(value = "查询字典类型列表", httpMethod = "GET")
    @GetMapping(value = "/type/{dictType}")
    public AjaxResult getDictType(@PathVariable String dictType, String dictLabel) {
        SysDictData dictData = new SysDictData();
        dictData.setDictType(dictType);
        dictData.setDictLabel(dictLabel);
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        return success(list);
    }

    /**
     * 查询消息通知列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询消息通知列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "当前页", required = true, defaultValue = "1", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "每页显示数", required = true, defaultValue = "10", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "消息通知类型，字典配置sys_message_type，全部传空NULL", required = false, dataType = "String", paramType = "query")
    })
    @ApiResponses({@ApiResponse(code = 200, message = "OK", response = SysMessage.class)})
    public TableDataInfo list(String type) {
        SysMessage sysMessage = new SysMessage();
        sysMessage.setType(type);
        sysMessage.setUserId(SecurityUtils.getUsername());
        startPage();
        List<SysMessage> list = sysMessageService.selectSysMessageList(sysMessage);
        for (SysMessage sysMessageEntity : list) {
            // 根据菜单ID构建完整的路径字符串
            if (StringUtils.isNotNull(sysMessageEntity.getMenuId())) {
                String fullPath = buildFullPath(sysMessageEntity.getMenuId());
                sysMessageEntity.setPath(fullPath);
            }
        }
        return getDataTable(list);
    }

    /**
     * 根据菜单ID构建完整的路径字符串
     * 该方法从指定的菜单项开始，递归地向上遍历父菜单，直到找到顶级菜单，然后构建起从根到指定菜单的完整路径
     *
     * @param menuId 要构建路径的菜单项的ID
     * @return 完整路径的字符串表示
     */
    private String buildFullPath(Long menuId) {
        // 创建StringBuilder用于拼接菜单路径
        StringBuilder pathBuilder = new StringBuilder();
        // 获取当前菜单项的信息
        SysMenu currentMenu = menuService.selectMenuById(menuId);
        // 循环向上遍历菜单的父级，直到达到顶级菜单（parentID为0）
        while (currentMenu != null && currentMenu.getParentId() != 0) {
            // 在路径字符串的开头插入当前菜单路径
            pathBuilder.insert(0, "/" + currentMenu.getPath());
            // 更新当前菜单项为父菜单项，继续向上遍历
            currentMenu = menuService.selectMenuById(currentMenu.getParentId());
        }
        // 如果当前菜单项是顶级菜单，则添加其路径到字符串开头
        if (currentMenu != null && currentMenu.getParentId() == 0) {
            pathBuilder.insert(0, "/" + currentMenu.getPath());
        }
        // 返回构建完成的完整路径字符串
        return pathBuilder.toString();
    }

    /**
     * 查询消息通知-未读数量
     */
    @GetMapping("/getUnreadTotal")
    @ApiOperation(value = "查询消息通知未读数量", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "消息通知类型，字典配置sys_message_type，全部传空NULL", required = false, dataType = "String", paramType = "query")
    })
    public AjaxResult getUnreadTotal(String type) {
        SysMessage sysMessage = new SysMessage();
        sysMessage.setType(type);
        // 状态：0未读，1已读，默认0未读
        sysMessage.setStatus(0);
        sysMessage.setUserId(SecurityUtils.getUsername());
        List<SysMessage> list = sysMessageService.selectSysMessageList(sysMessage);
        return AjaxResult.success(list.size());
    }

    /**
     * 新增消息通知
     */
    @Log(title = "消息通知", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增消息通知", httpMethod = "POST")
    public AjaxResult add(@RequestBody SysMessage sysMessage) {
        if (StringUtils.isEmpty(sysMessage.getType()) || StringUtils.isNull(sysMessage.getMessageConfigId()) || StringUtils.isEmpty(sysMessage.getUserId())) {
            return AjaxResult.error("参数不能为空");
        }
        return toAjax(sysMessageService.insertSysMessage(sysMessage));
    }

    /**
     * 根据id修改消息通知-点击消息详情时调用
     */
    @Log(title = "消息通知", businessType = BusinessType.UPDATE)
    @PutMapping("/editById")
    @ApiOperation(value = "根据id修改消息通知-详情跳转列表时使用", httpMethod = "PUT")
    public AjaxResult editById(Long id) {
        if (StringUtils.isNull(id)) {
            return AjaxResult.error("参数不能为空");
        }
        int result = 0;
        SysMessage sysMessage = sysMessageService.selectSysMessageById(id);
        if (StringUtils.isNotNull(sysMessage) && StringUtils.isNotNull(sysMessage.getStatus()) && 0 == sysMessage.getStatus()) {
            SysMessage sysMessageEntity = new SysMessage();
            sysMessageEntity.setId(id);
            // 状态：0未读，1已读，默认0未读
            sysMessageEntity.setStatus(1);
            sysMessageEntity.setUpdateBy(SecurityUtils.getUsername());
            result = sysMessageService.updateSysMessage(sysMessageEntity);
        }
        return AjaxResult.success(result);
    }

    /**
     * 修改消息通知-全部已读
     */
    @PutMapping("/editReadStatus")
    @ApiOperation(value = "修改消息通知-全部已读", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "消息通知类型，字典配置sys_message_type，全部传空NULL", required = false, dataType = "String", paramType = "query")
    })
    public AjaxResult editReadStatus(String type) {
        SysMessage sysMessage = new SysMessage();
        sysMessage.setType(type);
        // 状态：0未读，1已读，默认0未读
        sysMessage.setStatus(1);
        sysMessage.setUserId(SecurityUtils.getUsername());
        sysMessage.setUpdateBy(SecurityUtils.getUsername());
        sysMessage.setUpdateTime(DateUtils.getNowDate());
        return AjaxResult.success(sysMessageService.updateSysMessageByCondition(sysMessage));
    }

//    /**
//     * 修改消息通知为已读
//     */
//    @Log(title = "消息通知", businessType = BusinessType.UPDATE)
//    @PostMapping("/edit")
//    @ApiOperation(value = "修改消息通知", httpMethod = "POST")
//    public AjaxResult edit(@RequestBody SysMessage sysMessage) {
//        if (StringUtils.isEmpty(sysMessage.getType()) || StringUtils.isNull(sysMessage.getMessageConfigId()) || StringUtils.isEmpty(sysMessage.getUserId())) {
//            return AjaxResult.error("参数不能为空");
//        }
//        return success(sysMessageService.updateSysMessageStatus(sysMessage));
//    }

}
