<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.SysStationDataMapper">

    <resultMap type="SysStationData" id="SysStationDataResult">
        <result property="regionId"    column="region_id"    />
        <result property="regionName"    column="region_name"    />
        <result property="pointCode"    column="point_code"    />
        <result property="pointName"    column="point_name"    />
        <result property="pointAddress"    column="point_address"    />
        <result property="pointLongitude"    column="point_longitude"    />
        <result property="pointLatitude"    column="point_latitude"    />
        <result property="so2"    column="so2"    />
        <result property="so224"    column="so2_24"    />
        <result property="no2"    column="no2"    />
        <result property="pm10"    column="pm10"    />
        <result property="pm1024"    column="pm10_24"    />
        <result property="co"    column="co"    />
        <result property="o3"    column="o3"    />
        <result property="o38"    column="o3_8"    />
        <result property="pm25"    column="pm25"    />
        <result property="pm2524"    column="pm25_24"    />
        <result property="time"    column="time"    />
        <result property="aqiScore"    column="aqi_score"    />
        <result property="aqiTrend"    column="aqi_trend"    />
        <result property="pollutionLevel"    column="pollution_level"    />
        <result property="pollutionLevelBig"    column="pollution_level_big"    />
        <result property="pollutionStatus"    column="pollution_status"    />
        <result property="topPollution"    column="top_pollution"    />
        <result property="type"    column="type"    />
        <result property="id"    column="id"    />
        <result property="sort"    column="sort"    />
    </resultMap>

    <sql id="selectSysStationDataVo">
        select region_id, region_name, point_code, point_name, point_address, point_longitude, point_latitude, so2, so2_24, no2, pm10, pm10_24, co, o3, o3_8, pm25, pm25_24, time, aqi_score, aqi_trend, pollution_level, pollution_level_big, pollution_status, top_pollution, type, id, sort from sys_station_data
    </sql>

    <select id="selectSysStationDataList" parameterType="SysStationData" resultMap="SysStationDataResult">
        <include refid="selectSysStationDataVo"/>
        <where>
            <if test="regionId != null  and regionId != ''"> and region_id = #{regionId}</if>
            <if test="regionName != null  and regionName != ''"> and region_name like concat('%', #{regionName}, '%')</if>
            <if test="pointCode != null  and pointCode != ''"> and point_code = #{pointCode}</if>
            <if test="pointName != null  and pointName != ''"> and point_name like concat('%', #{pointName}, '%')</if>
            <if test="pointAddress != null  and pointAddress != ''"> and point_address = #{pointAddress}</if>
            <if test="pointLongitude != null  and pointLongitude != ''"> and point_longitude = #{pointLongitude}</if>
            <if test="pointLatitude != null  and pointLatitude != ''"> and point_latitude = #{pointLatitude}</if>
            <if test="so2 != null  and so2 != ''"> and so2 = #{so2}</if>
            <if test="so224 != null  and so224 != ''"> and so2_24 = #{so224}</if>
            <if test="no2 != null  and no2 != ''"> and no2 = #{no2}</if>
            <if test="pm10 != null  and pm10 != ''"> and pm10 = #{pm10}</if>
            <if test="pm1024 != null  and pm1024 != ''"> and pm10_24 = #{pm1024}</if>
            <if test="co != null  and co != ''"> and co = #{co}</if>
            <if test="o3 != null  and o3 != ''"> and o3 = #{o3}</if>
            <if test="o38 != null  and o38 != ''"> and o3_8 = #{o38}</if>
            <if test="pm25 != null  and pm25 != ''"> and pm25 = #{pm25}</if>
            <if test="pm2524 != null  and pm2524 != ''"> and pm25_24 = #{pm2524}</if>
            <if test="time != null"> and time = #{time}</if>
            <if test="aqiScore != null  and aqiScore != ''"> and aqi_score = #{aqiScore}</if>
            <if test="aqiTrend != null  and aqiTrend != ''"> and aqi_trend = #{aqiTrend}</if>
            <if test="pollutionLevel != null  and pollutionLevel != ''"> and pollution_level = #{pollutionLevel}</if>
            <if test="pollutionLevelBig != null  and pollutionLevelBig != ''"> and pollution_level_big = #{pollutionLevelBig}</if>
            <if test="pollutionStatus != null  and pollutionStatus != ''"> and pollution_status = #{pollutionStatus}</if>
            <if test="topPollution != null  and topPollution != ''"> and top_pollution = #{topPollution}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectSysStationDataById" parameterType="Long" resultMap="SysStationDataResult">
        <include refid="selectSysStationDataVo"/>
        WHERE id = #{id}
    </select>

    <select id="selectStationDataByTime" parameterType="java.util.Date" resultType="com.base.system.domain.SysStationData">
        SELECT
            point_name as pointName,
            to_char(h.time, 'YYYY-MM-DD') as datestr,
            ROUND(AVG(CAST(h.pm25 AS FLOAT))) as pm25,
            ROUND(AVG( CAST(h.pm10 AS FLOAT))) as pm10
        FROM
            public.sys_station_data h
        WHERE
            h.time >= #{startTime}
        GROUP BY
            h.point_name,
            to_char(h.time, 'YYYY-MM-DD')
        ORDER BY
            h.point_name,  to_char(h.time, 'YYYY-MM-DD')
    </select>

    <insert id="insertSysStationData" parameterType="SysStationData">
        insert into sys_station_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regionId != null">region_id,</if>
            <if test="regionName != null">region_name,</if>
            <if test="pointCode != null">point_code,</if>
            <if test="pointName != null">point_name,</if>
            <if test="pointAddress != null">point_address,</if>
            <if test="pointLongitude != null">point_longitude,</if>
            <if test="pointLatitude != null">point_latitude,</if>
            <if test="so2 != null">so2,</if>
            <if test="so224 != null">so2_24,</if>
            <if test="no2 != null">no2,</if>
            <if test="pm10 != null">pm10,</if>
            <if test="pm1024 != null">pm10_24,</if>
            <if test="co != null">co,</if>
            <if test="o3 != null">o3,</if>
            <if test="o38 != null">o3_8,</if>
            <if test="pm25 != null">pm25,</if>
            <if test="pm2524 != null">pm25_24,</if>
            <if test="time != null">time,</if>
            <if test="aqiScore != null">aqi_score,</if>
            <if test="aqiTrend != null">aqi_trend,</if>
            <if test="pollutionLevel != null">pollution_level,</if>
            <if test="pollutionLevelBig != null">pollution_level_big,</if>
            <if test="pollutionStatus != null">pollution_status,</if>
            <if test="topPollution != null">top_pollution,</if>
            <if test="type != null">type,</if>
            <if test="id != null">id,</if>
            <if test="sort != null">sort,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regionId != null">#{regionId},</if>
            <if test="regionName != null">#{regionName},</if>
            <if test="pointCode != null">#{pointCode},</if>
            <if test="pointName != null">#{pointName},</if>
            <if test="pointAddress != null">#{pointAddress},</if>
            <if test="pointLongitude != null">#{pointLongitude},</if>
            <if test="pointLatitude != null">#{pointLatitude},</if>
            <if test="so2 != null">#{so2},</if>
            <if test="so224 != null">#{so224},</if>
            <if test="no2 != null">#{no2},</if>
            <if test="pm10 != null">#{pm10},</if>
            <if test="pm1024 != null">#{pm1024},</if>
            <if test="co != null">#{co},</if>
            <if test="o3 != null">#{o3},</if>
            <if test="o38 != null">#{o38},</if>
            <if test="pm25 != null">#{pm25},</if>
            <if test="pm2524 != null">#{pm2524},</if>
            <if test="time != null">#{time},</if>
            <if test="aqiScore != null">#{aqiScore},</if>
            <if test="aqiTrend != null">#{aqiTrend},</if>
            <if test="pollutionLevel != null">#{pollutionLevel},</if>
            <if test="pollutionLevelBig != null">#{pollutionLevelBig},</if>
            <if test="pollutionStatus != null">#{pollutionStatus},</if>
            <if test="topPollution != null">#{topPollution},</if>
            <if test="type != null">#{type},</if>
            <if test="id != null">#{id},</if>
            <if test="sort != null">#{sort},</if>
         </trim>
    </insert>

    <update id="updateSysStationData" parameterType="SysStationData">
        update sys_station_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="regionName != null">region_name = #{regionName},</if>
            <if test="pointCode != null">point_code = #{pointCode},</if>
            <if test="pointName != null">point_name = #{pointName},</if>
            <if test="pointAddress != null">point_address = #{pointAddress},</if>
            <if test="pointLongitude != null">point_longitude = #{pointLongitude},</if>
            <if test="pointLatitude != null">point_latitude = #{pointLatitude},</if>
            <if test="so2 != null">so2 = #{so2},</if>
            <if test="so224 != null">so2_24 = #{so224},</if>
            <if test="no2 != null">no2 = #{no2},</if>
            <if test="pm10 != null">pm10 = #{pm10},</if>
            <if test="pm1024 != null">pm10_24 = #{pm1024},</if>
            <if test="co != null">co = #{co},</if>
            <if test="o3 != null">o3 = #{o3},</if>
            <if test="o38 != null">o3_8 = #{o38},</if>
            <if test="pm25 != null">pm25 = #{pm25},</if>
            <if test="pm2524 != null">pm25_24 = #{pm2524},</if>
            <if test="time != null">time = #{time},</if>
            <if test="aqiScore != null">aqi_score = #{aqiScore},</if>
            <if test="aqiTrend != null">aqi_trend = #{aqiTrend},</if>
            <if test="pollutionLevel != null">pollution_level = #{pollutionLevel},</if>
            <if test="pollutionLevelBig != null">pollution_level_big = #{pollutionLevelBig},</if>
            <if test="pollutionStatus != null">pollution_status = #{pollutionStatus},</if>
            <if test="topPollution != null">top_pollution = #{topPollution},</if>
            <if test="type != null">type = #{type},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysStationDataById" parameterType="Long">
        delete from sys_station_data where id = #{id}
    </delete>

    <delete id="deleteSysStationDataByIds" parameterType="Long">
        delete from sys_station_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchSysStationData">
        insert into sys_station_data( region_id, region_name, point_code, point_name, point_address, point_longitude, point_latitude, so2, so2_24, no2, pm10, pm10_24, co, o3, o3_8, pm25, pm25_24, time, aqi_score, aqi_trend, pollution_level, pollution_level_big, pollution_status, top_pollution, type) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.regionId}, #{item.regionName}, #{item.pointCode}, #{item.pointName}, #{item.pointAddress}, #{item.pointLongitude}, #{item.pointLatitude}, #{item.so2}, #{item.so224}, #{item.no2}, #{item.pm10}, #{item.pm1024}, #{item.co}, #{item.o3}, #{item.o38}, #{item.pm25}, #{item.pm2524}, #{item.time}, #{item.aqiScore}, #{item.aqiTrend}, #{item.pollutionLevel}, #{item.pollutionLevelBig}, #{item.pollutionStatus}, #{item.topPollution}, #{item.type})
        </foreach>
    </insert>
</mapper>
