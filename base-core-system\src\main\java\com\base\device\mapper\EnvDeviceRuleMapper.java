package com.base.device.mapper;

import com.base.device.domain.EnvDeviceRule;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备运行规则(运行状态、报警等判断逻辑)Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Repository
public interface EnvDeviceRuleMapper
{
    /**
     * 查询设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param ruleId 设备运行规则(运行状态、报警等判断逻辑)主键
     * @return 设备运行规则(运行状态、报警等判断逻辑)
     */
    public EnvDeviceRule selectEnvDeviceRuleByRuleId(Long ruleId);

    /**
     * 查询设备运行规则(运行状态、报警等判断逻辑)列表
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 设备运行规则(运行状态、报警等判断逻辑)集合
     */
    public List<EnvDeviceRule> selectEnvDeviceRuleList(EnvDeviceRule envDeviceRule);

    /**
     * 新增设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 结果
     */
    public int insertEnvDeviceRule(EnvDeviceRule envDeviceRule);

    /**
     * 修改设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 结果
     */
    public int updateEnvDeviceRule(EnvDeviceRule envDeviceRule);

    /**
     * 删除设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param ruleId 设备运行规则(运行状态、报警等判断逻辑)主键
     * @return 结果
     */
    public int deleteEnvDeviceRuleByRuleId(Long ruleId);

    /**
     * 批量删除设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param ruleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEnvDeviceRuleByRuleIds(Long[] ruleIds);

    /**
     * 批量新增设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param envDeviceRuleList 设备运行规则(运行状态、报警等判断逻辑)列表
     * @return 结果
     */
    public int batchEnvDeviceRule(List<EnvDeviceRule> envDeviceRuleList);

    /**
     * 根据设备ID集合查询设备运行规则列表
     *
     * @param deviceIds 设备ID集合
     * @return 设备运行规则集合
     */
    List<EnvDeviceRule> selectByDeviceIdIn(List<Long> deviceIds);
}
