package com.base.common.enums;

import lombok.Getter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.function.UnaryOperator;

@Getter
public enum TimeType {
    YEAR("year", "y", "Y"),
    MONTH("month", "n", "M"),
    DAY("day", "d", "D"),
    HOUR("hour", "h", "H"),
    MINUTE("min", "m", "T"), // 保持与原有"min"一致，但使用更明确的名称
    SECOND("second", "s", "S");

    // Getters
    private final String type;
    private final String iotType;
    private final String freq;

    TimeType(String type, String iotType, String freq) {
        this.type = type;
        this.iotType = iotType;
        this.freq = freq;
    }

    public static TimeType fromType(String type) {
        if (type == null || type.isEmpty()) {
            throw new IllegalArgumentException("Time type cannot be null or empty");
        }

        return Arrays.stream(values())
                .filter(e -> e.type.equalsIgnoreCase(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid time type: " + type));
    }

    public static boolean isValidType(String type) {
        return Arrays.stream(values())
                .anyMatch(e -> e.type.equalsIgnoreCase(type));
    }

    public UnaryOperator<LocalDateTime> getStep() {
        return this.getStep(1);
    }

    public UnaryOperator<LocalDateTime> getStep(int freq) {
        switch (this) {
            case YEAR:
                return t -> t.plusYears(freq);
            case MONTH:
                return t -> t.plusMonths(freq);
            case DAY:
                return t -> t.plusDays(freq);
            case HOUR:
                return t -> t.plusHours(freq);
            case MINUTE:
                return t -> t.plusMinutes(freq);
            case SECOND:
                return t -> t.plusSeconds(freq);
            default:
                throw new IllegalArgumentException("Invalid time type: " + type);
        }
    }

}
