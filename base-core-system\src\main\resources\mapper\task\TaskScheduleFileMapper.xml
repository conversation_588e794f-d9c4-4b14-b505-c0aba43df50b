<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.task.mapper.TaskScheduleFileMapper">
    
    <resultMap type="TaskScheduleFile" id="TaskScheduleFileResult">
        <result property="id"    column="id"    />
        <result property="taskScheduleId"    column="task_schedule_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSuffix"    column="file_suffix"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectTaskScheduleFileVo">
        select id, task_schedule_id, file_name, file_path, file_suffix, create_by, create_time, del_flag from task_schedule_file
    </sql>

    <select id="selectTaskScheduleFileList" parameterType="TaskScheduleFile" resultMap="TaskScheduleFileResult">
        <include refid="selectTaskScheduleFileVo"/>
        <where>  
            <if test="taskScheduleId != null "> and task_schedule_id = #{taskScheduleId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="fileSuffix != null  and fileSuffix != ''"> and file_suffix = #{fileSuffix}</if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    
    <select id="selectTaskScheduleFileById" parameterType="Long" resultMap="TaskScheduleFileResult">
        <include refid="selectTaskScheduleFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTaskScheduleFile" parameterType="TaskScheduleFile" useGeneratedKeys="true" keyProperty="id">
        insert into task_schedule_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskScheduleId != null">task_schedule_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileSuffix != null">file_suffix,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskScheduleId != null">#{taskScheduleId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileSuffix != null">#{fileSuffix},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTaskScheduleFile" parameterType="TaskScheduleFile">
        update task_schedule_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskScheduleId != null">task_schedule_id = #{taskScheduleId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileSuffix != null">file_suffix = #{fileSuffix},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskScheduleFileById" parameterType="Long">
        delete from task_schedule_file where id = #{id}
    </delete>

    <delete id="deleteTaskScheduleFileByIds" parameterType="String">
        delete from task_schedule_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>