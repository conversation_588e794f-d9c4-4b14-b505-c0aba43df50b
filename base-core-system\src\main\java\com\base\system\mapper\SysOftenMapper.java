package com.base.system.mapper;

import com.base.system.domain.SysOften;

import java.util.List;

/**
 * 用户常用功能Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface SysOftenMapper
{
    /**
     * 查询用户常用功能
     *
     * @param oftenId 用户常用功能主键
     * @return 用户常用功能
     */
    public SysOften selectSysOftenByOftenId(Long oftenId);

    /**
     * 查询用户常用功能列表
     *
     * @param sysOften 用户常用功能
     * @return 用户常用功能集合
     */
    public List<SysOften> selectSysOftenList(SysOften sysOften);

    /**
     * 新增用户常用功能
     *
     * @param sysOften 用户常用功能
     * @return 结果
     */
    public int insertSysOften(SysOften sysOften);

    /**
     * 修改用户常用功能
     *
     * @param sysOften 用户常用功能
     * @return 结果
     */
    public int updateSysOften(SysOften sysOften);

    /**
     * 删除用户常用功能
     *
     * @param oftenId 用户常用功能主键
     * @return 结果
     */
    public int deleteSysOftenByOftenId(Long oftenId);

    /**
     * 批量删除用户常用功能
     *
     * @param oftenIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysOftenByOftenIds(Long[] oftenIds);
}
