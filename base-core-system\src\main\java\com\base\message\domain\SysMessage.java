package com.base.message.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;

/**
 * 消息通知对象 sys_message
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@ApiModel("消息通知对象")
public class SysMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 消息通知类型，字典配置sys_message_type
     */
    @Excel(name = "消息通知类型，字典配置sys_message_type")
    @ApiModelProperty("消息通知类型，字典配置sys_message_type")
    private String type;

    /**
     * 消息通知配置id
     */
    @Excel(name = "消息通知配置id")
    @ApiModelProperty("消息通知配置id")
    private Long messageConfigId;

    /**
     * 编码
     */
    @Excel(name = "编码")
    @ApiModelProperty("编码")
    private String code;

    /**
     * 标题
     */
    @Excel(name = "标题")
    @ApiModelProperty("标题")
    private String title;

    /**
     * 内容
     */
    @Excel(name = "内容")
    @ApiModelProperty("内容")
    private String content;

    /**
     * 状态：0未读，1已读，默认0未读
     */
    @Excel(name = "状态：0未读，1已读，默认0未读")
    @ApiModelProperty("状态：0未读，1已读，默认0未读")
    private Integer status;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    @ApiModelProperty("用户id")
    private String userId;

    /**
     * 删除标志：0代表存在，2代表删除，默认0
     */
    @ApiModelProperty(hidden = true)
    private Integer delFlag;

    /**
     * 动态参数，json格式
     */
    @ApiModelProperty("动态参数，json格式")
    private String param;

    /**
     * 菜单id
     */
    @ApiModelProperty("菜单id")
    private Long menuId;

    /**
     * 弹窗参数
     */
    @Excel(name = "弹窗参数")
    @ApiModelProperty("弹窗参数")
    private String dialogQuery;

    /**
     * 参数
     */
    @Excel(name = "参数")
    @ApiModelProperty("参数")
    private String query;

    /**
     * 菜单路径地址
     */
    private String path;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public Long getMessageConfigId() {
        return messageConfigId;
    }

    public void setMessageConfigId(Long messageConfigId) {
        this.messageConfigId = messageConfigId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public Long getMenuId() {
        return menuId;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public String getDialogQuery() {
        return dialogQuery;
    }

    public void setDialogQuery(String dialogQuery) {
        this.dialogQuery = dialogQuery;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("type", getType())
                .append("messageConfigId", getMessageConfigId())
                .append("code", getCode())
                .append("title", getTitle()).
                append("content", getContent())
                .append("status", getStatus())
                .append("userId", getUserId())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("param", getParam())
                .toString();
    }
}
