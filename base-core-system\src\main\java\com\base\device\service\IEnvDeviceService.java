package com.base.device.service;

import com.base.common.core.domain.entity.SysDictData;
import com.base.device.domain.EnvDevice;
import com.base.device.domain.dto.EnvDeviceDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 设备基础信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface IEnvDeviceService
{
    /**
     * 查询设备基础信息
     *
     * @param deviceId 设备基础信息主键
     * @return 设备基础信息
     */
    public EnvDevice selectEnvDeviceByDeviceId(Long deviceId);

    /**
     * 查询设备基础信息列表
     *
     * @param envDevice 设备基础信息
     * @return 设备基础信息集合
     */
    public List<EnvDevice> selectEnvDeviceList(EnvDevice envDevice);

    List<EnvDevice> selectEnvDevicePointJoinList(EnvDevice envDevice);

    /**
     * 新增设备基础信息
     *
     * @param envDevice 设备基础信息
     * @return 结果
     */
    public int insertEnvDevice(EnvDevice envDevice);

    /**
     * 批量新增设备基础信息
     *
     * @param envDevice 设备基础信息
     * @return 结果
     */
    public int batchEnvDevice(List<EnvDevice> envDeviceList);

    /**
     * 修改设备基础信息
     *
     * @param envDevice 设备基础信息
     * @return 结果
     */
    public int updateEnvDevice(EnvDevice envDevice);

    /**
     * 批量删除设备基础信息
     *
     * @param deviceIds 需要删除的设备基础信息主键集合
     * @return 结果
     */
    public int deleteEnvDeviceByDeviceIds(Long[] deviceIds);

    /**
     * 删除设备基础信息信息
     *
     * @param deviceId 设备基础信息主键
     * @return 结果
     */
    public int deleteEnvDeviceByDeviceId(Long deviceId);

    List<EnvDevice> selectEnvDeviceListByDTO(EnvDeviceDTO envDeviceDTO);

    List<EnvDevice> selectDevicePointByDTO(EnvDeviceDTO envDeviceDTO);

    void batchUpdateEnvDevice(EnvDevice envDevice);

    List<SysDictData> selectScreenTree(String scene, boolean queryNum, boolean queryDevice, boolean querySub, EnvDevice deviceItem);

    List<SysDictData> selectSubType(String scene, boolean equals, boolean equals1, EnvDevice deviceItem);

    List<SysDictData> selectScreenList(String scene, Integer status, Boolean queryNum, Boolean queryDevice, EnvDevice deviceItem);

    List<Map<String, Object>> runStateCount(String scene, String mainType, String subType);

    void toTop(Long deviceId, Boolean top);

    void sortDeviceList(List<EnvDevice> deviceSortList);

    Map<String, Object> baseBindAll(List<Long> deviceIds);

    void bind(Long deviceId, List<String> pointIds);

    void export(HttpServletResponse response, EnvDevice envDevice, Boolean template);
}
