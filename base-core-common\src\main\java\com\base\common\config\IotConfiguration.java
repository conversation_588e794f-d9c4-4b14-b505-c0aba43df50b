package com.base.common.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.isession.util.Version;
import org.apache.iotdb.session.pool.SessionPool;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

import javax.annotation.Resource;

@Slf4j
@Configuration
@AutoConfigureOrder(Ordered.HIGHEST_PRECEDENCE)
public class IotConfiguration {

    @Resource
    private IotProperties iotProperties;


    @Bean(name = "IotSessionPool")
    public SessionPool getSessionPool() {
        return new SessionPool.Builder()
                .maxSize(iotProperties.getMaxSize())
                .host(iotProperties.getHost())
                .port(iotProperties.getPort())
                .user(iotProperties.getUser())
                .password(iotProperties.getPassword())
                .connectionTimeoutInMs(iotProperties.getConnectionTimeoutInMs())
                .version(Version.V_0_13)
                .build();
    }

}
