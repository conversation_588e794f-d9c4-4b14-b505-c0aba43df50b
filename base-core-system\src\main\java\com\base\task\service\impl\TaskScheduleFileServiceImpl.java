package com.base.task.service.impl;

import com.base.common.utils.DateUtils;
import com.base.task.domain.TaskScheduleFile;
import com.base.task.mapper.TaskScheduleFileMapper;
import com.base.task.service.ITaskScheduleFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 任务进度文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Service
public class TaskScheduleFileServiceImpl implements ITaskScheduleFileService {
    @Autowired
    private TaskScheduleFileMapper taskScheduleFileMapper;

    /**
     * 查询任务进度文件
     *
     * @param id 任务进度文件主键
     * @return 任务进度文件
     */
    @Override
    public TaskScheduleFile selectTaskScheduleFileById(Long id) {
        return taskScheduleFileMapper.selectTaskScheduleFileById(id);
    }

    /**
     * 查询任务进度文件列表
     *
     * @param taskScheduleFile 任务进度文件
     * @return 任务进度文件
     */
    @Override
    public List<TaskScheduleFile> selectTaskScheduleFileList(TaskScheduleFile taskScheduleFile) {
        return taskScheduleFileMapper.selectTaskScheduleFileList(taskScheduleFile);
    }

    /**
     * 新增任务进度文件
     *
     * @param taskScheduleFile 任务进度文件
     * @return 结果
     */
    @Override
    public int insertTaskScheduleFile(TaskScheduleFile taskScheduleFile) {
        taskScheduleFile.setCreateTime(DateUtils.getNowDate());
        return taskScheduleFileMapper.insertTaskScheduleFile(taskScheduleFile);
    }

    /**
     * 修改任务进度文件
     *
     * @param taskScheduleFile 任务进度文件
     * @return 结果
     */
    @Override
    public int updateTaskScheduleFile(TaskScheduleFile taskScheduleFile) {
        return taskScheduleFileMapper.updateTaskScheduleFile(taskScheduleFile);
    }

    /**
     * 批量删除任务进度文件
     *
     * @param ids 需要删除的任务进度文件主键
     * @return 结果
     */
    @Override
    public int deleteTaskScheduleFileByIds(Long[] ids) {
        return taskScheduleFileMapper.deleteTaskScheduleFileByIds(ids);
    }

    /**
     * 删除任务进度文件信息
     *
     * @param id 任务进度文件主键
     * @return 结果
     */
    @Override
    public int deleteTaskScheduleFileById(Long id) {
        return taskScheduleFileMapper.deleteTaskScheduleFileById(id);
    }
}
