package com.base.dex.service;

import com.base.dex.domain.DevBasePoint;

import java.util.List;

/**
 * 设备点Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IDevBasePointService
{
    /**
     * 查询设备点
     *
     * @param pointId 设备点主键
     * @return 设备点
     */
    public DevBasePoint selectDevBasePointByPointId(String pointId);

    /**
     * 查询设备点列表
     *
     * @param devBasePoint 设备点
     * @return 设备点集合
     */
    public List<DevBasePoint> selectDevBasePointList(DevBasePoint devBasePoint);

    /**
     * 新增设备点
     *
     * @param devBasePoint 设备点
     * @return 结果
     */
    public int insertDevBasePoint(DevBasePoint devBasePoint);

    /**
     * 批量新增设备点
     *
     * @param devBasePoint 设备点
     * @return 结果
     */
    public int batchDevBasePoint(List<DevBasePoint> devBasePointList);

    /**
     * 修改设备点
     *
     * @param devBasePoint 设备点
     * @return 结果
     */
    public int updateDevBasePoint(DevBasePoint devBasePoint);

    /**
     * 批量删除设备点
     *
     * @param pointIds 需要删除的设备点主键集合
     * @return 结果
     */
    public int deleteDevBasePointByPointIds(String[] pointIds);

    /**
     * 删除设备点信息
     *
     * @param pointId 设备点主键
     * @return 结果
     */
    public int deleteDevBasePointByPointId(String pointId);
}
