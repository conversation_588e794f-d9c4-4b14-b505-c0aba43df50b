package com.base.system.service;

import com.base.system.domain.Product;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
public interface IProductInfoService {

    // 获取产品列表
    List<Product> getProductList(String keyword);

    // 根据产品ID获取产品详情
    Product getProductDetailById(int productId);

    List<Product> getProductListByIds(Set<Integer> productIds);

    List<Product> getProductListByIds(Set<Integer> productIds, String enable);

    // 新增方法：标记产品为已读
    void markProductAsRead(int productId);

}
