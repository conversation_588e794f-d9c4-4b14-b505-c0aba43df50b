package com.base.common.utils;

import com.base.common.utils.reflect.ReflectUtils;
import com.base.common.utils.uuid.IdUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TreeUtils {

    public static <T> List<T> buildTree(List<T> dataList, String idKey, String parentIdKey, String childrenKey){
        Map<Object, T> objectTMap = dataList.stream().collect(Collectors.toMap(
                t -> ReflectUtils.invokeGetter(t, idKey),
                Function.identity(),
                (t1, t2) -> t1
        ));

        List<T> rootList = new ArrayList<>();

        for (T t : dataList) {
            Object parentId = ReflectUtils.invokeGetter(t, parentIdKey);
            if (parentId != null) {
                T parent = objectTMap.get(parentId);
                if (parent != null) {
                    List<T> children = (List<T>) ReflectUtils.invokeGetter(parent, childrenKey);
                    if (children == null) {
                        children = new ArrayList<>();
                    }
                    children.add(t);
                    ReflectUtils.invokeSetter(parent, childrenKey, children);
                }
            }
            if (! objectTMap.containsKey(parentId)){
                rootList.add(t);
            }

        }
        return rootList;
    }

    /**
     * 为树形结构数据递归添加 uuid
     *
     * @param dataList      树形数据列表
     * @param childrenField 子节点字段名，默认 "children"
     * @param uuidField     uuid 字段名，默认 "uuid"
     * @param <T>           泛型类型
     * @return 添加 uuid 后的树形数据
     */
    public static <T> List<T> addUuid(List<T> dataList, String childrenField, String uuidField) {
        if (dataList == null || dataList.isEmpty()) {
            return dataList;
        }
        if(StringUtils.isBlank(childrenField)){
            childrenField = "children";
        }
        if (StringUtils.isBlank(uuidField)){
            uuidField = "uuid";
        }

        for (T item : dataList) {
            ReflectUtils.invokeSetter(item, uuidField, IdUtils.fastUUID());

            List<?> children = ReflectUtils.invokeGetter(item, childrenField);
            if (children != null && !children.isEmpty()) {
                // 递归处理子节点
                List<?> newChildren = addUuid(children, childrenField, uuidField);
                ReflectUtils.invokeSetter(item, childrenField, newChildren);
            }
        }

        return dataList;
    }
}
