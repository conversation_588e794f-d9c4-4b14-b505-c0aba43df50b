package com.base;

import com.base.common.http.HttpClientRegistrar;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
@EnableScheduling
@Import(HttpClientRegistrar.class)
@EnableAsync
public class BaseCoreApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(BaseCoreApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  后台管理系统   ლ(´ڡ`ლ)ﾞ  \n" +
                "___________________ \n" +
                "\\_   _____/   __   \\\n" +
                " |    __)_\\____    /\n" +
                " |        \\  /    / \n" +
                "/_______  / /____/  \n" +
                "        \\/          \n" +
                "                    \n");
    }
}
