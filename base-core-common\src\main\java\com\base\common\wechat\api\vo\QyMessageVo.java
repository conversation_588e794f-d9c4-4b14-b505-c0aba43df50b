package com.base.common.wechat.api.vo;


import java.io.Serializable;

/**
 * 企业微信消息
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
public class QyMessageVo implements Serializable {

    /**
     * 企业微信用户id
     */
    private String qyUserId;

    /**
     * 消息标题，如：设备报警通知
     */
    private String title;

    /**
     * 消息内容，多个内容，使用" \n"换行，如：设备编号：京A1111 \n设备名称：京A1111 \n报警内容：排放标准未达标 \n报警时间：2024-11-25 11:57:49
     */
    private String description;

    /**
     * 跳转详情路径，如："http://localhost:28081/env-app/#/pages/views/alarm/alarmdetail?alarm_id=31809b33-edc3-4b57-bbba-4dde492b35e1&alarm_code=alarm_device"
     */
    private String detailUrl;

    public String getQyUserId() {
        return qyUserId;
    }

    public void setQyUserId(String qyUserId) {
        this.qyUserId = qyUserId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    @Override
    public String toString() {
        return "QyMessageVo{" +
                "qyUserId='" + qyUserId + '\'' +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", detailUrl='" + detailUrl + '\'' +
                '}';
    }
}
