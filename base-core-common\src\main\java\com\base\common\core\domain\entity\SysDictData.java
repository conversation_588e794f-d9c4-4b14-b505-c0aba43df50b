package com.base.common.core.domain.entity;

import com.base.common.annotation.Excel;
import com.base.common.annotation.Excel.ColumnType;
import com.base.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 字典数据表 sys_dict_data
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SysDictData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 字典编码
     */
    @Excel(name = "字典编码", cellType = ColumnType.NUMERIC)
    @JsonAlias("dict_code")
    private Long dictCode;

    /**
     * 字典排序
     */
    @Excel(name = "字典排序", cellType = ColumnType.NUMERIC)
    @JsonAlias("dict_sort")
    private Long dictSort;

    /**
     * 字典标签
     */
    @Excel(name = "字典标签")
    @JsonAlias("dict_label")
    @NotBlank(message = "字典标签不能为空")
    @Size(min = 0, max = 100, message = "字典标签长度不能超过100个字符")
    private String dictLabel;

    /**
     * 字典键值
     */
    @Excel(name = "字典键值")
    @JsonAlias("dict_value")
    @NotBlank(message = "字典键值不能为空")
    @Size(min = 0, max = 100, message = "字典键值长度不能超过100个字符")
    private String dictValue;

    /**
     * 字典类型
     */
    @Excel(name = "字典类型")
    @JsonAlias("dict_type")
    @NotBlank(message = "字典类型不能为空")
    @Size(min = 0, max = 100, message = "字典类型长度不能超过100个字符")
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @JsonAlias("css_class")
    private String cssClass;

    /**
     * 表格字典样式
     */
    @JsonAlias("list_class")
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    @Excel(name = "是否默认", readConverterExp = "Y=是,N=否")
    @JsonAlias("is_default")
    private String isDefault;

    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @JsonAlias("status")
    private String status;

    @JsonAlias("join_id")
    private Long joinId;

    @JsonAlias("scene")
    private String scene;

    @JsonAlias("icon")
    private String icon;

    private Integer number;

    private List<Object> children;

    private String uuid;

    private String parentValue;

    private String parentLabel;

    public SysDictData setStatus(String status) {
        if (StringUtils.equals(status, "all")) {
            this.status = null;
        } else {
            this.status = status;
        }
        return this;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("dictCode", getDictCode())
                .append("dictSort", getDictSort())
                .append("dictLabel", getDictLabel())
                .append("dictValue", getDictValue())
                .append("dictType", getDictType())
                .append("cssClass", getCssClass())
                .append("listClass", getListClass())
                .append("isDefault", getIsDefault())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }

}
