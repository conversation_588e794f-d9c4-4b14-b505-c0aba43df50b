package com.base.dex.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class PointDataCache {

    /**
     * 时间
     */
    private Date time;

    /**
     * 点位ID
     */
    private String pointId;

    /**
     * 点位原始值
     */
    private Double sourceValue;

    /**
     * 处理后的数值
     */
    private Double value;

    /**
     * 数据有效性标识
     */
    private String validFlag;

    /**
     * 点位标识
     */
    private String pointFlag;

    /**
     * 显示值
     */
    private String viewValue;

    /**
     * 点位编号
     */
    private String pointCode;

    /**
     * 点位中文名称
     */
    private String pointCodeCn;

    /**
     * 数据过期标识
     */
    private Boolean expireFlag;

    /**
     * 数据过期时间
     */
    private Date expireTime;
}
