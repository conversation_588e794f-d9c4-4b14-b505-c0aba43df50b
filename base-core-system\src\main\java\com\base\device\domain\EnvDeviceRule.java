
package com.base.device.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 设备运行规则(运行状态、报警等判断逻辑)对象 env_device_rule
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public class EnvDeviceRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设备id  对应env_basic_device表中id */
    @Excel(name = "设备id  对应env_basic_device表中id")
    private Long deviceId;

    /** 规则 */
    @Excel(name = "规则")
    private String rule;

    /** 符合规则对应的结果, 给到device表中的run_state, 并且关联字典 */
    @Excel(name = "符合规则对应的结果, 给到device表中的run_state, 并且关联字典")
    private String result;

    /** 持续时长, 即持续多久后才算报警,单位为秒  0及立即报警 */
    @Excel(name = "持续时长, 即持续多久后才算报警,单位为秒  0及立即报警")
    private Long runTime;

    /** 优先级, 值越大, 越优先判断 */
    @Excel(name = "优先级, 值越大, 越优先判断")
    private Long level;

    /** 是否报警 */
    @Excel(name = "是否报警")
    private Long warn;

    /** 是否改变运行状态 */
    @Excel(name = "是否改变运行状态")
    private Long run;

    /** $column.columnComment */
    private Long ruleId;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("deviceId", getDeviceId())
            .append("rule", getRule())
            .append("result", getResult())
            .append("runTime", getRunTime())
            .append("level", getLevel())
            .append("warn", getWarn())
            .append("run", getRun())
            .append("ruleId", getRuleId())
            .toString();
    }
}
