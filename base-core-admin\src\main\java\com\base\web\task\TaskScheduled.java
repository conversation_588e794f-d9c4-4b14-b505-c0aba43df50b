package com.base.web.task;

import com.base.task.service.ITaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @date 2024-09-02 10:03
 */
@Component
public class TaskScheduled {
    private static final Logger logger = LoggerFactory.getLogger(TaskScheduled.class);

    @Autowired
    private ITaskService taskService;

    /**
     * 每天9点生成排期任务
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void task() {
        logger.info("==========> 每天9点生成排期任务(cron = \"0 0 9 * * ?\")启动...");
        try {
            // 生成类型：0系统自动执行，1人工手动执行
            taskService.getGenTask(0);
        } catch (Exception e) {
            logger.error("==========> 每天9点生成排期任务出错", e);
        }
    }

}
