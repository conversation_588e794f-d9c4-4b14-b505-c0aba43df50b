package com.base.device.mapper;

import com.base.device.domain.EnvDevicePoint;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备-点位 关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Repository
public interface EnvDevicePointMapper
{
    /**
     * 查询设备-点位 关联关系
     *
     * @param joinId 设备-点位 关联关系主键
     * @return 设备-点位 关联关系
     */
    public EnvDevicePoint selectEnvDevicePointByJoinId(Long joinId);

    /**
     * 查询设备-点位 关联关系列表
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 设备-点位 关联关系集合
     */
    public List<EnvDevicePoint> selectEnvDevicePointList(EnvDevicePoint envDevicePoint);

    /**
     * 新增设备-点位 关联关系
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 结果
     */
    public int insertEnvDevicePoint(EnvDevicePoint envDevicePoint);

    /**
     * 修改设备-点位 关联关系
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 结果
     */
    public int updateEnvDevicePoint(EnvDevicePoint envDevicePoint);

    /**
     * 删除设备-点位 关联关系
     *
     * @param joinId 设备-点位 关联关系主键
     * @return 结果
     */
    public int deleteEnvDevicePointByJoinId(Long joinId);

    /**
     * 批量删除设备-点位 关联关系
     *
     * @param joinIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEnvDevicePointByJoinIds(Long[] joinIds);

    /**
     * 批量新增设备-点位 关联关系
     *
     * @param envDevicePointList 设备-点位 关联关系列表
     * @return 结果
     */
    public int batchEnvDevicePoint(List<EnvDevicePoint> envDevicePointList);
}
