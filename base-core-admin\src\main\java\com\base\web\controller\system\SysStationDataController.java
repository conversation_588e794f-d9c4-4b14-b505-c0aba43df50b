package com.base.web.controller.system;

import com.alibaba.fastjson2.JSONArray;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.DateUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.system.domain.SysStationData;
import com.base.system.service.ISysStationDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 国控站数据Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/sys_station_data")
public class SysStationDataController extends BaseController
{
    @Autowired
    private ISysStationDataService sysStationDataService;

    /**
     * 查询国控站数据列表
     */
    @PreAuthorize("@ss.hasPermi('sys:station:data:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysStationData sysStationData){
        startPage();
        List<SysStationData> sysStationDataList = sysStationDataService.selectSysStationDataList(sysStationData);
        return getDataTable(sysStationDataList);
    }

    /**
     * 导出国控站数据列表
     */
    @PreAuthorize("@ss.hasPermi('sys:station:data:export')")
    @Log(title = "国控站数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysStationData sysStationData)
    {
        List<SysStationData> list = sysStationDataService.selectSysStationDataList(sysStationData);
        ExcelUtil<SysStationData> util = new ExcelUtil<SysStationData>(SysStationData.class);
        util.exportExcel(response, list, "国控站数据数据");
    }

    /**
     * 获取国控站数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('sys:station:data:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") int id)
    {
        return success(sysStationDataService.selectSysStationDataById(id));
    }

    /**
     * 新增国控站数据
     */
    @PreAuthorize("@ss.hasPermi('sys:station:data:add')")
    @Log(title = "国控站数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody SysStationData sysStationData)
    {
        return toAjax(sysStationDataService.insertSysStationData(sysStationData));
    }

    /**
     * 修改国控站数据
     */
    @PreAuthorize("@ss.hasPermi('sys:station:data:edit')")
    @Log(title = "国控站数据", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody SysStationData sysStationData)
    {
        return toAjax(sysStationDataService.updateSysStationData(sysStationData));
    }

    /**
     * 删除国控站数据
     */
    @PreAuthorize("@ss.hasPermi('sys:station:data:remove')")
    @Log(title = "国控站数据", businessType = BusinessType.DELETE)
	@GetMapping("/delete/{ids}")
    public AjaxResult remove(@PathVariable int[] ids)
    {
        return toAjax(sysStationDataService.deleteSysStationDataByIds(ids));
    }

    /**
     * 国控站列表数据
     */
    @PreAuthorize("@ss.hasPermi('sys:station:data:list')")
    @GetMapping("/station_list")
    public AjaxResultSnake sysStationDataList(@RequestParam(name = "query_time", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date queryTime) {
        JSONArray jsonArray;
        if (StringUtils.isNotNull(queryTime)){
            jsonArray = sysStationDataService.getStationData(queryTime);
        }else{
            jsonArray = sysStationDataService.getStationData();
        }
        return AjaxResultSnake.success(jsonArray);
    }

    @GetMapping("/station_chart")
    public AjaxResultSnake sysStationDataChart(@RequestParam(name = "start_time", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime) {
        if (StringUtils.isNull(startTime)){
            startTime = DateUtils.clearToDay(DateUtils.addDays(DateUtils.getNowDate(), -6));
        }
        JSONArray jsonArray = sysStationDataService.getStationDataChart(startTime);
        return AjaxResultSnake.success(jsonArray);
    }
}
