package com.base.framework.aspectj;

import com.base.common.annotation.UseRedis;
import com.base.common.enums.RedisIndex;
import com.base.common.config.RedisTemplateContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class RedisTemplateAspect {


    private final RedisTemplateContext redisTemplateContext;

    public RedisTemplateAspect(RedisTemplateContext redisTemplateContext) {
        this.redisTemplateContext = redisTemplateContext;
    }

    @Around("@annotation(useRedis)")
    public Object around(ProceedingJoinPoint joinPoint, UseRedis useRedis) throws Throwable {
        RedisIndex dataSource = useRedis.value();
        try {
            redisTemplateContext.setRedisTemplate(dataSource);
            return joinPoint.proceed();
        } finally {
            redisTemplateContext.clear();
        }
    }

}
