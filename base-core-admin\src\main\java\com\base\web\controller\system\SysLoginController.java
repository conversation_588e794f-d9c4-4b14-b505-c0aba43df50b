package com.base.web.controller.system;

import com.base.common.config.CasConfig;
import com.base.common.config.BaseConfig;
import com.base.common.config.OAuth2Config;
import com.base.common.constant.Constants;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.entity.SysMenu;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.core.domain.model.LoginBody;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.sign.RsaUtils;
import com.base.framework.web.service.SysLoginService;
import com.base.framework.web.service.SysPermissionService;
import com.base.system.service.ISysMenuService;
import com.base.system.task.WeatherTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.crypto.BadPaddingException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "登录管理")
@RestController
public class SysLoginController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private BaseConfig BaseConfig;

    @Autowired
    WeatherTask weatherTask;
    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) throws Exception {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        try {
            String token = loginService.login(loginBody.getUsername(), RsaUtils.decryptByPrivateKey(loginBody.getPassword()), loginBody.getCode(),
                    loginBody.getUuid());
            ajax.put(Constants.TOKEN, token);
            return ajax;
        } catch (BadPaddingException ignored) {
            return AjaxResult.error("密码解密错误");
        }
    }

    @GetMapping("/cas/login")
    public AjaxResult casLogin(String ticket) throws UnsupportedEncodingException {
        AjaxResult ajax = AjaxResult.success();
        String token;
        try {
            token = loginService.casLogin(ticket);
        } catch (Exception e) {
            return AjaxResult.error("登录失败", StringUtils.format("{}?service={}", CasConfig.getServerLoginUrl(), URLEncoder.encode(CasConfig.getServiceUrl(), StandardCharsets.UTF_8.toString())));
        }
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    @GetMapping("/sso/login")
    public AjaxResult ssoLogin(String code) throws UnsupportedEncodingException {
        AjaxResult ajax = AjaxResult.success();
        String sso = BaseConfig.getSSO();
        if (StringUtils.isBlank(sso)) {
            return AjaxResult.error("未配置单点登录");
        }
        String token = "";
        if (StringUtils.equals(sso, "cas")) {
            try {
                token = loginService.casLogin(code);
            } catch (Exception e) {
                return AjaxResult.error("登录失败", StringUtils.format("{}?service={}", CasConfig.getServerLoginUrl(), URLEncoder.encode(CasConfig.getServiceUrl(), StandardCharsets.UTF_8.toString())));
            }
        } else if (StringUtils.equals(sso, "oauth2")) {
            try {
                token = loginService.oauth2Login(code);
            } catch (Exception e) {
                return AjaxResult.error("登录失败", OAuth2Config.getServerLoginUrl() + URLEncoder.encode(OAuth2Config.getServiceUrl(), StandardCharsets.UTF_8.toString()));
            }

        } else {
            return AjaxResult.error("不存在的登录方式");
        }
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters(@RequestParam(required = false, defaultValue = "1") String sceneType, @RequestParam(required = false, defaultValue = "0") Long productId) {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId, sceneType, productId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    @ApiOperation("验证用户信息")
    @PostMapping("/validateUser")
    public AjaxResult validateUser(@RequestBody LoginBody loginBody) {
        try {
            boolean result = loginService.validateUser(loginBody.getUsername(), loginBody.getPassword(), loginBody.getOpenId());
            if (result) {
                return AjaxResult.success("验证成功");
            } else {
                return AjaxResult.success("验证失败");
            }
        } catch (Exception e) {
            logger.error("=====> 验证用户信息出错", e);
            return AjaxResult.error("验证失败");
        }
    }
}
