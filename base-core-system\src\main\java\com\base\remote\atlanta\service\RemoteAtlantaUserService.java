package com.base.remote.atlanta.service;

import com.base.remote.atlanta.domain.AtlantaUserDTO;
import com.base.common.annotation.EnableApiClient;
import com.base.common.annotation.HttpClient;
import com.base.common.enums.ApiServices;
import com.base.common.enums.HttpMethod;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@EnableApiClient(serviceName = ApiServices.ATLANTA)
public interface RemoteAtlantaUserService {

    @HttpClient(path = "/data_sync_manage/person_sync_all",method = HttpMethod.POST)
    void syncUser(@RequestBody List<AtlantaUserDTO> atlantaUserDTOList);

}
