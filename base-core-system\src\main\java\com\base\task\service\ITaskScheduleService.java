package com.base.task.service;

import com.base.task.domain.TaskSchedule;

import java.util.List;

/**
 * 任务进度Service接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public interface ITaskScheduleService {
    /**
     * 查询任务进度
     *
     * @param id 任务进度主键
     * @return 任务进度
     */
    public TaskSchedule selectTaskScheduleById(Long id);

    /**
     * 查询任务进度列表
     *
     * @param taskSchedule 任务进度
     * @return 任务进度集合
     */
    public List<TaskSchedule> selectTaskScheduleList(TaskSchedule taskSchedule);

    /**
     * 新增任务进度
     *
     * @param taskSchedule 任务进度
     * @return 结果
     */
    public int insertTaskSchedule(TaskSchedule taskSchedule);

    /**
     * 修改任务进度
     *
     * @param taskSchedule 任务进度
     * @return 结果
     */
    public int updateTaskSchedule(TaskSchedule taskSchedule);

    /**
     * 批量删除任务进度
     *
     * @param ids 需要删除的任务进度主键集合
     * @return 结果
     */
    public int deleteTaskScheduleByIds(Long[] ids);

    /**
     * 删除任务进度信息
     *
     * @param id 任务进度主键
     * @return 结果
     */
    public int deleteTaskScheduleById(Long id);
}
