package com.base.device.domain.dto;

import com.base.common.exception.ServiceException;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
public class EnvDevicePointBindDTO {

    /**
     * 设备ID
     */
    @NotNull(message = "要绑定的设备不能为空")
    private Long deviceId;

    /**
     * 点位ID
     */
    @NotNull(message = "要绑定的点位不能为空")
    private Object pointId;

    private List<String> pointIdList;

    public EnvDevicePointBindDTO setPointId(Object pointId){
        List<String> pointIds = new ArrayList<>();
        if (pointId instanceof String) {
            pointIds.add((String) pointId);
        } else if (pointId instanceof List<?>) {
            List<String> list = (List<String>) pointId;
            pointIds.addAll(list);
        } else {
            throw new ServiceException("点位id错误");
        }
        return this;
    }
}
