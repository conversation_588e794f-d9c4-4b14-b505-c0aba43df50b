package com.base.remote.atlanta.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户数据传输对象
 */
@Data
@ApiModel(description = "用户数据传输对象")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AtlantaUserDTO {

    /**
     * 人员账号
     */
    @ApiModelProperty(value = "人员账号")
    private String personId;

    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String personName;

    /**
     * 人员账号（冗余字段）
     */
    @ApiModelProperty(value = "人员账号（冗余）")
    private String userName;

    /**
     * 微信 openid
     */
    @ApiModelProperty(value = "微信 openid")
    private String openId;

    /**
     * 企业微信用户 ID
     */
    @ApiModelProperty(value = "企业微信用户ID")
    private String qyUserId;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phonenumber;

    /**
     * 所属部门 ID
     */
    @ApiModelProperty(value = "所属部门ID")
    private Long departmentId;

    /**
     * 所属部门名称
     */
    @ApiModelProperty(value = "所属部门中文名称")
    private String departmentName;

    /**
     * 是否负责人（0-否，1-是）
     */
    @ApiModelProperty(value = "是否负责人，0-否，1-是")
    private Integer chargeFlag;

}
