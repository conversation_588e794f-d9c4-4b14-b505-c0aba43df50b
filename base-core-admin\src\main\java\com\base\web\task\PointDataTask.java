package com.base.web.task;

import com.base.common.utils.DateUtils;
import com.base.common.utils.StringUtils;
import com.base.device.service.IEnvDevicePointService;
import com.base.device.service.IEnvDeviceService;
import com.base.dex.domain.DevBasePoint;
import com.base.dex.domain.DevDataAll;
import com.base.dex.service.IDevBasePointService;
import com.base.dex.service.IDevDataAllService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class PointDataTask {

    @Autowired
    private IEnvDeviceService deviceService;

    @Autowired
    private IDevBasePointService pointService;

    @Autowired
    private IEnvDevicePointService devicePointService;

    @Autowired
    private IDevDataAllService devDataAllService;


    public void pointMinDataTask(){
        Date monitorTime = DateUtils.clearToMinute(DateUtils.addMinutes(DateUtils.getNowDate(), -1));
        this.pointMinDataTask(monitorTime);
    }

    public void pointMinDataTask(Date monitorTime){
        Date endTime = DateUtils.addSeconds(monitorTime, 59);
        List<DevDataAll> devDataAllList = devDataAllService.selectByTimeBetween(monitorTime, endTime);
        if (StringUtils.isEmpty(devDataAllList)){
            return;
        }

        Map<String, List<DevDataAll>> devDataMap = devDataAllList.stream().collect(Collectors.groupingBy(DevDataAll::getPointId));
        List<DevBasePoint> devBasePointList = pointService.selectDevBasePointList(new DevBasePoint());

    }
}
