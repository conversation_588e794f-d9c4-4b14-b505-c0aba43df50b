package com.base.task.service.impl;

import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StringUtils;
import com.base.task.domain.Task;
import com.base.task.domain.TaskSchedule;
import com.base.task.domain.TaskScheduleFile;
import com.base.task.mapper.TaskScheduleMapper;
import com.base.task.service.ITaskScheduleFileService;
import com.base.task.service.ITaskScheduleService;
import com.base.task.service.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 任务进度Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Service
public class TaskScheduleServiceImpl implements ITaskScheduleService {
    @Autowired
    private TaskScheduleMapper taskScheduleMapper;

    @Autowired
    private ITaskService taskService;

    @Autowired
    private ITaskScheduleFileService taskScheduleFileService;


    /**
     * 查询任务进度
     *
     * @param id 任务进度主键
     * @return 任务进度
     */
    @Override
    public TaskSchedule selectTaskScheduleById(Long id) {
        return taskScheduleMapper.selectTaskScheduleById(id);
    }

    /**
     * 查询任务进度列表
     *
     * @param taskSchedule 任务进度
     * @return 任务进度
     */
    @Override
    public List<TaskSchedule> selectTaskScheduleList(TaskSchedule taskSchedule) {
        return taskScheduleMapper.selectTaskScheduleList(taskSchedule);
    }

    /**
     * 新增任务进度
     *
     * @param taskSchedule 任务进度
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertTaskSchedule(TaskSchedule taskSchedule) {
        taskSchedule.setCreateBy(SecurityUtils.getUsername());
        taskSchedule.setCreateTime(DateUtils.getNowDate());
        int result = taskScheduleMapper.insertTaskSchedule(taskSchedule);
        // 任务进度文件
        if (StringUtils.isNotEmpty(taskSchedule.getTaskScheduleFileList())) {
            for (TaskScheduleFile item : taskSchedule.getTaskScheduleFileList()) {
                if (StringUtils.isEmpty(item.getFilePath())) {
                    continue;
                }
                item.setTaskScheduleId(taskSchedule.getId());
                String fileSuffix = item.getFilePath().substring(item.getFilePath().lastIndexOf(".") + 1);
                item.setFileSuffix(fileSuffix);
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                taskScheduleFileService.insertTaskScheduleFile(item);
            }
        }
        // 修改任务为1已完成，2未解决的也属于任务完成
        boolean b = null != taskSchedule.getStatus() && (1 == taskSchedule.getStatus() || 2 == taskSchedule.getStatus());
        if (b) {
            Task task = new Task();
            task.setId(taskSchedule.getTaskId());
            // 任务完成状态：0进行中 1已完成
            task.setStatus(1);
            taskService.updateTask(task);
        }
        return result;
    }

    /**
     * 修改任务进度
     *
     * @param taskSchedule 任务进度
     * @return 结果
     */
    @Override
    public int updateTaskSchedule(TaskSchedule taskSchedule) {
        return taskScheduleMapper.updateTaskSchedule(taskSchedule);
    }

    /**
     * 批量删除任务进度
     *
     * @param ids 需要删除的任务进度主键
     * @return 结果
     */
    @Override
    public int deleteTaskScheduleByIds(Long[] ids) {
        return taskScheduleMapper.deleteTaskScheduleByIds(ids);
    }

    /**
     * 删除任务进度信息
     *
     * @param id 任务进度主键
     * @return 结果
     */
    @Override
    public int deleteTaskScheduleById(Long id) {
        return taskScheduleMapper.deleteTaskScheduleById(id);
    }
}
