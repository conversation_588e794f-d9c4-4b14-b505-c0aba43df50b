package com.base.device.service.impl;

import com.base.common.core.domain.entity.FastExcelDTO;
import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.domain.entity.SysSceneDict;
import com.base.common.exception.ServiceException;
import com.base.common.utils.*;
import com.base.common.utils.spring.SpringUtils;
import com.base.device.domain.EnvDevice;
import com.base.device.domain.EnvDevicePoint;
import com.base.device.domain.dto.EnvDeviceDTO;
import com.base.device.domain.dto.EnvDeviceRunStateCountDTO;
import com.base.device.mapper.EnvDeviceMapper;
import com.base.device.service.IEnvDevicePointService;
import com.base.device.service.IEnvDeviceService;
import com.base.dex.domain.DevBasePoint;
import com.base.dex.service.IDevBasePointService;
import com.base.system.domain.SysField;
import com.base.system.domain.SysProcess;
import com.base.system.mapper.SysFieldMapper;
import com.base.system.service.ISysFieldService;
import com.base.system.service.ISysProcessService;
import com.base.system.service.ISysSceneDictService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Service
public class EnvDeviceServiceImpl implements IEnvDeviceService
{
    @Autowired
    private EnvDeviceMapper envDeviceMapper;

    @Autowired
    private ISysProcessService sysProcessService;

    @Autowired
    private SysFieldMapper sysFieldMapper;

    @Autowired
    private IEnvDevicePointService envDevicePointService;

    @Autowired
    private ISysSceneDictService sceneDictService;

    @Autowired
    private IDevBasePointService devBasePointService;

    public static final String DEVICE_CACHE_KEY =  "EnvDeviceCache:";

    @Autowired
    private ISysFieldService sysFieldService;

    /**
     * 查询设备基础信息
     *
     * @param deviceId 设备基础信息主键
     * @return 设备基础信息
     */
    @Override
    public EnvDevice selectEnvDeviceByDeviceId(Long deviceId)
    {
        return envDeviceMapper.selectEnvDeviceByDeviceId(deviceId);
    }

    /**
     * 查询设备基础信息列表
     *
     * @param envDevice 设备基础信息
     * @return 设备基础信息
     */
    @Override
    public List<EnvDevice> selectEnvDeviceList(EnvDevice envDevice)
    {
        envDevice = this.initEnvDeviceQuery(envDevice);
        return envDeviceMapper.selectEnvDeviceList(envDevice);
    }

    /**
     * 查询设备基础信息列表
     *
     * @param envDevice 设备基础信息
     * @return 设备基础信息
     */
    @Override
    public List<EnvDevice> selectEnvDevicePointJoinList(EnvDevice envDevice)
    {
        envDevice = this.initEnvDeviceQuery(envDevice);
        List<EnvDevice> envDeviceList = envDeviceMapper.selectEnvDevicePointJoinList(envDevice);
        Map<String, String> deviceMainType = DictUtils.getDictValueLabelMap("device_main_type");
        Map<String, String> deviceSubType = DictUtils.getDictValueLabelMap("device_sub_type");
        envDeviceList.forEach(device -> {
            device.getFieldJson().put("f052e567ae2111efb5c29146dae5fc83", device.getPointCount());
            device.getFieldJson().put("18fab887ae2211efb5c79146dae5fc83", device.getPointNames());
//            device.setMainType(deviceMainType.get(device.getMainType()));
//            device.setSubType(deviceSubType.get(device.getSubType()));
        });
        return envDeviceList;
    }

    /**
     * 新增设备基础信息
     *
     * @param envDevice 设备基础信息
     * @return 结果
     */
    @Override
    public int insertEnvDevice(EnvDevice envDevice)
    {
        SysSceneDict sysSceneDict = sceneDictService.selectChild("device_main_type", "device_sub_type", envDevice.getSubType());
        envDevice.setMainType(sysSceneDict.getScene());
        envDevice.setFieldKey("device_" + envDevice.getSubType());
        envDevice.setCreateBy(SecurityUtils.getUsername());
        envDevice.setCreateTime(DateUtils.getNowDate());
        return envDeviceMapper.insertEnvDevice(envDevice);
    }

    @Override
    public int batchEnvDevice(List<EnvDevice> envDeviceList)
    {
        if (StringUtils.isNotEmpty(envDeviceList)){
            return envDeviceMapper.batchEnvDevice(envDeviceList);
        }
        return 0;
    }

    /**
     * 修改设备基础信息
     *
     * @param envDevice 设备基础信息
     * @return 结果
     */
    @Override
    public int updateEnvDevice(EnvDevice envDevice)
    {
        envDevice.setUpdateTime(DateUtils.getNowDate());
        envDevice.setUpdateBy(SecurityUtils.getUsername());
        if (StringUtils.isNotBlank(envDevice.getSubType())){
            SysSceneDict sysSceneDict = sceneDictService.selectChild("device_main_type", "device_sub_type", envDevice.getSubType());
            envDevice.setMainType(sysSceneDict.getScene());
            envDevice.setFieldKey("device_" + envDevice.getSubType());
        }
        return envDeviceMapper.updateEnvDevice(envDevice);
    }

    /**
     * 批量删除设备基础信息
     *
     * @param deviceIds 需要删除的设备基础信息主键
     * @return 结果
     */
    @Override
    public int deleteEnvDeviceByDeviceIds(Long[] deviceIds)
    {
        return envDeviceMapper.deleteEnvDeviceByDeviceIds(deviceIds);
    }

    /**
     * 删除设备基础信息信息
     *
     * @param deviceId 设备基础信息主键
     * @return 结果
     */
    @Override
    public int deleteEnvDeviceByDeviceId(Long deviceId)
    {
        return envDeviceMapper.deleteEnvDeviceByDeviceId(deviceId);
    }

    public EnvDevice initEnvDeviceQuery(EnvDevice envDevice){

        if (StringUtils.isBlank(envDevice.getSubType()) && StringUtils.isEmpty(envDevice.getSubTypeList())){
            // 处理设备的子类查询条件
            List<String> mainTypeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(envDevice.getMainTypeList())){
                mainTypeList = envDevice.getMainTypeList();
            } else if (StringUtils.isNotBlank(envDevice.getMainType())) {
                mainTypeList = Collections.singletonList(envDevice.getMainType());
            } else if (StringUtils.isNotBlank(envDevice.getScene())) {
                List<SysDictData> deviceMainType = DictUtils.selectDictData("device_main_type", envDevice.getScene());
                mainTypeList = deviceMainType.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
            }
            if (StringUtils.isNotEmpty(mainTypeList)){
                List<String> subTypeList = new ArrayList<>();
                for (String mainType : mainTypeList) {
                    List<SysDictData> deviceSubType = DictUtils.selectDictData("device_sub_type", mainType);
                    subTypeList.addAll(deviceSubType.stream().map(SysDictData::getDictValue).collect(Collectors.toList()));
                }
                envDevice.setSubTypeList(subTypeList);
            }
        }

        if (StringUtils.isNotNull(envDevice.getProcessId())){
            // 查询所属区域时, 需要考虑其所属区域的父级区域
            List<SysProcess> sysProcessList = sysProcessService.selectChildren(envDevice.getProcessId());
            List<Long> processIdList = new ArrayList<>(Collections.singletonList(envDevice.getProcessId()));
            if (StringUtils.isNotEmpty(sysProcessList)){
                processIdList.addAll(sysProcessList.stream().map(SysProcess::getProcessId).collect(Collectors.toList()));
            }
            envDevice.setProcessIdList(processIdList);
        }

        // 构造排序字段
        if (StringUtils.isBlank(envDevice.getOrderByField())){
            envDevice.setOrderByField("sort");
        } else if (StringUtils.contains(envDevice.getOrderByField(), ".")) {
            List<SysField> sysFieldList = sysFieldMapper.selectSysFieldList(new SysField().setFieldValue(envDevice.getOrderByField()));
            if (StringUtils.isNotEmpty(sysFieldList)) {
                envDevice.setFieldScene(sysFieldList.get(0).getFieldScene());
            }
            String[] parts = envDevice.getOrderByField().split("\\.");
            if (parts.length == 2) {
                envDevice.setOrderByField(StringUtils.format(parts[1]));
            }
        }

        // 构造 ORDER BY 子句
        StringBuilder orderByClause = new StringBuilder();
        orderByClause.append("order by field_json ->> 'to_top' asc, ");

        envDevice.setOrderBySort(StringUtils.isBlank(envDevice.getOrderBySort()) ? "asc" : envDevice.getOrderBySort());
        if (StringUtils.equals(envDevice.getFieldScene(), "dex_point")) {
            // 判断是否是因子排序, 因子排序需要进行类型转换, 转成float后再进行排序

            orderByClause.append(StringUtils.format(
                    "CASE WHEN {} ~ '^[0-9]+(\\\\.[0-9]+)?$' THEN ({})::float ELSE NULL END ",
                    envDevice.getOrderByField(), envDevice.getOrderByField()
            ));
        }else {
            orderByClause.append(envDevice.getOrderByField());
        }
        orderByClause.append(StringUtils.format(" {}", envDevice.getOrderBySort()));
        envDevice.setOrderByField(orderByClause.toString());
        return envDevice;
    }

    @Override
    public List<EnvDevice> selectEnvDeviceListByDTO(EnvDeviceDTO envDeviceDTO) {
        if (StringUtils.isNull(envDeviceDTO.getDeviceItem())){
            envDeviceDTO.setDeviceItem(new EnvDevice());
        }
        return this.selectEnvDeviceList(envDeviceDTO.getDeviceItem());
    }


    @Override
    public List<EnvDevice> selectDevicePointByDTO(EnvDeviceDTO envDeviceDTO) {
        if (StringUtils.isNull(envDeviceDTO.getDeviceItem())){
            envDeviceDTO.setDeviceItem(new EnvDevice());
        }
        return this.selectEnvDevicePointJoinList(envDeviceDTO.getDeviceItem());
    }

    @Override
    public void batchUpdateEnvDevice(EnvDevice envDevice){
        envDevice.setUpdateTime(DateUtils.getNowDate());
        envDevice.setUpdateBy(SecurityUtils.getUsername());
        this.envDeviceMapper.batchUpdateEnvDevice(envDevice);
    }


    @Override
    public List<SysDictData> selectScreenTree(String scene, boolean queryNum, boolean queryDevice, boolean querySub, EnvDevice deviceItem) {
        List<SysDictData> result = new ArrayList<>();
        // Step 1: 查询主类（device_main_type）
        List<SysDictData> mainTypeList = DictUtils.selectDictData("device_main_type", scene);
        if (mainTypeList.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 2: 查询所有子类（device_sub_type），以主类为 key
        Map<String, List<SysDictData>> subTypeMap = new HashMap<>();
        for (SysDictData mainType : mainTypeList) {
            List<SysDictData> subTypes = DictUtils.selectDictData("device_sub_type", mainType.getDictValue());
            subTypeMap.put(mainType.getDictValue(), subTypes);
        }

        // Step 3: 查询设备列表（如果需要统计或展示设备）
        List<EnvDevice> deviceList = new ArrayList<>();
        Map<String, List<EnvDevice>> deviceMainDict = new HashMap<>();
        if (queryNum || queryDevice) {
            if (deviceItem == null) {
                deviceItem = new EnvDevice(); // 默认空对象
            }
            deviceList = selectEnvDeviceList(deviceItem); // 查询设备
            deviceMainDict = deviceList.stream()
                    .collect(Collectors.groupingBy(EnvDevice::getMainType));
        }

        // Step 4: 构建最终树结构

        for (SysDictData mainType : mainTypeList) {
            List<Object> children = new ArrayList<>();
            List<EnvDevice> mainDevices = deviceMainDict.getOrDefault(mainType.getDictValue(), Collections.emptyList());
            mainType.setNumber(mainDevices.size());

            if (querySub) {
                // 三级联动：主类 -> 子类 -> 设备
                List<SysDictData> subTypes = subTypeMap.getOrDefault(mainType.getDictValue(), Collections.emptyList());
                Map<String, List<EnvDevice>> deviceSubDict = mainDevices.stream()
                        .collect(Collectors.groupingBy(EnvDevice::getSubType));

                int totalNumber = 0;

                for (SysDictData subType : subTypes) {
                    subType.setDictValue(subType.getDictValue());
                    subType.setDictLabel(subType.getDictLabel());

                    if (queryNum) {
                        List<EnvDevice> devices = deviceSubDict.getOrDefault(subType.getDictValue(), Collections.emptyList());
                        subType.setNumber(devices.size());
                        totalNumber += devices.size();
                    }

                    if (queryDevice) {
                        subType.setChildren(new ArrayList<>(deviceSubDict.getOrDefault(subType.getDictValue(), Collections.emptyList())));
                    }

                    children.add(subType);
                }

                mainType.setNumber(totalNumber);
                mainType.setChildren(children);
            } else {
                // 二级联动：主类 -> 设备
                mainType.setChildren(new ArrayList<>(mainDevices));
            }

            result.add(mainType);
        }

        return result;
    }

    @Override
    public List<SysDictData> selectSubType(String scene, boolean queryNum, boolean queryDevice, EnvDevice deviceItem) {
        // Step 1: 查询子类字典数据
        List<SysDictData> children = DictUtils.selectDictData("device_sub_type", scene);
        if (children.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 2: 提取所有子类 dict_value
        List<String> subTypeList = children.stream()
                .map(SysDictData::getDictValue)
                .collect(Collectors.toList());

        // Step 3: 如果需要查询数量或设备，则查询设备列表
        Map<String, List<EnvDevice>> mainDict = new HashMap<>();
        if (queryNum || queryDevice) {
            if (deviceItem == null) {
                deviceItem = new EnvDevice(); // 默认空对象
            }
            deviceItem.setSubTypeList(subTypeList); // 设置 sub_type_list

            List<EnvDevice> mainList = selectEnvDeviceList(deviceItem); // 查询设备列表
            // 按 sub_type 分组
            mainDict = mainList.stream()
                    .collect(Collectors.groupingBy(EnvDevice::getSubType));
        }

        // Step 4: 遍历子类设置 number 和 children（设备）
        for (SysDictData child : children) {
            String subType = child.getDictValue();

            if (queryNum) {
                List<EnvDevice> devices = mainDict.getOrDefault(subType, Collections.emptyList());
                int count = devices.size();
                child.setNumber(count);
            }

            if (queryDevice) {
                child.setChildren(new ArrayList<>(mainDict.getOrDefault(subType, Collections.emptyList())));
            }
        }

        // Step 5: 返回最终子类列表
        return children;
    }

    @Override
    public List<SysDictData> selectScreenList(String scene, Integer status, Boolean queryNum, Boolean queryDevice, EnvDevice deviceItem) {
        List<SysDictData> resultList = new ArrayList<>();

        // Step 1: 查询主类（device_main_type）
        List<SysDictData> mainTypeList = DictUtils.selectDictData("device_main_type", scene, status == null ? null : status.toString());
        if (mainTypeList.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 2: 提取所有主类 dict_value
        List<String> mainValueList = mainTypeList.stream()
                .map(SysDictData::getDictValue)
                .collect(Collectors.toList());

        // Step 3: 查询子类，并按主类分组
        Map<String, List<SysDictData>> subTypeMap = new HashMap<>();
        for (String mainValue : mainValueList) {
            List<SysDictData> subTypes = DictUtils.selectDictData("device_sub_type", mainValue, status == null ? null : status.toString());
            subTypeMap.put(mainValue, subTypes);
        }

        // Step 4: 查询设备列表（如果需要统计或展示设备）
        if (deviceItem == null) {
            deviceItem = new EnvDevice(); // 默认空对象
        }
        deviceItem.setMainTypeList(mainValueList); // 设置主类过滤条件

        List<EnvDevice> deviceList = new ArrayList<>();
        Map<String, List<EnvDevice>> deviceSubDict = new HashMap<>();

        if (queryNum || queryDevice) {
            deviceList = selectEnvDeviceList(deviceItem); // 查询设备
            deviceSubDict = deviceList.stream()
                    .collect(Collectors.groupingBy(EnvDevice::getSubType));
        }

        // Step 5: 构建最终结果列表
        for (SysDictData mainType : mainTypeList) {
            String mainValue = mainType.getDictValue();
            List<SysDictData> childrenList = subTypeMap.getOrDefault(mainValue, Collections.emptyList());

            for (SysDictData child : childrenList) {
                // 设置 parent_label & parent_value
                child.setParentLabel(mainType.getDictLabel());
                child.setParentValue(mainValue);

                // 设置数量
                if (queryNum) {
                    int count = deviceSubDict.getOrDefault(child.getDictValue(), Collections.emptyList()).size();
                    child.setNumber(count);
                }

                // 设置子节点（设备）
                if (queryDevice) {
                    child.setChildren(new ArrayList<>(deviceSubDict.getOrDefault(child.getDictValue(), Collections.emptyList())));
                }

                resultList.add(child);
            }
        }

        return resultList;
    }

    @Override
    public List<Map<String, Object>> runStateCount(String scene, String mainType, String subType) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> totalMap = new HashMap<>();
        totalMap.put("label", "总数");
        totalMap.put("key", "");
        totalMap.put("value", 0);
        totalMap.put("css", "#00D9D9");
        result.add(totalMap);

        int sumCount = 0;

        if (StringUtils.isBlank(mainType) && StringUtils.isBlank(subType)){
            throw new ServiceException("设备类别和设备类型禁止都为空");
        }

        // Step 1: 获取所有运行状态和当前场景下的有效状态
        List<SysDictData> runStateList = DictUtils.selectDictData("device_run_state", mainType);

        // Step 2: 构造过滤条件
        List<String> subTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(subType)) {
            subTypeList.add(subType);
        } else{
            List<String> mainList = new ArrayList<>();
            if (StringUtils.isNotBlank(mainType)) {
                mainList.add(mainType);
            } else {
                Map<String, SysDictData> mainTypeDict = DictUtils.selectDictData("device_main_type", scene)
                        .stream().collect(Collectors.toMap(SysDictData::getDictValue, Function.identity(), (t1, t2) -> t2));
                mainList.addAll(mainTypeDict.keySet());
            }

            // 获取子类列表
            subTypeList = DictUtils.selectDictData("device_sub_type", mainList).stream()
                    .map(SysDictData::getDictValue)
                    .collect(Collectors.toList());
        }

        // Step 3: 查询统计
        List<EnvDeviceRunStateCountDTO> countList = envDeviceMapper.countByRunState(subTypeList);
        Map<String, Integer> countMap = countList.stream()
                .collect(Collectors.toMap(
                        EnvDeviceRunStateCountDTO::getRunState,
                        EnvDeviceRunStateCountDTO::getNumber));



        // Step 4: 填充结果
        for (SysDictData runState : runStateList) {
            int count = countMap.getOrDefault(runState.getDictValue(), 0);
            Map<String, Object> item = new HashMap<>();
            item.put("label", runState.getDictLabel());
            item.put("key", runState.getDictValue());
            item.put("value", count);
            item.put("css", runState.getCssClass());
            result.add(item);

            sumCount += count;
        }

        // 填充总数
        IntSummaryStatistics collect = countList.stream().collect(Collectors.summarizingInt(EnvDeviceRunStateCountDTO::getNumber));
        result.get(0).put("value", collect.getSum());

        return result;
    }

    @Override
    public void toTop(Long deviceId, Boolean top) {
        if (BooleanUtils.isTrue(top)){
            // 置顶设备
            Map<String, Object> resultMap = this.envDeviceMapper.queryMinTop();
            if (StringUtils.isNotNull(resultMap.get("minSort"))) {
                int minSort = Integer.parseInt(resultMap.get("minSort").toString()) - 1;
                EnvDevice envDevice = this.selectEnvDeviceByDeviceId(deviceId);
                envDevice.getFieldJson().put("to_top", minSort);
                this.updateEnvDevice(envDevice);
            }
        }else {
            // 取消置顶
            this.envDeviceMapper.clearTop(deviceId);
        }
    }

    @Override
    public void sortDeviceList(List<EnvDevice> deviceSortList) {
        if (StringUtils.isEmpty(deviceSortList)){
            return;
        }
        // 提取排序值并排序
        List<Integer> sortList = deviceSortList.stream()
                .map(device -> Integer.parseInt(String.valueOf(device.getSort())))
                .sorted().collect(Collectors.toList());

        // 更新每个设备的排序值
        for (int i = 0; i < deviceSortList.size(); i++) {
            EnvDevice envDevice = deviceSortList.get(i);
            Long deviceId = envDevice.getDeviceId();
            Integer sortedValue = sortList.get(i);

            envDevice.setDeviceId(deviceId);
            envDevice.setSort(Long.valueOf(sortedValue));
            this.updateEnvDevice(envDevice); // 假设 updateEnvDevice 方法支持部分字段更新
        }
    }

    @Override
    public Map<String, Object> baseBindAll(List<Long> deviceIds) {
        Map<String, Object> response = new HashMap<>();
        List<EnvDevice> successList = new ArrayList<>();
        List<EnvDevice> errorList = new ArrayList<>();
        response.put("success_list", successList);
        response.put("error_list", errorList);

        if (StringUtils.isEmpty(deviceIds)){
            return response;
        }
        List<EnvDevicePoint> joinList = new ArrayList<>();

        // 查询出设备
        List<EnvDevice> envDeviceList = this.selectEnvDeviceList(new EnvDevice().setDeviceIdList(deviceIds));
        // 查询出点位
        List<DevBasePoint> devBasePoints = devBasePointService.selectDevBasePointList(new DevBasePoint());
        // 转换好类型
        Map<String, List<DevBasePoint>> pointMap = devBasePoints.stream().collect(Collectors.groupingBy(DevBasePoint::getRemark));

        for (EnvDevice device : envDeviceList) {
            List<DevBasePoint> pointList = pointMap.get(device.getDeviceName());
            if (pointList != null && !pointList.isEmpty()) {
                for (DevBasePoint point : pointList) {
                    joinList.add(new EnvDevicePoint().setDeviceId(device.getDeviceId())
                            .setPointId(point.getPointId()).setStatus(0L).setDefaultFlag(1L));
                }
                device.setPointList(pointList);
                successList.add(device);
            } else {
                device.setMsg("未找到关联因子");
                errorList.add(device);
            }
        }

        if (!joinList.isEmpty()) {
            envDevicePointService.batchEnvDevicePoint(joinList);
        }

        if (!successList.isEmpty()) {
            EnvDeviceServiceImpl bean = SpringUtils.getBean(EnvDeviceServiceImpl.class);
            bean.devicePointToFieldTask(); // 假设这是一个已定义的方法
        }

        return response;
    }

    @Override
    public void bind(Long deviceId, List<String> pointIds) {
        List<EnvDevicePoint> joinList = new ArrayList<>();
        for (String pointId : pointIds) {
            EnvDevicePoint envDevicePoint = new EnvDevicePoint();
            envDevicePoint.setDeviceId(deviceId);
            envDevicePoint.setPointId(pointId);
            envDevicePoint.setStatus(0L); // 默认状态
            envDevicePoint.setDefaultFlag(1L); // 默认值标记
            joinList.add(envDevicePoint);
        }

        int res = envDevicePointService.batchEnvDevicePoint(joinList);
        EnvDeviceServiceImpl bean = SpringUtils.getBean(EnvDeviceServiceImpl.class);
        bean.devicePointToFieldTask(); // 假设这是一个已定义的方法
    }

    @Override
    public void export(HttpServletResponse response, EnvDevice envDevice, Boolean template) {
        String fileName;
        List<String> fieldKeyList = new ArrayList<>();
        if (StringUtils.isNotBlank(envDevice.getSubType())){
            fileName = DictUtils.getDictLabel("device_sub_type", envDevice.getSubType());
            fieldKeyList.add("device_" + envDevice.getSubType());
        }else {
            fileName = DictUtils.getDictLabel("device_main_type", envDevice.getMainType());
            List<SysDictData> deviceSubType = DictUtils.selectDictData("device_sub_type", envDevice.getMainType());
            for (SysDictData sysDictData : deviceSubType) {
                fieldKeyList.add("device_" + sysDictData.getDictValue());
            }
        }
        if (StringUtils.isNotBlank(envDevice.getFieldKey())){
            fieldKeyList = Collections.singletonList(envDevice.getFieldKey());
            envDevice.setFieldKey(null);
        }
        List<EnvDevice> envDeviceList = new ArrayList<>();
        if(BooleanUtils.isNotTrue(template)){
            envDeviceList = this.selectEnvDeviceList(envDevice);
        }
        FastExcelDTO fastExcelDTO = sysFieldService.exportByField(response, envDeviceList, fieldKeyList);
        fastExcelDTO.setFileName(fileName + DateUtils.getDate("yyyy_MM_dd_HH_mm")).setSheetName(fileName);
        FastExcelUtils.exportExcel(response, fastExcelDTO);
    }

    @Async("threadPoolTaskExecutor")
    public void devicePointToFieldTask() {
        // 同步动态字段到动态字段表

    }

}
