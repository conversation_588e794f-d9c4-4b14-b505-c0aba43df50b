package com.base.web.controller.api;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.utils.StringUtils;
import com.base.dex.domain.DevDataPointMin;
import com.base.dex.service.IDevDataPointMinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 设备点位分钟数据 Controller
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping(value = "/api/device/dataPoint")
@Api(tags = "设备点位分钟值数据")
public class DevDataPointMinController extends BaseController {

    @Autowired
    private IDevDataPointMinService devDataPointMinService;

    @GetMapping("/getMinDataChart")
    @ApiOperation("异常报警详情-折线图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deviceId", required = true, value = "设备id", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "pointId", required = true, value = "点位id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "alarmTime", required = true, value = "报警时间，格式：yyyy-MM-dd HH:mm:ss", dataType = "String", paramType = "query"),
    })
    public AjaxResult getMinDataChart(Long deviceId, String pointId, String alarmTime) {
        try {
            JSONArray result = new JSONArray();
            // 只有设备报警有图表展示
            if (deviceId != null && StringUtils.isNotEmpty(pointId) && StringUtils.isNotEmpty(alarmTime)) {
                DevDataPointMin devDataPoint = new DevDataPointMin();
                devDataPoint.setDeviceId(deviceId);
                devDataPoint.setMonitorTime(DateUtil.parseDateTime(alarmTime));
                devDataPoint.setPointId(pointId);
                List<DevDataPointMin> devDataPointMinList = devDataPointMinService.getDevDataPointMinList(devDataPoint);

                devDataPointMinList.forEach(item -> {
                    JSONObject obj = new JSONObject();
                    obj.put("factor", item.getFactor());
                    obj.put("value", item.getValue());
                    obj.put("monitorTime", item.getMonitorTime() != null ? DateUtil.formatDateTime(item.getMonitorTime()) : null);
                    result.add(obj);
                });
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("=====> 查询异常报警详情-折线图出错", e);
            return AjaxResult.error("异常报警详情-折线图出错");
        }
    }

}
