package com.base.env.domain;

import com.base.common.core.domain.BaseEntity;
import com.base.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 企业厂界范围坐标配置对象 env_data_point
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
public class EnvDataPoint extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Integer id;

    /**
     * 类型，区分多个范围坐标
     */
    @Excel(name = "类型，区分多个范围坐标")
    private Integer type;

    /**
     * x坐标
     */
    @Excel(name = "x坐标")
    private String posX;

    /**
     * y坐标
     */
    @Excel(name = "y坐标")
    private String posY;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setPosX(String posX) {
        this.posX = posX;
    }

    public String getPosX() {
        return posX;
    }

    public void setPosY(String posY) {
        this.posY = posY;
    }

    public String getPosY() {
        return posY;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("type", getType())
                .append("posX", getPosX())
                .append("posY", getPosY())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
