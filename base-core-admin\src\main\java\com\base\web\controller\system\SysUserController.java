package com.base.web.controller.system;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.annotation.Log;
import com.base.common.constant.CacheConstants;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.domain.entity.SysDept;
import com.base.common.core.domain.entity.SysRole;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.core.domain.model.LoginUser;
import com.base.common.core.page.TableDataInfo;
import com.base.common.core.redis.RedisCache;
import com.base.common.enums.BusinessType;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.common.wechat.api.WechatApi;
import com.base.system.service.ISysDeptService;
import com.base.system.service.ISysPostService;
import com.base.system.service.ISysRoleService;
import com.base.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private WechatApi wechatApi;

    /**
     * 部门树列表
     */
    @GetMapping("/getDeptTree")
    @ApiOperation("部门树列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "部门名称", required = false, dataType = "String", paramType = "query"),
    })
    public AjaxResult getDeptTree(String name) {
        SysDept dept = new SysDept();
        dept.setDeptName(name);
        return success(deptService.selectDeptTreeList(dept));
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/getUserList")
    @ApiOperation("用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "用户名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "phoneNumber", value = "手机号", required = false, dataType = "String", paramType = "query"),
    })
    public AjaxResult getUserList(Long deptId, String userName, String phoneNumber) {
        SysUser user = new SysUser();
        user.setDeptId(deptId);
        user.setUserName(userName);
        user.setPhonenumber(phoneNumber);
        List<SysUser> list = userService.selectUserList(user);
        JSONArray result = new JSONArray();
        for (SysUser item : list) {
            // 过滤admin
            if (item.getUserId().equals(1)) {
                continue;
            }
            JSONObject obj = new JSONObject();
            obj.put("deptId", item.getDeptId());
            obj.put("userId", item.getUserId().toString());
            obj.put("userName", item.getUserName());
            obj.put("nickName", item.getNickName());
            obj.put("phoneNumber", item.getPhonenumber());
            result.add(obj);
        }
        return success(result);
    }

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if (!userService.checkUserNameUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        int res = userService.resetPwd(user);
        if (res > 0) {
            // 修改成功则强退用户
            Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
            for (String key : keys) {
                LoginUser loginUser = redisCache.getCacheObject(key);
                if (loginUser.getUserId().equals(user.getUserId())) {
                    redisCache.deleteObject(key);
                }
            }
        }
        return toAjax(res);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取部门树列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDept dept) {
        return success(deptService.selectDeptTreeList(dept));
    }

    /**
     * 手动同步企业微信userId
     */
    @GetMapping("/getSynQyWechatUserId")
    @ApiOperation("手动同步企业微信userId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phoneNumber", value = "手机号", required = false, dataType = "String", paramType = "query"),
    })
    public AjaxResult getSynQyWechatUserId(String phoneNumber) {
        SysUser user = new SysUser();
        user.setPhonenumber(phoneNumber);
        List<SysUser> list = userService.selectUserList(user);
        int result = 0;
        for (SysUser item : list) {
            if (StringUtils.isNotEmpty(item.getPhonenumber())) {
                String userId = wechatApi.getUserId(item.getPhonenumber());
                if (StringUtils.isNotEmpty(userId)) {
                    SysUser sysUser = new SysUser();
                    sysUser.setUserId(item.getUserId());
                    sysUser.setQyUserId(userId);
                    result += userService.updateUserOpenId(sysUser);
                }
            }
        }
        return success(result);
    }

    @GetMapping("/sync/all")
    @ApiOperation("同步用户信息到报警服务")
    public AjaxResult syncAtlantaUser() {
        return AjaxResult.success(userService.syncAtlantaUser());
    }


    @GetMapping("dept/tree")
    public AjaxResultSnake deptUserTree() {
        List<SysUser> sysUserList = userService.selectUserList(new SysUser().setStatus("0"));
        List<SysDept> sysDepts = deptService.buildDeptTree(deptService.selectAllDeptList());

        // Step 2: 将每个用户挂到其所属部门的 children 中
        for (SysUser user : sysUserList) {
            SysDept dept = deptService.fromTreeFindDeptById(sysDepts, user.getDeptId());
            if (dept != null) {
                dept.getChildren().add(new SysDept().setDeptName(user.getNickName()).setUserName(user.getUserName()).setParentId(dept.getDeptId()).setAncestors(dept.getAncestors() + "," + dept.getDeptId()));
            }
        }

        // Step 3: 递归清理没有子节点的部门
        List<SysDept> filtered = deptService.filterDept(sysDepts);
        return AjaxResultSnake.success(filtered);
    }
}
