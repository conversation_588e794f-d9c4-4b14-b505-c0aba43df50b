<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.SysBuildMapper">

    <!-- 添加 resultMap 定义 -->
    <resultMap id="SysBuildResult" type="com.base.system.domain.SysBuild">
        <id property="buildId" column="build_id" />
        <result property="buildName" column="build_name" />
        <result property="posX" column="pos_x" />
        <result property="posY" column="pos_y" />
        <result property="posZ" column="pos_z" />
        <result property="sort" column="sort" />
        <result property="createTime" column="create_time" />
        <result property="createBy" column="create_by" />
        <result property="updateTime" column="update_time" />
        <result property="updateBy" column="update_by" />
        <result property="delFlag" column="del_flag" />
        <result property="remark" column="remark" />
    </resultMap>

    <!-- 根据ID获取建筑物信息 -->
    <select id="getById" resultMap="SysBuildResult">
        SELECT * FROM sys_build WHERE build_id = #{buildId}
    </select>

    <!-- 根据条件查询建筑物列表 -->
    <select id="selectList" resultMap="SysBuildResult">
        SELECT * FROM sys_build
        <where>
            <if test="buildName != null and buildName != ''">
                AND build_name LIKE CONCAT('%', #{buildName}, '%')
            </if>
            <if test="delFlag == null">
                AND del_flag = 0
            </if>
        </where>
        ORDER BY sort ASC
    </select>

    <!-- 插入新建筑物 -->
    <insert id="insert" parameterType="com.base.system.domain.SysBuild" useGeneratedKeys="true" keyProperty="buildId">
        INSERT INTO sys_build (
            <if test="buildName != null and buildName != ''">build_name,</if>
            <if test="posX != null">pos_x,</if>
            <if test="posY != null">pos_y,</if>
            <if test="posZ != null">pos_z,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        ) VALUES (
            <if test="buildName != null and buildName != ''">#{buildName},</if>
            <if test="posX != null">#{posX},</if>
            <if test="posY != null">#{posY},</if>
            <if test="posZ != null">#{posZ},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            current_timestamp
        )
    </insert>

    <!-- 更新建筑物信息 -->
    <update id="update" parameterType="com.base.system.domain.SysBuild">
        UPDATE sys_build
        <set>
            <if test="buildName != null and buildName != ''">build_name = #{buildName},</if>
            <if test="posX != null">pos_x = #{posX},</if>
            <if test="posY != null">pos_y = #{posY},</if>
            <if test="posZ != null">pos_z = #{posZ},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE build_id = #{buildId}
    </update>

    <!-- 根据ID删除建筑物 -->
    <update id="deleteById">
        UPDATE sys_build SET del_flag = '2' WHERE build_id = #{buildId}
    </update>

</mapper>