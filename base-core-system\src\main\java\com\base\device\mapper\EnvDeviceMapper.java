package com.base.device.mapper;

import com.base.device.domain.EnvDevice;
import com.base.device.domain.dto.EnvDeviceRunStateCountDTO;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 设备基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Repository
public interface EnvDeviceMapper
{
    /**
     * 查询设备基础信息
     *
     * @param deviceId 设备基础信息主键
     * @return 设备基础信息
     */
    public EnvDevice selectEnvDeviceByDeviceId(Long deviceId);

    /**
     * 查询设备基础信息列表
     *
     * @param envDevice 设备基础信息
     * @return 设备基础信息集合
     */
    public List<EnvDevice> selectEnvDeviceList(EnvDevice envDevice);

    /**
     * 新增设备基础信息
     *
     * @param envDevice 设备基础信息
     * @return 结果
     */
    public int insertEnvDevice(EnvDevice envDevice);

    /**
     * 修改设备基础信息
     *
     * @param envDevice 设备基础信息
     * @return 结果
     */
    public int updateEnvDevice(EnvDevice envDevice);

    /**
     * 删除设备基础信息
     *
     * @param deviceId 设备基础信息主键
     * @return 结果
     */
    public int deleteEnvDeviceByDeviceId(Long deviceId);

    /**
     * 批量删除设备基础信息
     *
     * @param deviceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEnvDeviceByDeviceIds(Long[] deviceIds);

    /**
     * 批量新增设备基础信息
     *
     * @param envDeviceList 设备基础信息列表
     * @return 结果
     */
    public int batchEnvDevice(List<EnvDevice> envDeviceList);

    List<EnvDevice> selectEnvDevicePointJoinList(EnvDevice envDevice);

    void batchUpdateEnvDevice(EnvDevice envDevice);

    List<EnvDeviceRunStateCountDTO> countByRunState(List<String> subTypeList);

    Map<String, Object> queryMinTop();

    void clearTop(@Param("deviceId")Long deviceId);
}
