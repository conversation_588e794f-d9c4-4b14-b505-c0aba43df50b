<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.dex.mapper.DevDataAllMapper">

    <resultMap type="DevDataAll" id="DevDataAllResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="pointCode"    column="point_code"    />
        <result property="pointAddress"    column="point_address"    />
        <result property="pointCnName"    column="point_cn_name"    />
        <result property="pointValue"    column="point_value"    />
        <result property="pointQuality"    column="point_quality"    />
        <result property="monitorTime"    column="monitor_time"    />
        <result property="pointId"    column="point_id"    />
        <result property="formulaValue"    column="formula_value"    />
    </resultMap>

    <sql id="selectDevDataAllVo">
        select id, device_id, point_code, point_address, point_cn_name, point_value, point_quality, monitor_time, point_id, formula_value from dev_data_all
    </sql>

    <select id="selectDevDataAllList" parameterType="DevDataAll" resultMap="DevDataAllResult">
        <include refid="selectDevDataAllVo"/>
        <where>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="pointCode != null  and pointCode != ''"> and point_code = #{pointCode}</if>
            <if test="pointAddress != null  and pointAddress != ''"> and point_address = #{pointAddress}</if>
            <if test="pointCnName != null  and pointCnName != ''"> and point_cn_name like concat('%', #{pointCnName}, '%')</if>
            <if test="pointValue != null  and pointValue != ''"> and point_value = #{pointValue}</if>
            <if test="pointQuality != null  and pointQuality != ''"> and point_quality = #{pointQuality}</if>
            <if test="formulaValue != null  and formulaValue != ''"> and formula_value = #{formulaValue}</if>
        </where>
    </select>

    <select id="selectDevDataAllByMonitorTime" parameterType="String" resultMap="DevDataAllResult">
        <include refid="selectDevDataAllVo"/>
        where monitor_time = #{monitorTime}
    </select>

    <select id="selectByTimeBetween" resultMap="DevDataAllResult">
        <include refid="selectDevDataAllVo"/>
        where monitor_time between #{startTime} and #{endTime}
    </select>

    <insert id="insertDevDataAll" parameterType="DevDataAll">
        insert into dev_data_all
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="pointCode != null">point_code,</if>
            <if test="pointAddress != null">point_address,</if>
            <if test="pointCnName != null">point_cn_name,</if>
            <if test="pointValue != null">point_value,</if>
            <if test="pointQuality != null">point_quality,</if>
            <if test="monitorTime != null">monitor_time,</if>
            <if test="pointId != null">point_id,</if>
            <if test="formulaValue != null">formula_value,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="pointCode != null">#{pointCode},</if>
            <if test="pointAddress != null">#{pointAddress},</if>
            <if test="pointCnName != null">#{pointCnName},</if>
            <if test="pointValue != null">#{pointValue},</if>
            <if test="pointQuality != null">#{pointQuality},</if>
            <if test="monitorTime != null">#{monitorTime},</if>
            <if test="pointId != null">#{pointId},</if>
            <if test="formulaValue != null">#{formulaValue},</if>
         </trim>
    </insert>

    <update id="updateDevDataAll" parameterType="DevDataAll">
        update dev_data_all
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null and id != ''">id = #{id},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="pointCode != null">point_code = #{pointCode},</if>
            <if test="pointAddress != null">point_address = #{pointAddress},</if>
            <if test="pointCnName != null">point_cn_name = #{pointCnName},</if>
            <if test="pointValue != null">point_value = #{pointValue},</if>
            <if test="pointQuality != null">point_quality = #{pointQuality},</if>
            <if test="pointId != null">point_id = #{pointId},</if>
            <if test="formulaValue != null">formula_value = #{formulaValue},</if>
        </trim>
        where monitor_time = #{monitorTime}
    </update>

    <delete id="deleteDevDataAllByMonitorTime" parameterType="String">
        delete from dev_data_all where monitor_time = #{monitorTime}
    </delete>

    <delete id="deleteDevDataAllByMonitorTimes" parameterType="String">
        delete from dev_data_all where monitor_time in
        <foreach item="monitorTime" collection="array" open="(" separator="," close=")">
            #{monitorTime}
        </foreach>
    </delete>

    <insert id="batchDevDataAll">
        insert into dev_data_all( id, device_id, point_code, point_address, point_cn_name, point_value, point_quality, monitor_time, point_id, formula_value) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.deviceId}, #{item.pointCode}, #{item.pointAddress}, #{item.pointCnName}, #{item.pointValue}, #{item.pointQuality}, #{item.monitorTime}, #{item.pointId}, #{item.formulaValue})
        </foreach>
    </insert>
</mapper>
