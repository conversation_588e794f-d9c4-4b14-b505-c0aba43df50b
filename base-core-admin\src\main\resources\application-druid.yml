# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: org.postgresql.Driver
        druid:
            # 主库数据源
            master:
                url: **********************************************************************************************************************************
                username: postgres
                password: 6x9XrDGndyJsJ4R
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url:
                username:
                password:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT version()
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: e9
                login-password: ALJzRTCzxDztwy
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    # redis 配置
    redis:
        base:
            host: ************
            port: 6379
            database: 3
            password: CaxuS8DkCBfYUsU
            timeout: 10s
            lettuce:
                pool:
                    max-active: 8
                    max-idle: 8
                    min-idle: 0
                    max-wait: -1ms

        # dex 数据源
        dex:
            host: ************
            port: 6379
            database: 1
            password: CaxuS8DkCBfYUsU
            timeout: 10s
            lettuce:
                pool:
                    max-active: 8
                    max-idle: 8
                    min-idle: 0
                    max-wait: -1ms

        # atlanta 数据源
        atlanta:
            host: ************
            port: 6379
            database: 2
            password: CaxuS8DkCBfYUsU
            timeout: 10s
            lettuce:
                pool:
                    max-active: 8
                    max-idle: 8
                    min-idle: 0
                    max-wait: -1ms
    iot:
        enabled: true
        host: ************
        port: 6667
        user: root
        password: newpwd
        database: root.env
        connectionTimeoutInMs: 3000
        maxSize: 1000

    mqtt:
        enabled: false
        host: tcp://************:1883
        #    clientId: local-base-core-service
        clientId:
        options:
            userName:
            password:
            # 这里表示会话不过期  false为不过期, true为过期
            cleanSession: true
            # 配置一个默认的主题，加载时不会用到，只能在需要时手动提取
            defaultTopic: devops
            timeout: 1000
            KeepAliveInterval: 30
            #断线重连方式，自动重新连接与会话不过期配合使用会导致
            #断线重新连接后会接收到断线期间的消息。需要更改设置请看password联系我
            automaticReconnect: true
            connectionTimeout: 3000
            # 最大链接数
            maxInflight: 100

# 企业微信配置信息
qywechat:
    # 企业ID，超低测试平台
    corpId: wwbfefd260b4651a56
    # 应用ID
    agentId: 1000006
    # 应用的凭证密钥
    corpSecret: MkD_fuVfNgGyN9tEmf0bdKvB5OUAMWUC3ut763vSo3U
    # 企业微信AccessToken获取方式：1、密钥，2、接口，默认为1
    accessTokenMode: 1
    # 企业微信AccessToken-接口地址
    accessTokenUrl: http://10.21.3.47:8080/jeecg-boot/qiyeweixinaccesstoken/qiyeweixinAccesstoken/getaccessToken?id=1625430350404395009
