<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.task.mapper.TaskScheduleMapper">
    
    <resultMap type="TaskSchedule" id="TaskScheduleResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="status"    column="status"    />
        <result property="userName"    column="nick_name"    />
        <result property="phoneNumber"    column="phonenumber"    />
        <result property="deptName"    column="dept_name"    />
    </resultMap>

    <sql id="selectTaskScheduleVo">
        select a.id, a.task_id, a.description, a.create_by, a.create_time, a.remark, a.del_flag, a.status, u.nick_name, u.phonenumber, d.dept_name
        from public.task_schedule a
        left join public.sys_user u on u.user_name = a.create_by
        left join public.sys_dept d on d.dept_id = u.dept_id
    </sql>

    <select id="selectTaskScheduleList" parameterType="TaskSchedule" resultMap="TaskScheduleResult">
        <include refid="selectTaskScheduleVo"/>
        <where>  
            <if test="taskId != null "> and a.task_id = #{taskId}</if>
            <if test="description != null  and description != ''"> and a.description = #{description}</if>
        </where>
        order by a.create_time desc
    </select>
    
    <select id="selectTaskScheduleById" parameterType="Long" resultMap="TaskScheduleResult">
        <include refid="selectTaskScheduleVo"/>
        where a.id = #{id}
    </select>
        
    <insert id="insertTaskSchedule" parameterType="TaskSchedule" useGeneratedKeys="true" keyProperty="id">
        insert into task_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateTaskSchedule" parameterType="TaskSchedule">
        update task_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskScheduleById" parameterType="Long">
        delete from task_schedule where id = #{id}
    </delete>

    <delete id="deleteTaskScheduleByIds" parameterType="String">
        delete from task_schedule where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>