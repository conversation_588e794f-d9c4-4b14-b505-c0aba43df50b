package com.base.system.service;

import com.base.system.domain.SysProcess;

import java.util.List;
import java.util.Map;

/**
 * 区域信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
public interface ISysProcessService
{
    /**
     * 查询区域信息
     *
     * @param processId 区域信息主键
     * @return 区域信息
     */
    public SysProcess selectSysProcessByProcessId(Long processId);

    /**
     * 查询区域信息列表
     *
     * @param sysProcess 区域信息
     * @return 区域信息集合
     */
    public List<SysProcess> selectSysProcessList(SysProcess sysProcess);

    /**
     * 新增区域信息
     *
     * @param sysProcess 区域信息
     * @return 结果
     */
    public int insertSysProcess(SysProcess sysProcess);

    /**
     * 修改区域信息
     *
     * @param sysProcess 区域信息
     * @return 结果
     */
    public int updateSysProcess(SysProcess sysProcess);

    /**
     * 批量删除区域信息
     *
     * @param processIds 需要删除的区域信息主键集合
     * @return 结果
     */
    public int deleteSysProcessByProcessIds(Long[] processIds);

    /**
     * 删除区域信息信息
     *
     * @param processId 区域信息主键
     * @return 结果
     */
    public int deleteSysProcessByProcessId(Long processId);

    /**
     * 根据区域ID获取祖先名称
     *
     * @param processId 区域ID
     * @return 祖先名称
     */
    String getAncestorsName(Long processId);

    /**
     * 获取所有祖先名称列表
     *
     * @return 祖先名称列表
     */
    List<SysProcess> getListAncestorsName();

    /**
     * 根据区域项选择区域树
     *
     * @param processItem 区域项
     * @return 区域树
     */
    List<SysProcess> selectSysProcessTree(SysProcess processItem);

    /**
     * 清空相关数据或状态
     */
    void clear();

    /**
     * 根据祖先ID选择子区域列表
     *
     * @param ancestorId 祖先ID
     * @return 子区域列表
     */
    List<SysProcess> selectChildren(Long ancestorId);

    /**
     * 选择所有区域
     *
     * @return 所有区域列表
     */
    List<SysProcess> selectAll();

    /**
     * 选择区域的ID映射
     *
     * @return 区域的ID映射
     */
    Map<Long, SysProcess> selectIdMap();

    /**
     * 选择区域的名称映射
     *
     * @return 区域的名称映射
     */
    Map<String, SysProcess> selectNameMap();

    SysProcess selectCacheById(Long processId);
}
