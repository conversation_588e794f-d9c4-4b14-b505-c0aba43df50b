package com.base.common.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 高德地图工具类
 *
 * <AUTHOR>
 * @date 2024-11-04 15:43
 */
public class LbsUtils {

    private static final String API_KEY = "73295e545a7d3981d0acc931827d7e4a";
    private static final String GEOCODE_URL = "https://restapi.amap.com/v3/geocode/regeo";


    /**
     * 根据经纬度坐标，通过高德接口获取行政地址
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 行政地址
     * @throws Exception
     */
    public static String getLocationInfo(double longitude, double latitude) throws Exception {
        // 构造请求URL
        String url = GEOCODE_URL + "?output=json&key=" + API_KEY + "&location=" + longitude + "," + latitude;
        // 发送GET请求
        String responseBody = HttpUtil.get(url);
        // 使用FastJSON2解析JSON响应
        JSONObject jsonObject = JSON.parseObject(responseBody);
        // 检查API调用是否成功
        if (jsonObject != null && "1".equals(jsonObject.getString("status"))) {
            // 获取地址信息
            String address = jsonObject.getJSONObject("regeocode").getString("formatted_address");
            return address;
        } else {
            throw new Exception("请求失败，位置信息: " + jsonObject.getString("info"));
        }
    }

    /**
     * 经纬度转换
     *
     * @param position 坐标
     * @return 经纬度
     */
    public static Double transformPosition(String position) {
        if (StringUtils.isEmpty(position)) {
            return null;
        }
        String d = position.split("°")[0].replace(" ", "");
        String m = position.split("°")[1].split("'")[0].replace(" ", "");
        String s = position.split("°")[1].split("'")[1].replace(" ", "").replace("\"", "");
        // 使用 BigDecimal 进行精确的小数运算
        BigDecimal bd_d = new BigDecimal(d);
        BigDecimal bd_m = new BigDecimal(m);
        BigDecimal bd_s = new BigDecimal(s);
        BigDecimal gps_dou = bd_d.add(bd_m.divide(BigDecimal.valueOf(60), 6, RoundingMode.HALF_UP))
                .add(bd_s.divide(BigDecimal.valueOf(3600), 6, RoundingMode.HALF_UP));
        // 返回保留6位小数的结果
        return gps_dou.setScale(6, RoundingMode.HALF_UP).doubleValue();
    }

    public static void main(String[] args) {
        // 经度
        double longitude = 113.23401;
        // 纬度
        double latitude = 23.076002;
        try {
            String address = getLocationInfo(longitude, latitude);
            System.out.println("地址: " + address);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
