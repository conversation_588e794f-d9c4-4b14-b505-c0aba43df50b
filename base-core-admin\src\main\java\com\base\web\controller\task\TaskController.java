package com.base.web.controller.task;

import com.alibaba.fastjson2.JSONObject;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.task.domain.Task;
import com.base.task.domain.TaskFile;
import com.base.task.domain.TaskSchedule;
import com.base.task.domain.TaskScheduleFile;
import com.base.task.service.ITaskFileService;
import com.base.task.service.ITaskScheduleFileService;
import com.base.task.service.ITaskScheduleService;
import com.base.task.service.ITaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 任务 Controller
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Api(tags = "任务管理")
@RestController
@RequestMapping("/api/task")
public class TaskController extends BaseController {
    @Autowired
    private ITaskService taskService;

    @Autowired
    private ITaskFileService taskFileService;

    @Autowired
    private ITaskScheduleService taskScheduleService;

    @Autowired
    private ITaskScheduleFileService taskScheduleFileService;

    @GetMapping("/getCountTask")
    @ApiOperation("统计进行中、我发起、已完成的数量")
    public AjaxResult getCountTask() {
        JSONObject result = new JSONObject();
        Task task = new Task();
        task.setExecutor(SecurityUtils.getUsername());
        task.setCreateBy(SecurityUtils.getUsername());
        // 1进行中，2我发起的，3已完成的
        task.setType(1);
        Long progressTotal = taskService.getCountTask(task);
        // 1进行中，2我发起的，3已完成的
        task.setType(2);
        Long selfTotal = taskService.getCountTask(task);
        // 1进行中，2我发起的，3已完成的
        task.setType(3);
        Long completionTotal = taskService.getCountTask(task);
        result.put("progressTotal", progressTotal);
        result.put("selfTotal", selfTotal);
        result.put("completionTotal", completionTotal);
        return AjaxResult.success(result);
    }

    /**
     * 查询任务列表
     */
    @GetMapping("/list")
    @ApiOperation("任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "当前页", required = true, defaultValue = "1", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "每页显示数", required = true, defaultValue = "10", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "任务名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "taskTypeId", value = "任务类型id", required = false, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "0全部，1进行中，2我发起的，3已完成的", required = false, defaultValue = "2", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态：2已逾期", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDatetime", value = "截止开始时间，格式：yyyy-MM-dd hh:mm:ss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDatetime", value = "截止结束时间，格式：yyyy-MM-dd hh:mm:ss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "year", value = "年份：yyyy", required = false, dataType = "String", paramType = "query"),
    })
    @PreAuthorize("@ss.hasPermi('task:list')")
    public TableDataInfo list(String name, Long taskTypeId, Integer type, Integer status, String startDatetime, String endDatetime, String year) {
        Task task = new Task();
        task.setName(name);
        task.setTaskTypeId(taskTypeId);
        task.setType(type);
        // 表示逾期的（还在进行中的）
        task.setStatus(status);
        task.setStartDatetime(startDatetime);
        task.setEndDatetime(endDatetime);
        task.setYear(year);
        task.setExecutor(SecurityUtils.getUsername());
        task.setCreateBy(SecurityUtils.getUsername());

        startPage();
        List<Task> list = taskService.selectTaskList(task);

        return getDataTable(list);
    }

    /**
     * 获取任务详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("任务详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        Task task = taskService.selectTaskById(id);
        if (task != null) {
            // 任务文件列表
            TaskFile taskFile = new TaskFile();
            taskFile.setTaskId(id);
            List<TaskFile> taskFileList = taskFileService.selectTaskFileList(taskFile);
            task.setTaskFileList(taskFileList);
            Date currentDate = DateUtils.getNowDate();

            // 未完成的，当前时间大于截止时间的表示为逾期
            if (0 == task.getStatus() && task.getDeadline() != null && currentDate.compareTo(task.getDeadline()) > 0) {
                task.setExpireText("已逾期");
            }

            // 任务进度
            TaskSchedule taskSchedule = new TaskSchedule();
            taskSchedule.setTaskId(id);
            List<TaskSchedule> taskScheduleList = taskScheduleService.selectTaskScheduleList(taskSchedule);
            for (TaskSchedule item : taskScheduleList) {
                TaskScheduleFile taskScheduleFile = new TaskScheduleFile();
                taskScheduleFile.setTaskScheduleId(item.getId());
                List<TaskScheduleFile> taskScheduleFileList = taskScheduleFileService.selectTaskScheduleFileList(taskScheduleFile);
                item.setTaskScheduleFileList(taskScheduleFileList);
            }
            task.setTaskScheduleList(taskScheduleList);
        }
        return success(task);
    }

    /**
     * 新增任务
     */
    @Log(title = "任务", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增任务")
    public AjaxResult add(@RequestBody Task task) {
        return toAjax(taskService.insertTask(task));
    }

    /**
     * 批量新增任务
     */
    @Log(title = "批量任务", businessType = BusinessType.INSERT)
    @PostMapping("/batchInsertTask")
    @ApiOperation("批量新增任务")
    public AjaxResult batchInsertTask() {
        try {
            //生成类型：0系统自动执行，1人工手动执行
            taskService.getGenTask(1);
            return success();
        } catch (Exception e) {
            logger.error("==========> 批量新增任务出错", e);
            return error("批量新增任务");
        }
    }

    /**
     * 修改任务
     */
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改任务")
    public AjaxResult edit(@RequestBody Task task) {
        return toAjax(taskService.updateTask(task));
    }

    /**
     * 删除任务
     */
    @Log(title = "任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(taskService.deleteTaskByIds(ids));
    }

}
