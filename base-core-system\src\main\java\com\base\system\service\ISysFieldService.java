package com.base.system.service;

import com.base.common.core.domain.entity.FastExcelDTO;
import com.base.system.domain.SysField;
import org.apache.poi.ss.formula.functions.T;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 动态字段Service接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface ISysFieldService
{
    /**
     * 查询动态字段
     *
     * @param fieldId 动态字段主键
     * @return 动态字段
     */
    public SysField selectSysFieldByFieldId(Long fieldId);

    /**
     * 查询动态字段列表
     *
     * @param sysField 动态字段
     * @return 动态字段集合
     */
    public List<SysField> selectSysFieldList(SysField sysField);

    /**
     * 新增动态字段
     *
     * @param sysField 动态字段
     * @return 结果
     */
    public SysField insertSysField(SysField sysField);

    SysField insertSysField(SysField fieldItem, String fieldPrefix);

    /**
     * 修改动态字段
     *
     * @param sysField 动态字段
     * @return 结果
     */
    public int updateSysField(SysField sysField);

    int updateSysField(SysField sysField, String fieldPrefix);

    /**
     * 批量删除动态字段
     *
     * @param fieldIds 需要删除的动态字段主键集合
     * @return 结果
     */
    public int deleteSysFieldByFieldIds(Long[] fieldIds);

    /**
     * 删除动态字段信息
     *
     * @param fieldId 动态字段主键
     * @return 结果
     */
    public int deleteSysFieldByFieldId(Long fieldId);

    List<SysField> selectByFieldKey(String fieldKey);

    <T> T setFormatJson(List<SysField> fieldList, T data);

    <T> List<T> setFormatJson(List<SysField> fieldList, List<T> data);

    List<SysField> selectByMenuAndFieldKey(Long menuId, String fieldKey, boolean queryAll);
    List<SysField> selectByMenuAndFieldKey(Long menuId, List<String> fieldKeyList, boolean queryAll);

    String replicate();

    public void addFieldByDict(String dictType, String dictValue, String scene);

    <T> T setFormatJsonByMenu(T data, String menuId, String fieldKeyPrefix);

    <T> List<T> setFormatJsonByMenu(List<T> dataList, String menuId, String fieldKeyPrefix);

    FastExcelDTO<T> exportByField(HttpServletResponse response, List<?> sourceData, List<String> fieldKeyList);

    FastExcelDTO<T> exportByField(HttpServletResponse response, List<?> sourceData, List<String> fieldKeyList, Long menuId);

}
