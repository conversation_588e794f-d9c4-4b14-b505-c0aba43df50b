package com.base.task.service;

import com.base.task.domain.TaskFile;

import java.util.List;

/**
 * 任务文件Service接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public interface ITaskFileService {
    /**
     * 查询任务文件
     *
     * @param id 任务文件主键
     * @return 任务文件
     */
    public TaskFile selectTaskFileById(Long id);

    /**
     * 查询任务文件列表
     *
     * @param taskFile 任务文件
     * @return 任务文件集合
     */
    public List<TaskFile> selectTaskFileList(TaskFile taskFile);

    /**
     * 新增任务文件
     *
     * @param taskFile 任务文件
     * @return 结果
     */
    public int insertTaskFile(TaskFile taskFile);

    /**
     * 修改任务文件
     *
     * @param taskFile 任务文件
     * @return 结果
     */
    public int updateTaskFile(TaskFile taskFile);

    /**
     * 批量删除任务文件
     *
     * @param ids 需要删除的任务文件主键集合
     * @return 结果
     */
    public int deleteTaskFileByIds(Long[] ids);

    /**
     * 删除任务文件信息
     *
     * @param id 任务文件主键
     * @return 结果
     */
    public int deleteTaskFileById(Long id);
}
