package com.base.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.base.common.config.ProductConfig;
import com.base.common.core.redis.RedisCache;
import com.base.system.domain.Product;
import com.base.system.domain.ProductRoot;
import com.base.system.service.IProductInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
@Service
public class ProductInfoServiceImpl implements IProductInfoService {

    @Resource
    private ProductConfig productConfig;

    @Autowired
    private RedisCache redisCache;

    // 新增公共方法：生成 Redis Key
    private static String getRedisKeyForProduct(int productId) {
        return "product:readStatus:" + productId;
    }

    // 获取产品列表
    @Override
    public List<Product> getProductList(String keyword) {
        String configData = productConfig.getProduceConfig();
        JSONObject jsonObject = JSONObject.parseObject(configData);
        List<Product> allProducts = JSON.parseObject(jsonObject.toString(),
                ProductRoot.class, JSONReader.Feature.SupportSmartMatch).getProductList();

        // 设置产品的已读状态
        setProductsReadStatus(allProducts);

        if (keyword == null || keyword.trim().isEmpty()) {
            return allProducts;
        }

        return filterProductsByKeyword(allProducts, keyword.toLowerCase());
    }

    private List<Product> filterProductsByKeyword(List<Product> products, String keyword) {
        return products.stream()
                .peek(product -> {
                    if (product.getChildren() != null) {
                        // 筛选子产品列表
                        List<Product> filteredChildren = product.getChildren().stream()
                                .filter(child ->
                                        (child.getName() != null && child.getName().toLowerCase().contains(keyword)) ||
                                                (child.getIntroduction() != null && child.getIntroduction().toLowerCase().contains(keyword)) ||
                                                (child.getTags() != null && !child.getTags().isEmpty() &&
                                                        child.getTags().stream()
                                                                .anyMatch(tag -> tag != null && tag.toLowerCase().contains(keyword)))
                                )
                                .collect(Collectors.toList());
                        // 更新父级产品的子产品列表
                        product.setChildren(filteredChildren);
                    }
                })
                .filter(product -> product.getChildren() == null || !product.getChildren().isEmpty()) // 移除没有子产品的父级产品
                .collect(Collectors.toList());
    }


    private void setProductsReadStatus(List<Product> products) {
        // 将已读状态合并到产品信息中
        for (Product product : products) {
            Integer readStatus = redisCache.getCacheObject(getRedisKeyForProduct(product.getProductId()));
            if (readStatus != null) {
                product.setReadStatus(readStatus);
            }
            for (Product child : product.getChildren()) {
                readStatus = redisCache.getCacheObject(getRedisKeyForProduct(child.getProductId()));
                if (readStatus != null) {
                    child.setReadStatus(readStatus);
                }
            }
        }
    }

    // 根据产品ID获取产品详情
    @Override
    public Product getProductDetailById(int productId) {
        for (Product product : getProductList(null)) {
            Product result = findProductById(product, productId);
            if (result != null) {
                return result; // 返回匹配的 Product 对象
            }
        }
        return null; // 如果未找到对应ID的产品，返回null
    }


    @Override
    public List<Product> getProductListByIds(Set<Integer> productIds) {
        List<Product> productList = this.getProductList(null);
        return this.filterProductByIds(productList, productIds);
    }

    @Override
    public List<Product> getProductListByIds(Set<Integer> productIds, String enable) {
        List<Product> productList = this.getProductList(null);
        productList = this.filterProductByIds(productList, productIds);
        return this.filterProductByEnable(productList, enable);
    }

    private List<Product> filterProductByIds(List<Product> products, Set<Integer> allowedIds) {
        if (products == null) {
            return Collections.emptyList();
        }

        List<Product> filteredList = new ArrayList<>();
        for (Product product : products) {
            // 递归处理子节点
            List<Product> children = filterProductByIds(product.getChildren(), allowedIds);
            product.setChildren(children);

            // 当前节点符合条件，或者子节点里有符合条件的
            if (allowedIds.contains(product.getProductId()) || !children.isEmpty()) {
                filteredList.add(product);
            }
        }
        return filteredList;
    }

    private List<Product> filterProductByEnable(List<Product> products, String enable) {
        if (products == null) {
            return Collections.emptyList();
        }

        List<Product> filteredList = new ArrayList<>();
        for (Product product : products) {
            // 递归处理子节点
            List<Product> children = filterProductByEnable(product.getChildren(), enable);
            product.setChildren(children);

            // 当前节点符合条件，或者子节点里有符合条件的
            if (StringUtils.equals(enable, product.getIsEnabled()) || !children.isEmpty()) {
                filteredList.add(product);
            }
        }
        return filteredList;
    }

    // 新增递归方法，用于查找父级或子级别的产品
    private Product findProductById(Product product, int productId) {
        if (product.getProductId() == productId) {
            return product; // 找到匹配的产品
        }
        if (product.getChildren() != null) {
            for (Product child : product.getChildren()) {
                Product result = findProductById(child, productId);
                if (result != null) {
                    return result; // 在子级中找到匹配的产品
                }
            }
        }
        return null; // 未找到匹配的产品
    }

    @Override
    public void markProductAsRead(int productId) {
        String key = getRedisKeyForProduct(productId); // 使用公共方法生成 Redis Key
        redisCache.setCacheObject(key, 1); // 标记为已读
    }

}
