package com.base.message.mapper;

import java.util.List;

import com.base.message.domain.SysMessage;

/**
 * 消息通知Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface SysMessageMapper {
    /**
     * 查询消息通知
     *
     * @param id 消息通知主键
     * @return 消息通知
     */
    public SysMessage selectSysMessageById(Long id);

    /**
     * 查询消息通知列表
     *
     * @param sysMessage 消息通知
     * @return 消息通知集合
     */
    public List<SysMessage> selectSysMessageList(SysMessage sysMessage);

    /**
     * 新增消息通知
     *
     * @param sysMessage 消息通知
     * @return 结果
     */
    public int insertSysMessage(SysMessage sysMessage);

    /**
     * 修改消息通知
     *
     * @param sysMessage 消息通知
     * @return 结果
     */
    public int updateSysMessage(SysMessage sysMessage);

    /**
     * 根据条件修改消息通知
     *
     * @param sysMessage 消息通知
     * @return 结果
     */
    public int updateSysMessageByCondition(SysMessage sysMessage);

    /**
     * 删除消息通知
     *
     * @param id 消息通知主键
     * @return 结果
     */
    public int deleteSysMessageById(Long id);

    /**
     * 批量删除消息通知
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysMessageByIds(Long[] ids);
}
