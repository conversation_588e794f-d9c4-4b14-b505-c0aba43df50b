package com.base.common.utils.file;

import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.jpeg.JpegMetadataReader;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import com.drew.metadata.exif.ExifSubIFDDirectory;
import com.base.common.config.BaseConfig;
import com.base.common.constant.Constants;
import com.base.common.utils.StringUtils;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 */
public class ImageUtils {
    private static final Logger log = LoggerFactory.getLogger(ImageUtils.class);

    public static byte[] getImage(String imagePath) {
        InputStream is = getFile(imagePath);
        try {
            return IOUtils.toByteArray(is);
        } catch (Exception e) {
            log.error("图片加载异常 {}", e);
            return null;
        } finally {
            IOUtils.closeQuietly(is);
        }
    }

    public static InputStream getFile(String imagePath) {
        try {
            byte[] result = readFile(imagePath);
            result = Arrays.copyOf(result, result.length);
            return new ByteArrayInputStream(result);
        } catch (Exception e) {
            log.error("获取图片异常 {}", e);
        }
        return null;
    }

    /**
     * 读取文件为字节数据
     *
     * @param url 地址
     * @return 字节数据
     */
    public static byte[] readFile(String url) {
        InputStream in = null;
        try {
            if (url.startsWith("http")) {
                // 网络地址
                URL urlObj = new URL(url);
                URLConnection urlConnection = urlObj.openConnection();
                urlConnection.setConnectTimeout(30 * 1000);
                urlConnection.setReadTimeout(60 * 1000);
                urlConnection.setDoInput(true);
                in = urlConnection.getInputStream();
            } else {
                // 本机地址
                String localPath = BaseConfig.getProfile();
                String downloadPath = localPath + StringUtils.substringAfter(url, Constants.RESOURCE_PREFIX);
                in = new FileInputStream(downloadPath);
            }
            return IOUtils.toByteArray(in);
        } catch (Exception e) {
            log.error("获取文件路径异常 {}", e);
            return null;
        } finally {
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * 获取图片详细信息
     *
     * @param filePath 图片路径
     * @return 图片详细信息
     */
    public static Map<String, String> getImageMetadata(String filePath) {
        Map<String, String> mapData = new HashMap<>(16);
        try {
            File file = new File(filePath);
            Metadata metadata = JpegMetadataReader.readMetadata(file);
            for (Directory directory : metadata.getDirectories()) {
                for (Tag tag : directory.getTags()) {
                    mapData.put(tag.getTagName(), tag.getDescription());
                }
            }
        } catch (Exception e) {
            log.error("=====> 获取图片详细信息出错", e);
        }
        return mapData;
    }

    /**
     * 添加水印
     *
     * @param sourceImagePath 图片源地址
     * @param longitude       经度
     * @param latitude        纬度
     * @param datetime        拍摄时间
     * @param userName        当前上传人
     * @param address         经纬度转换后的地址
     * @param destImagePath   图片目标地址
     * @param x               水印的X坐标
     * @param y               水印的Y坐标
     * @throws IOException
     */
    public static void addWatermark(String sourceImagePath, double longitude, double latitude, String datetime, String userName, String address, String destImagePath, int x, int y) throws Exception {
        File sourceImageFile = new File(sourceImagePath);
        BufferedImage sourceImage = ImageIO.read(sourceImageFile);
        // 读取EXIF元数据
        Metadata metadata = ImageMetadataReader.readMetadata(sourceImageFile);
        ExifSubIFDDirectory directory = metadata.getFirstDirectoryOfType(ExifSubIFDDirectory.class);
        // 默认值
        int orientation = 1;
        if (directory != null && directory.containsTag(ExifSubIFDDirectory.TAG_ORIENTATION)) {
            orientation = directory.getInt(ExifSubIFDDirectory.TAG_ORIENTATION);
        }

        // 调整图片方向
        BufferedImage adjustedImage = adjustOrientation(sourceImage, orientation);

        // 创建图形上下文
        Graphics2D g2d = adjustedImage.createGraphics();
        // 设置水印文字颜色和字体等属性
        g2d.setColor(Color.white);
        g2d.setFont(new Font("Noto Sans CJK SC", Font.BOLD, 50));
        // 绘制水印文本
        g2d.drawString("经度: " + longitude, x, y);
        g2d.drawString("纬度: " + latitude, x, y + 70);
        g2d.drawString("时间: " + datetime, x, y + 140);
        g2d.drawString("姓名: " + userName, x, y + 210);
        g2d.drawString("定位: " + address, x, y + 280);
        // 释放图形上下文资源
        g2d.dispose();
        // 输出图片到目标路径
        ImageIO.write(adjustedImage, "jpg", new File(destImagePath));
    }

    private static BufferedImage adjustOrientation(BufferedImage originalImage, int orientation) {
        switch (orientation) {
            case 2:
                // 水平翻转
                return flipHorizontal(originalImage);
            case 3:
                // 180度旋转
                return rotate180(originalImage);
            case 4:
                // 垂直翻转
                return flipVertical(originalImage);
            case 5:
                // 顺时针旋转90度并水平翻转
                return flipHorizontal(rotatenv0(originalImage));
            case 6:
                // 顺时针旋转90度
                return rotatenv0(originalImage);
            case 7:
                // 逆时针旋转90度并水平翻转
                return flipHorizontal(rotate270(originalImage));
            case 8:
                // 逆时针旋转90度
                return rotate270(originalImage);
            default:
                // 不需要调整
                return originalImage;
        }
    }

    private static BufferedImage flipHorizontal(BufferedImage image) {
        AffineTransform tx = AffineTransform.getScaleInstance(-1, 1);
        tx.translate(-image.getWidth(), 0);
        AffineTransformOp op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
        return op.filter(image, null);
    }

    private static BufferedImage flipVertical(BufferedImage image) {
        AffineTransform tx = AffineTransform.getScaleInstance(1, -1);
        tx.translate(0, -image.getHeight());
        AffineTransformOp op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
        return op.filter(image, null);
    }

    private static BufferedImage rotatenv0(BufferedImage image) {
        AffineTransform tx = AffineTransform.getRotateInstance(Math.PI / 2, image.getWidth() / 2, image.getHeight() / 2);
        AffineTransformOp op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
        return op.filter(image, null);
    }

    private static BufferedImage rotate180(BufferedImage image) {
        AffineTransform tx = AffineTransform.getRotateInstance(Math.PI, image.getWidth() / 2, image.getHeight() / 2);
        AffineTransformOp op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
        return op.filter(image, null);
    }

    private static BufferedImage rotate270(BufferedImage image) {
        AffineTransform tx = AffineTransform.getRotateInstance(-Math.PI / 2, image.getWidth() / 2, image.getHeight() / 2);
        AffineTransformOp op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
        return op.filter(image, null);
    }

}
