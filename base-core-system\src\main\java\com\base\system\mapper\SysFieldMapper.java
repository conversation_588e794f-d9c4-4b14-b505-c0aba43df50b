package com.base.system.mapper;

import com.base.system.domain.SysField;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 动态字段Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Repository
public interface SysFieldMapper
{
    /**
     * 查询动态字段
     *
     * @param fieldId 动态字段主键
     * @return 动态字段
     */
    public SysField selectSysFieldByFieldId(Long fieldId);

    /**
     * 查询动态字段列表
     *
     * @param sysField 动态字段
     * @return 动态字段集合
     */
    public List<SysField> selectSysFieldList(SysField sysField);

    /**
     * 新增动态字段
     *
     * @param sysField 动态字段
     * @return 结果
     */
    public int insertSysField(SysField sysField);

    /**
     * 修改动态字段
     *
     * @param sysField 动态字段
     * @return 结果
     */
    public int updateSysField(SysField sysField);

    /**
     * 删除动态字段
     *
     * @param fieldId 动态字段主键
     * @return 结果
     */
    public int deleteSysFieldByFieldId(Long fieldId);

    /**
     * 批量删除动态字段
     *
     * @param fieldIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysFieldByFieldIds(Long[] fieldIds);

    /**
     * 批量新增动态字段
     *
     * @param sysFieldList 动态字段列表
     * @return 结果
     */
    public int batchSysField(List<SysField> sysFieldList);

    List<SysField> selectByFieldKeyLike(String fieldKey);
}
