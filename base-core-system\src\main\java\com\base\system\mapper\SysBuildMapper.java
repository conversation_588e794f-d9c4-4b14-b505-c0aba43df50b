package com.base.system.mapper;

import com.base.system.domain.SysBuild;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 系统建筑物Mapper接口
 * 定义了与数据库交互的方法
 */
@Mapper
public interface SysBuildMapper {
    /**
     * 根据ID获取建筑物信息
     *
     * @param buildId 建筑物ID
     * @return 建筑物信息
     */
    SysBuild getById(String buildId);

    /**
     * 根据条件查询建筑物列表
     *
     * @param sysBuild 查询条件
     * @return 建筑物列表
     */
    List<SysBuild> selectList(SysBuild sysBuild);

    /**
     * 插入新建筑物
     *
     * @param sysBuild 新建筑物信息
     * @return 影响的行数
     */
    int insert(SysBuild sysBuild);

    /**
     * 更新建筑物信息
     *
     * @param sysBuild 要更新的建筑物信息
     * @return 影响的行数
     */
    int update(SysBuild sysBuild);

    /**
     * 根据ID删除建筑物
     *
     * @param buildId 要删除的建筑物ID
     * @return 影响的行数
     */
    int deleteById(String buildId);
}
