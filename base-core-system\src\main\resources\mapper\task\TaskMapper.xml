<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.task.mapper.TaskMapper">

    <resultMap type="Task" id="TaskResult">
        <result property="id" column="id"/>
        <result property="taskTypeId" column="task_type_id"/>
        <result property="name" column="name"/>
        <result property="taskNumber" column="task_number"/>
        <result property="description" column="description"/>
        <result property="location" column="location"/>
        <result property="deadline" column="deadline"/>
        <result property="completionTime" column="completion_time"/>
        <result property="executor" column="executor"/>
        <result property="urgencyLevel" column="urgency_level"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="sourceType" column="source_type"/>
        <result property="taskTypeName" column="task_type_name"/>
        <result property="userName" column="nick_name"/>
        <result property="createName" column="create_name"/>
        <result property="phoneNumber" column="phonenumber"/>
        <result property="deptName" column="dept_name"/>
        <result property="fieldKey" column="field_key"/>
        <result property="fieldJson" column="field_json"/>
    </resultMap>

    <sql id="selectTaskVo">
        select a.id, a.task_type_id, a.name, a.task_number, a.description, a.location, a.deadline, a.completion_time, a.executor, a.urgency_level, a.status, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, a.del_flag, a.source_type, b.name task_type_name, u.nick_name, u.phonenumber, u1.nick_name create_name, d.dept_name
        , a.field_key, a.field_json
        from public.task a
        inner join public.task_type b on b.id = a.task_type_id
        left join public.sys_user u on u.user_name = a.executor
        left join public.sys_user u1 on u1.user_name = a.create_by
        left join public.sys_dept d on d.dept_id = u.dept_id
    </sql>

    <select id="selectTaskList" parameterType="Task" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        <where>
            a.del_flag = 0
            <if test="taskTypeId != null "> and a.task_type_id = #{taskTypeId}</if>
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="startDatetime != null and startDatetime != '' and endDatetime != null and endDatetime != ''">
                and to_char(a.deadline, 'YYYY-MM-DD HH24:MI:SS') between #{startDatetime} and #{endDatetime}
            </if>
            <if test="year != null and year != ''">
                and to_char(a.deadline, 'YYYY') = #{year}
            </if>
            <if test="type != null and type == 0">
                and (a.executor = #{executor} or a.create_by = #{createBy})
                <if test="status != null and status == 2"> and a.status = 0 and now() > a.deadline </if>
            </if>
            <if test="type != null and type == 1">
                and a.executor = #{executor} and a.status = 0
                <if test="status != null and status == 2"> and now() > a.deadline </if>
            </if>
            <if test="type != null and type == 2">
                and a.create_by = #{createBy}
                <if test="status != null and status == 2"> and a.status = 0 and now() > a.deadline </if>
            </if>
            <if test="type != null and type == 3">
                and a.executor = #{executor} and a.status = 1
            </if>
            <if test="urgencyLevel != null "> and a.urgency_level = #{urgencyLevel}</if>
            <if test="sourceType != null "> and a.source_type = #{sourceType}</if>
        </where>
        order by a.urgency_level desc
    </select>

    <select id="getCountTask" parameterType="Task" resultType="java.lang.Long">
        select count(1) from public.task a
        <where>
            a.del_flag = 0
            <if test="type != null and type == 1">
                and a.executor = #{executor} and a.status = 0
            </if>
            <if test="type != null and type == 2">
                and a.create_by = #{createBy}
            </if>
            <if test="type != null and type == 3">
                and a.executor = #{executor} and a.status = 1
            </if>
        </where>
    </select>

    <select id="selectTaskById" parameterType="Long" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        where a.id = #{id}
    </select>
    
    <select id="getTaskScheduleList" parameterType="Long" resultType="Task">
        SELECT to_char(t.deadline, 'YYYY-MM-DD') AS startDatetime,
            STRING_AGG(DISTINCT t.executor, ',') AS executor,
            STRING_AGG(DISTINCT t.id::text || '__' || t.status::text || '__' || t.executor, ',') AS taskInfoExecutors,
            STRING_AGG(DISTINCT tsf.id::text, ',') AS taskFileStrIds
        FROM public.task t
        LEFT JOIN public.task_schedule ts on ts.task_id = t.id
        LEFT JOIN public.task_schedule_file tsf ON tsf.task_schedule_id = ts.id
        WHERE t.del_flag = 0 AND t.source_type = 1 AND t.task_type_id = #{taskTypeId}
        GROUP BY to_char(t.deadline, 'YYYY-MM-DD')
        ORDER BY startDatetime desc
    </select>

    <insert id="insertTask" parameterType="Task" useGeneratedKeys="true" keyProperty="id">
        insert into task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskTypeId != null">task_type_id,</if>
            <if test="name != null">name,</if>
            <if test="taskNumber != null">task_number,</if>
            <if test="description != null">description,</if>
            <if test="location != null">location,</if>
            <if test="deadline != null">deadline,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="executor != null">executor,</if>
            <if test="urgencyLevel != null">urgency_level,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="fieldKey != null">field_key,</if>
            <if test="fieldJson != null">#{field_json},</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskTypeId != null">#{taskTypeId},</if>
            <if test="name != null">#{name},</if>
            <if test="taskNumber != null">#{taskNumber},</if>
            <if test="description != null">#{description},</if>
            <if test="location != null">#{location},</if>
            <if test="deadline != null">#{deadline},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="executor != null">#{executor},</if>
            <if test="urgencyLevel != null">#{urgencyLevel},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="fieldKey != null">#{fieldKey},</if>
            <if test="fieldJson != null">#{fieldJson},</if>
        </trim>
    </insert>

    <insert id="batchInsertTask" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        insert into task ( task_type_id, name, task_number, description, location, deadline, completion_time, executor, urgency_level, status, create_by, create_time, remark, source_type )
        values
        <foreach collection="list" item="task" separator=",">
            ( #{task.taskTypeId}, #{task.name}, #{task.taskNumber}, #{task.description}, #{task.location}, #{task.deadline}, #{task.completionTime},
              #{task.executor}, #{task.urgencyLevel}, #{task.status}, #{task.createBy}, #{task.createTime}, #{task.remark}, #{task.sourceType}
            )
        </foreach>
    </insert>

    <update id="updateTask" parameterType="Task">
        update task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskTypeId != null">task_type_id = #{taskTypeId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="taskNumber != null">task_number = #{taskNumber},</if>
            <if test="description != null">description = #{description},</if>
            <if test="location != null">location = #{location},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="executor != null">executor = #{executor},</if>
            <if test="urgencyLevel != null">urgency_level = #{urgencyLevel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="fieldKey != null">field_key = #{fieldKey},</if>
            <if test="fieldJson != null">field_json = #{fieldJson},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskById" parameterType="Long">
        delete from task where id = #{id}
    </delete>

    <delete id="deleteTaskByIds" parameterType="String">
        delete from task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>