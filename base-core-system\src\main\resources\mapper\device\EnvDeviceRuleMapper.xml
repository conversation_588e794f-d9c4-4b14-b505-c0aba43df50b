<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.device.mapper.EnvDeviceRuleMapper">

    <resultMap type="EnvDeviceRule" id="EnvDeviceRuleResult">
        <result property="deviceId"    column="device_id"    />
        <result property="rule"    column="rule"    />
        <result property="result"    column="result"    />
        <result property="runTime"    column="run_time"    />
        <result property="level"    column="level"    />
        <result property="warn"    column="warn"    />
        <result property="run"    column="run"    />
        <result property="ruleId"    column="rule_id"    />
    </resultMap>

    <sql id="selectEnvDeviceRuleVo">
        select device_id, rule, result, run_time, level, warn, run, rule_id from device.env_device_rule
    </sql>

    <select id="selectEnvDeviceRuleList" parameterType="EnvDeviceRule" resultMap="EnvDeviceRuleResult">
        <include refid="selectEnvDeviceRuleVo"/>
        <where>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="rule != null  and rule != ''"> and rule = #{rule}</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="runTime != null "> and run_time = #{runTime}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="warn != null "> and warn = #{warn}</if>
            <if test="run != null "> and run = #{run}</if>
        </where>
    </select>

    <select id="selectEnvDeviceRuleByRuleId" parameterType="Long" resultMap="EnvDeviceRuleResult">
        <include refid="selectEnvDeviceRuleVo"/>
        where rule_id = #{ruleId}
    </select>

    <select id="selectByDeviceIdIn" parameterType="list" resultMap="EnvDeviceRuleResult">
        <include refid="selectEnvDeviceRuleVo"/>
        WHERE device_id IN
        <foreach item="deviceId" collection="list" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </select>

    <insert id="insertEnvDeviceRule" parameterType="EnvDeviceRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into device.env_device_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="rule != null">rule,</if>
            <if test="result != null">result,</if>
            <if test="runTime != null">run_time,</if>
            <if test="level != null">level,</if>
            <if test="warn != null">warn,</if>
            <if test="run != null">run,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="rule != null">#{rule},</if>
            <if test="result != null">#{result},</if>
            <if test="runTime != null">#{runTime},</if>
            <if test="level != null">#{level},</if>
            <if test="warn != null">#{warn},</if>
            <if test="run != null">#{run},</if>
         </trim>
    </insert>

    <update id="updateEnvDeviceRule" parameterType="EnvDeviceRule">
        update device.env_device_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="rule != null">rule = #{rule},</if>
            <if test="result != null">result = #{result},</if>
            <if test="runTime != null">run_time = #{runTime},</if>
            <if test="level != null">level = #{level},</if>
            <if test="warn != null">warn = #{warn},</if>
            <if test="run != null">run = #{run},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteEnvDeviceRuleByRuleId" parameterType="Long">
        delete from device.env_device_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteEnvDeviceRuleByRuleIds" parameterType="String">
        delete from device.env_device_rule where rule_id in
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

    <insert id="batchEnvDeviceRule">
        insert into device.env_device_rule( device_id, rule, result, run_time, level, warn, run) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.deviceId}, #{item.rule}, #{item.result}, #{item.runTime}, #{item.level}, #{item.warn}, #{item.run})
        </foreach>
    </insert>
</mapper>
