package com.base.common.utils.mqtt;

import com.base.common.config.MQTTConfig;
import com.base.common.utils.uuid.UUID;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Configuration
public class MQTTUtils {
    private static final Logger log = LoggerFactory.getLogger(MQTTUtils.class);
    @Autowired
    private MQTTConfig mqttConfig;

    private MqttClient mqttClient;
    private boolean isConnected = false;

    public MQTTUtils createDevOpsMQTTClient() {
        this.createMQTTClient();
        return this;
    }

    private MQTTUtils connect() {
        try {
            this.mqttClient.connect(mqttConfig.getOptions());
            isConnected = true;
            log.info("MQTTClient连接成功！");
        } catch (MqttException mqttException) {
            log.error("MQTTClient连接失败！", mqttException);
            isConnected = false;
            reconnect();  // 连接失败时尝试重连
        }
        return this;
    }

    @Bean
    public MqttClient createMQTTClient() {
        try {
            String clientId = mqttConfig.getClientId();
            if (StringUtils.isBlank(clientId)){
                clientId = UUID.fastUUID().toString();
                this.mqttClient = new MqttClient(mqttConfig.getHost(), clientId, new MemoryPersistence());
            }else{
                this.mqttClient = new MqttClient(mqttConfig.getHost(), clientId);
            }
            log.info("MQTTClient创建成功！");
            // 设置回调函数来监听连接丢失
            this.mqttClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    log.error("MQTT连接丢失，正在尝试重新连接...", cause);
                    reconnect();  // 连接丢失时尝试重连
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    // 处理接收到的消息
                    log.info("接收到消息，主题: {}, 消息: {}", topic, new String(message.getPayload(), StandardCharsets.UTF_8));
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    // 处理消息发送完成后的操作
                    log.info("消息发送完成，主题: {}", token.getTopics()[0]);
                }
            });
            return this.mqttClient;
        } catch (MqttException exception) {
            log.error("MQTTClient创建失败！", exception);
            return null;
        }
    }

    // 重连方法
    private void reconnect() {
        int retries = 0;
        while (!isConnected && retries < 500) {
            try {
                log.info("第 {} 次重试连接...", retries + 1);
                this.mqttClient.connect(mqttConfig.getOptions());
                isConnected = true;
            } catch (MqttException e) {
                retries++;
                log.error("重试连接失败，第 {} 次重试...", retries, e);
                try {
                    double pow = Math.pow(2, retries);
                    if (pow > 300) {
                        pow = 300;
                    }
                    // 指数退避（Exponential Backoff）：等待时间逐步增加
                    Thread.sleep((long) pow * 1000);  // 等待 2^retries 秒再重试
                } catch (InterruptedException interruptedException) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        if (!isConnected) {
            log.error("连接失败，已经尝试了 500 次，停止重试！");
        } else {
            log.info("MQTTClient重新连接成功！");
        }
    }

    public boolean publish(String topicName, String message) {
        log.info("订阅主题名:{}, message:{}", topicName, message);
        MqttMessage mqttMessage = new MqttMessage(message.getBytes(StandardCharsets.UTF_8));
        try {
            this.mqttClient.publish(topicName, mqttMessage);
            return true;
        } catch (MqttException exception) {
            log.error("发布消息失败！", exception);
            return false;
        }
    }

    public boolean publish(String topicName, int qos, String message) {
        log.info("主题名:{}, qos:{}, message:{}", topicName, qos, message);
        MqttMessage mqttMessage = new MqttMessage(message.getBytes(StandardCharsets.UTF_8));
        try {
            this.mqttClient.publish(topicName, mqttMessage.getPayload(), qos, false);
            return true;
        } catch (MqttException exception) {
            log.error("发布消息失败！", exception);
            return false;
        }
    }

    public void subscribe(String topicName, int qos) {
        log.info("订阅主题名:{}, qos:{}", topicName, qos);
        try {
            this.mqttClient.subscribe(topicName, qos);
        } catch (MqttException e) {
            log.error("订阅主题失败！", e);
        }
    }

    public void subscribe(String topicName, int qos, IMqttMessageListener messageListener) {
        log.info("订阅主题名:{}, qos:{}, Listener类:{}", topicName, qos, messageListener.getClass());
        try {
            this.mqttClient.subscribe(topicName, qos, messageListener);
        } catch (MqttException e) {
            log.error("订阅主题失败！", e);
        }
    }

    public void subscribe(List<String> topicNames, List<Integer> qoses, List<IMqttMessageListener> messageListeners) {
        try {
            String[] topics = topicNames.toArray(new String[0]);
            int[] qosesArray = new int[qoses.size()];
            for (int i = 0; i < qoses.size(); i++) {
                qosesArray[i] = qoses.get(i);
            }
            IMqttMessageListener[] listeners = messageListeners.toArray(new IMqttMessageListener[0]);

            this.mqttClient.subscribe(topics, qosesArray, listeners);
        } catch (MqttException e) {
            log.error("订阅主题失败！", e);
        }
    }

    public void cleanTopic(String topicName) {
        log.info("取消订阅主题名:{}", topicName);
        try {
            this.mqttClient.unsubscribe(topicName);
        } catch (MqttException e) {
            log.error("取消订阅失败！", e);
        }
    }

    @PostConstruct
    public void initMqttClient() {
        if (mqttConfig.getEnabled()){
            MQTTUtils mqttClientUtils = this.createDevOpsMQTTClient().connect();
//            mqttClientUtils.subscribe("message/call/back", 2, new MessageCallbackListener());
//            mqttClientUtils.subscribe("message/message1", 2, new MessageCallbackListener());
//            mqttClientUtils.subscribe("message/+/back", 2, new MessageCallbackListener());
//            mqttClientUtils.subscribe("message/call/#", 2, new MessageCallbackListener());
        }else{
            log.info("MQTT未连接... enabled={}", mqttConfig.getEnabled());
        }
    }
}
