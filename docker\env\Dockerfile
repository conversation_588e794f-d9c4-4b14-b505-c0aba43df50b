# 使用Python作为基础镜像
FROM java:8
# 设置工作目录
WORKDIR /config
WORKDIR /profile
WORKDIR /app
# 复制应用代码到容器中
#COPY e9-admin.jar /app/admin.jar
# 暴露应用端口
EXPOSE 28080
#定义时区参数
ENV TZ=Asia/Shanghai
#设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo '$TZ' > /etc/timezone
# 设置阿里云的 Alpine 源并安装 ttf-dejavu 和 fontconfig
RUN #set -xe \
#    && sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
#    && apk --no-cache add ttf-dejavu fontconfig
#设置编码
ENV LANG C.UTF-8
# 设置启动命令
CMD ["java", "-jar","/app/base-core-admin.jar","--spring.config.location=/config/application-druid.yml,/config/application.yml"]
