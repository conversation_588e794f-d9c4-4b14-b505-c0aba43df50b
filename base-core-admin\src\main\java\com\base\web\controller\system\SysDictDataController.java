package com.base.web.controller.system;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.domain.dto.SysDictDataDTO;
import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.DateUtils;
import com.base.common.utils.DictUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.common.utils.uuid.UUID;
import com.base.system.service.ISysDictDataService;
import com.base.system.service.ISysDictTypeService;
import com.base.system.service.ISysFieldService;
import com.base.system.service.ISysSceneDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController {
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private ISysSceneDictService sceneDictService;

    @Autowired
    private ISysFieldService sysFieldService;

    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDictData dictData) {
        startPage();
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @PostMapping("/listNew")
    public AjaxResultSnake listNew(@RequestBody SysDictDataDTO dictDataDTO) {
        SysDictData dictDataItem =  dictDataDTO.getDictDataItem();
        List<SysDictData> list = DictUtils.selectDictData(dictDataItem.getDictType(), dictDataItem.getScene(), dictDataItem.getStatus());
        return AjaxResultSnake.success(list);
    }

    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:dict:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictData dictData) {
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
        util.exportExcel(response, list, "字典数据");
    }

    /**
     * 查询字典数据详细
     */
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable Long dictCode) {
        return success(dictDataService.selectDictDataById(dictCode));
    }

    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/get")
    public AjaxResultSnake getInfoById(@RequestParam("dict_data_id") Long dictCode) {
        return AjaxResultSnake.success(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/type/{dictType}")
    public AjaxResult dictType(@PathVariable String dictType) {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(data)) {
            data = new ArrayList<SysDictData>();
        }
        return success(data);
    }

    /**
     * 新增字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDictData dict) {
        dict.setCreateBy(getUsername());
        if (StringUtils.isBlank(dict.getDictValue())) {
            dict.setDictValue(UUID.randomUUID().toString());
        }
        return toAjax(dictDataService.insertDictData(dict));
    }

    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping("add")
    @Transactional
    public AjaxResultSnake addNew(@Validated @RequestBody SysDictDataDTO dictDataDTO) {
        SysDictData dict = dictDataDTO.getDictDataItem();
        dict.setCreateBy(getUsername());
        if (StringUtils.isBlank(dict.getDictValue())) {
            dict.setDictValue(UUID.randomUUID().toString());
        }
        dictDataService.insertDictData(dict);
        sceneDictService.insertSysSceneByDict(dict);
        sysFieldService.addFieldByDict(dict.getDictType(), dict.getDictValue(), dict.getScene());
        return AjaxResultSnake.success(dict);
    }

    /**
     * 修改保存字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDictData dict) {
        dict.setUpdateBy(getUsername());
        return toAjax(dictDataService.updateDictData(dict));
    }

    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResultSnake editNew(@Validated @RequestBody SysDictDataDTO dictDataDTO) {
        SysDictData dict = dictDataDTO.getDictDataItem();
        dict.setUpdateBy(getUsername());
        dict.setUpdateTime(DateUtils.getNowDate());
        dictDataService.updateDictData(dict);
        if (StringUtils.isNotNull(dict.getJoinId())){
            sceneDictService.deleteSysSceneDictByJoinId(dict.getJoinId());
        }
        sceneDictService.insertSysSceneByDict(dict);
        return AjaxResultSnake.success();
    }


    /**
     * 删除字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public AjaxResult remove(@PathVariable Long[] dictCodes) {
        dictDataService.deleteDictDataByIds(dictCodes);
        return success();
    }

    /**
     * 删除字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @RequestMapping("/delete")
    public AjaxResultSnake removeNew(@RequestParam("dict_code") Long dictCode, @RequestParam(value = "join_id", required = false) Long joinId) {
        dictDataService.deleteDictDataByIds(new Long[]{dictCode});
        if (StringUtils.isNotNull(joinId)){
            sceneDictService.deleteSysSceneDictByJoinId(joinId);
        }
        return AjaxResultSnake.success();
    }

    /**
     * 清理数据字典缓存
     */
    @PreAuthorize("@ss.hasPermi('system:dict:edit')") // 根据业务需求设置权限
    @PostMapping("/clear_cache")
    public AjaxResultSnake clearCache(@RequestBody SysDictDataDTO dictDataDTO) {
        SysDictData dictDataItem = dictDataDTO.getDictDataItem();

        // 调用服务层清理缓存
        dictDataService.reloadCache(dictDataItem.getDictType());
        sceneDictService.reloadCache(dictDataItem.getScene());
        return AjaxResultSnake.success();
    }

    @GetMapping("/get_uuid")
    public AjaxResultSnake getUuidSnake() {
        return AjaxResultSnake.success(UUID.randomUUID().toString(true));
    }

    /**
     * 根据多个字典类型查询数据字典列表（支持分页）
     */
    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @PostMapping("/type_list")
    public AjaxResultSnake dictDataTypeList(@RequestBody SysDictDataDTO dto) {
        List<SysDictData> list = DictUtils.selectDictData(dto.getDictType(), dto.getScene(), dto.getStatus());
        return AjaxResultSnake.success(list);
    }


}
