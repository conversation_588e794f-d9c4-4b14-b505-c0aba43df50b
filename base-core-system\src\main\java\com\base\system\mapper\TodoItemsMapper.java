package com.base.system.mapper;

import com.base.system.domain.TodoItems;
import com.base.system.domain.vo.TodoItemsQueryVO;

import java.util.Map;
import java.util.List;

public interface TodoItemsMapper {
    /**
     * 查询待办事项
     *
     * @param todoId 待办事项ID
     * @return 待办事项
     */
    public TodoItems selectTodoItemsById(Long todoId);

    /**
     * 查询待办事项列表
     *
     * @param todoItems 待办事项
     * @return 待办事项集合
     */
    public List<TodoItems> selectTodoItemsList(TodoItems todoItems);

    /**
     * 根据查询条件获取待办事项列表（新增排序字段）
     *
     * @param queryVO 查询条件
     * @return 待办事项列表
     */
    public List<TodoItems> selectTodoItemsByQuery(TodoItemsQueryVO queryVO);

    /**
     * 新增待办事项
     *
     * @param todoItems 待办事项
     * @return 结果
     */
    public int insertTodoItems(TodoItems todoItems);

    /**
     * 修改待办事项
     *
     * @param todoItems 待办事项
     * @return 结果
     */
    public int updateTodoItems(TodoItems todoItems);

    /**
     * 删除待办事项
     *
     * @param todoId 待办事项ID
     * @return 结果
     */
    public int deleteTodoItemsById(Long todoId);

    /**
     * 批量删除待办事项
     *
     * @param todoIds 需要删除的待办事项ID集合
     * @return 结果
     */
    public int deleteTodoItemsByIds(Long[] todoIds);

    /**
     * 统计各状态的待办事项数量
     * 
     * @param userId 用户ID
     * @return 各状态数量统计结果
     */
    List<Map<String, Object>> countTodoItemsByStatus(Long userId);
}
