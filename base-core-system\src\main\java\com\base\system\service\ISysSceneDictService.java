package com.base.system.service;

import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.domain.entity.SysSceneDict;

import java.util.List;

/**
 * 菜单-字典 关联关系Service接口
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface ISysSceneDictService
{
    /**
     * 查询菜单-字典 关联关系
     *
     * @param joinId 菜单-字典 关联关系主键
     * @return 菜单-字典 关联关系
     */
    public SysSceneDict selectSysSceneDictByJoinId(Long joinId);

    /**
     * 查询菜单-字典 关联关系列表
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 菜单-字典 关联关系集合
     */
    public List<SysSceneDict> selectSysSceneDictList(SysSceneDict sysSceneDict);

    /**
     * 新增菜单-字典 关联关系
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 结果
     */
    public int insertSysSceneDict(SysSceneDict sysSceneDict);

    /**
     * 修改菜单-字典 关联关系
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 结果
     */
    public int updateSysSceneDict(SysSceneDict sysSceneDict);

    /**
     * 批量删除菜单-字典 关联关系
     *
     * @param joinIds 需要删除的菜单-字典 关联关系主键集合
     * @return 结果
     */
    public int deleteSysSceneDictByJoinIds(Long[] joinIds);

    /**
     * 删除菜单-字典 关联关系信息
     *
     * @param joinId 菜单-字典 关联关系主键
     * @return 结果
     */
    public int deleteSysSceneDictByJoinId(Long joinId);

    void refreshData(String scene, String dictType, List<Long> dictCodeList);

    /**
     * 根据scene数组查询菜单-字典 关联关系列表
     *
     * @param scenes 场景数组
     * @return 菜单-字典 关联关系集合
     */
    public List<SysSceneDict> selectSysSceneDictListByScenes(List<String> scenes);

    void insertSysSceneByDict(SysDictData dict);

    void reloadCache(String scene);

    SysSceneDict selectChild(String parentType, String childType, String childValue);
}
