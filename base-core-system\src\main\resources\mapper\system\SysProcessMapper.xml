<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.SysProcessMapper">

    <resultMap type="SysProcess" id="SysProcessResult">
        <result property="processName" column="process_name"/>
        <result property="e9Code" column="e9_code"/>
        <result property="parentId" column="parent_id"/>
        <result property="processType" column="process_type"/>
        <result property="posX" column="pos_x"/>
        <result property="posY" column="pos_y"/>
        <result property="posZ" column="pos_z"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptId" column="dept_id"/>
        <result property="status" column="status"/>
        <result property="processId" column="process_id"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <sql id="selectSysProcessVo">
        select process_name,
               e9_code,
               parent_id,
               process_type,
               pos_x,
               pos_y,
               pos_z,
               remark,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               ancestors,
               dept_id,
               status,
               process_id,
               sort
        from sys_process
    </sql>

    <select id="selectSysProcessList" parameterType="SysProcess" resultMap="SysProcessResult">
        <include refid="selectSysProcessVo"/>
        <where>
            <if test="processName != null  and processName != ''">and process_name like concat('%', #{processName},
                '%')
            </if>
            <if test="e9Code != null  and e9Code != ''">and e9_code like concat('%', #{e9Code} , '%')</if>
            <if test="parentId != null ">and parent_id = #{parentId}</if>
            <if test="processType != null  and processType != ''">and process_type = #{processType}</if>
            <if test="posX != null  and posX != ''">and pos_x = #{posX}</if>
            <if test="posY != null  and posY != ''">and pos_y = #{posY}</if>
            <if test="posZ != null  and posZ != ''">and pos_z = #{posZ}</if>
            <if test="ancestors != null  and ancestors != ''">and ancestors = #{ancestors}</if>
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="processId != null ">and process_id = #{processId}</if>
            <if test="sort != null ">and sort = #{sort}</if>
            <if test="delFlag != null ">and del_flag = #{delFlag}</if>
            <if test="delFlag == null ">and del_flag = '0'</if>
        </where>
        ORDER BY sort
    </select>

    <select id="selectSysProcessByProcessId" parameterType="Long" resultMap="SysProcessResult">
        <include refid="selectSysProcessVo"/>
        where process_id = #{processId} and del_flag = '0'
    </select>

    <select id="selectSysProcessByIdIn" resultMap="SysProcessResult">
        <include refid="selectSysProcessVo"/>
        <where>
            process_id in
            <foreach item="processId" collection="list" open="(" separator="," close=")">
                #{processId}
            </foreach>
            and del_flag = '0'
        </where>

    </select>
    <select id="selectChildren" resultMap="SysProcessResult" parameterType="Long">
        <include refid="selectSysProcessVo"></include>
        where find_in_set(#{ancestorId}, ancestors)
    </select>

    <insert id="insertSysProcess" parameterType="SysProcess">
        <selectKey resultType="Long" order="AFTER" keyProperty="processId">
            SELECT nextval('sys_process_process_id_seq'::regclass) AS processId
        </selectKey>
        insert into sys_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processName != null">process_name,</if>
            <if test="e9Code != null">e9_code,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="processType != null">process_type,</if>
            <if test="posX != null">pos_x,</if>
            <if test="posY != null">pos_y,</if>
            <if test="posZ != null">pos_z,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="status != null">status,</if>
            del_flag,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processName != null">#{processName},</if>
            <if test="e9Code != null">#{e9Code},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="processType != null">#{processType},</if>
            <if test="posX != null">#{posX},</if>
            <if test="posY != null">#{posY},</if>
            <if test="posZ != null">#{posZ},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="delFlag == null">'0',</if>
        </trim>
    </insert>

    <update id="updateSysProcess" parameterType="SysProcess">
        update sys_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="processName != null">process_name = #{processName},</if>
            <if test="e9Code != null">e9_code = #{e9Code},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="processType != null">process_type = #{processType},</if>
            <if test="posX != null">pos_x = #{posX},</if>
            <if test="posY != null">pos_y = #{posY},</if>
            <if test="posZ != null">pos_z = #{posZ},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where process_id = #{processId}
    </update>

    <!--    <delete id="deleteSysProcessByProcessId" parameterType="Long">-->
    <!--        delete from sys_process where process_id = #{processId}-->
    <!--    </delete>-->

    <update id="deleteSysProcessByProcessId" parameterType="Long">
        update sys_process
        set del_flag = '1'
        where process_id = #{processId}
    </update>

    <!--    <delete id="deleteSysProcessByProcessIds" parameterType="String">-->
    <!--        delete from sys_process where process_id in-->
    <!--        <foreach item="processId" collection="array" open="(" separator="," close=")">-->
    <!--            #{processId}-->
    <!--        </foreach>-->
    <!--    </delete>-->

    <update id="deleteSysProcessByProcessIds" parameterType="Long">
        update sys_process set del_flag = '1'
        where process_id in
        <foreach item="processId" collection="array" open="(" separator="," close=")">
            #{processId}
        </foreach>
    </update>

    <insert id="batchSysProcess">
        insert into sys_process( process_name, e9_code, parent_id, process_type, pos_x, pos_y, pos_z, remark, del_flag,
        create_by, create_time, update_by, update_time, ancestors, dept_id, status) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.processName}, #{item.e9Code}, #{item.parentId}, #{item.processType}, #{item.posX}, #{item.posY},
            #{item.posZ}, #{item.remark},
            <if test="item.delFlag == null">'0'</if>
            <if test="item.delFlag != null">#{item.delFlag}</if>, #{item.createBy}, #{item.createTime},
            #{item.updateBy}, #{item.updateTime}, #{item.ancestors}, #{item.deptId}, #{item.status})
        </foreach>
    </insert>

    <update id="clear">
        update sys_process set del_flag = '1'
    </update>

</mapper>
