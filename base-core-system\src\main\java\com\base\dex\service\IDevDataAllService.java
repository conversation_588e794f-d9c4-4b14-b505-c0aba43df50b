package com.base.dex.service;

import com.base.dex.domain.DevDataAll;

import java.util.Date;
import java.util.List;

/**
 * 所有设备数据Service接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface IDevDataAllService
{
    /**
     * 查询所有设备数据
     *
     * @param monitorTime 所有设备数据主键
     * @return 所有设备数据
     */
    public DevDataAll selectDevDataAllByMonitorTime(String monitorTime);

    /**
     * 查询所有设备数据列表
     *
     * @param devDataAll 所有设备数据
     * @return 所有设备数据集合
     */
    public List<DevDataAll> selectDevDataAllList(DevDataAll devDataAll);

    /**
     * 新增所有设备数据
     *
     * @param devDataAll 所有设备数据
     * @return 结果
     */
    public int insertDevDataAll(DevDataAll devDataAll);

    /**
     * 批量新增所有设备数据
     *
     * @param devDataAll 所有设备数据
     * @return 结果
     */
    public int batchDevDataAll(List<DevDataAll> devDataAllList);

    /**
     * 修改所有设备数据
     *
     * @param devDataAll 所有设备数据
     * @return 结果
     */
    public int updateDevDataAll(DevDataAll devDataAll);

    /**
     * 批量删除所有设备数据
     *
     * @param monitorTimes 需要删除的所有设备数据主键集合
     * @return 结果
     */
    public int deleteDevDataAllByMonitorTimes(String[] monitorTimes);

    /**
     * 删除所有设备数据信息
     *
     * @param monitorTime 所有设备数据主键
     * @return 结果
     */
    public int deleteDevDataAllByMonitorTime(String monitorTime);

    List<DevDataAll> selectByTimeBetween(Date startTime, Date endTime);
}
