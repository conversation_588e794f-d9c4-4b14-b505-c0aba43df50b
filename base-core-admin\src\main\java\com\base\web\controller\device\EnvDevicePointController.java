package com.base.web.controller.device;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.poi.ExcelUtil;
import com.base.device.domain.EnvDevicePoint;
import com.base.device.domain.dto.EnvDevicePointBindDTO;
import com.base.device.service.IEnvDevicePointService;
import com.base.device.service.IEnvDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备-点位 关联关系Controller
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@RestController
@RequestMapping("/device/device_point")
public class EnvDevicePointController extends BaseController
{
    @Autowired
    private IEnvDevicePointService envDevicePointService;

    @Autowired
    private IEnvDeviceService deviceService;

    /**
     * 分页查询设备-点位 关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('device:device_point:list')")
    @RequestMapping("/page")
    public TableDataInfo page(@RequestBody EnvDevicePoint envDevicePoint)
    {
        startPage();
        List<EnvDevicePoint> list = envDevicePointService.selectEnvDevicePointList(envDevicePoint);
        return getDataTable(list);
    }

    /**
     * 查询设备-点位 关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('device:device_point:list')")
    @RequestMapping("/list")
    public AjaxResult list(@RequestBody EnvDevicePoint envDevicePoint)
    {
        List<EnvDevicePoint> list = envDevicePointService.selectEnvDevicePointList(envDevicePoint);
        return success(list);
    }

    /**
     * 导出设备-点位 关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('device:device_point:export')")
    @Log(title = "设备-点位 关联关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EnvDevicePoint envDevicePoint)
    {
        List<EnvDevicePoint> list = envDevicePointService.selectEnvDevicePointList(envDevicePoint);
        ExcelUtil<EnvDevicePoint> util = new ExcelUtil<EnvDevicePoint>(EnvDevicePoint.class);
        util.exportExcel(response, list, "设备-点位 关联关系数据");
    }

    /**
     * 获取设备-点位 关联关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:device_point:query')")
    @GetMapping(value = "get")
    public AjaxResult getInfo(@RequestParam("joinId") Long joinId)
    {
        return success(envDevicePointService.selectEnvDevicePointByJoinId(joinId));
    }

    /**
     * 新增设备-点位 关联关系
     */
    @PreAuthorize("@ss.hasPermi('device:device_point:add')")
    @Log(title = "设备-点位 关联关系", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody EnvDevicePoint envDevicePoint)
    {
        return toAjax(envDevicePointService.insertEnvDevicePoint(envDevicePoint));
    }

    /**
     * 修改设备-点位 关联关系
     */
    @PreAuthorize("@ss.hasPermi('device:device_point:edit')")
    @Log(title = "设备-点位 关联关系", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody EnvDevicePoint envDevicePoint)
    {
        return toAjax(envDevicePointService.updateEnvDevicePoint(envDevicePoint));
    }

    /**
     * 删除设备-点位 关联关系
     */
    @PreAuthorize("@ss.hasPermi('device:device_point:remove')")
    @Log(title = "设备-点位 关联关系", businessType = BusinessType.DELETE)
	@GetMapping("delete")
    public AjaxResult remove(@RequestParam("joinId") Long joinId)
    {
        return toAjax(envDevicePointService.deleteEnvDevicePointByJoinId(joinId));
    }

    @PostMapping("/bind/all")
    public AjaxResultSnake devicePointBindAll(@RequestBody List<Long> deviceIds) {
        deviceService.baseBindAll(deviceIds);
        return AjaxResultSnake.success();
    }

    /**
     * 绑定设备与点位
     */
    @PreAuthorize("@ss.hasPermi('device:device_point:bind')")
    @Log(title = "绑定设备和因子", businessType = BusinessType.INSERT)
    @PostMapping("/bind")
    public AjaxResultSnake devicePointBind(@RequestBody EnvDevicePointBindDTO bindDTO) {
        Long deviceId = bindDTO.getDeviceId();
        List<String> pointIds = bindDTO.getPointIdList();

        deviceService.bind(deviceId, pointIds);

        return AjaxResultSnake.success();
    }


}
