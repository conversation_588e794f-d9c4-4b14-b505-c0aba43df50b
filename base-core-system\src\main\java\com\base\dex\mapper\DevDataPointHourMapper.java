package com.base.dex.mapper;

import com.base.dex.domain.DevDataPointHour;

import java.util.List;

/**
 * 点位小时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface DevDataPointHourMapper {
    /**
     * 查询点位小时数据
     *
     * @param pointId 点位小时数据主键
     * @return 点位小时数据
     */
    public DevDataPointHour selectDevDataPointHourByPointId(String pointId);

    /**
     * 查询点位小时数据列表
     *
     * @param devDataPointHour 点位小时数据
     * @return 点位小时数据集合
     */
    public List<DevDataPointHour> selectDevDataPointHourList(DevDataPointHour devDataPointHour);

    /**
     * 新增点位小时数据
     *
     * @param devDataPointHour 点位小时数据
     * @return 结果
     */
    public int insertDevDataPointHour(DevDataPointHour devDataPointHour);

    /**
     * 修改点位小时数据
     *
     * @param devDataPointHour 点位小时数据
     * @return 结果
     */
    public int updateDevDataPointHour(DevDataPointHour devDataPointHour);

    /**
     * 删除点位小时数据
     *
     * @param pointId 点位小时数据主键
     * @return 结果
     */
    public int deleteDevDataPointHourByPointId(String pointId);

    /**
     * 批量删除点位小时数据
     *
     * @param pointIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDevDataPointHourByPointIds(String[] pointIds);

    /**
     * 获取点位小时数据列表
     *
     * @param devDataPointHour 点位小时数据
     * @return 点位小时数据集合
     */
    public List<DevDataPointHour> getDevDataPointHourList(DevDataPointHour devDataPointHour);

    /**
     * 获取环境质量监测微站-小型空气质量监测站14和厂界监测微站21的最近平均值
     *
     * @param devDataPointHour 点位小时数据
     * @return 环境质量监测微站-小型空气质量监测站14和厂界监测微站21的最近平均值
     */
    public DevDataPointHour getDevDataPointHourByAvg(DevDataPointHour devDataPointHour);

}
