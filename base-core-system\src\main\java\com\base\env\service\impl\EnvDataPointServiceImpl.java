package com.base.env.service.impl;

import com.base.common.core.text.Convert;
import com.base.env.domain.EnvDataPoint;
import com.base.env.mapper.EnvDataPointMapper;
import com.base.env.service.IEnvDataPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 企业厂界范围坐标配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Service
public class EnvDataPointServiceImpl implements IEnvDataPointService {
    @Autowired
    private EnvDataPointMapper envDataPointMapper;

    /**
     * 查询企业厂界范围坐标配置
     *
     * @param id 企业厂界范围坐标配置ID
     * @return 企业厂界范围坐标配置
     */
    @Override
    public EnvDataPoint selectEnvDataPointById(Integer id) {
        return envDataPointMapper.selectEnvDataPointById(id);
    }

    /**
     * 查询企业厂界范围坐标配置列表
     *
     * @param envDataPoint 企业厂界范围坐标配置
     * @return 企业厂界范围坐标配置
     */
    @Override
    public List<EnvDataPoint> selectEnvDataPointList(EnvDataPoint envDataPoint) {
        return envDataPointMapper.selectEnvDataPointList(envDataPoint);
    }

    /**
     * 新增企业厂界范围坐标配置
     *
     * @param envDataPoint 企业厂界范围坐标配置
     * @return 结果
     */
    @Override
    public int insertEnvDataPoint(EnvDataPoint envDataPoint) {
        return envDataPointMapper.insertEnvDataPoint(envDataPoint);
    }

    /**
     * 修改企业厂界范围坐标配置
     *
     * @param envDataPoint 企业厂界范围坐标配置
     * @return 结果
     */
    @Override
    public int updateEnvDataPoint(EnvDataPoint envDataPoint) {
        return envDataPointMapper.updateEnvDataPoint(envDataPoint);
    }

    /**
     * 删除企业厂界范围坐标配置对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEnvDataPointByIds(String ids) {
        return envDataPointMapper.deleteEnvDataPointByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除企业厂界范围坐标配置信息
     *
     * @param id 企业厂界范围坐标配置ID
     * @return 结果
     */
    @Override
    public int deleteEnvDataPointById(Integer id) {
        return envDataPointMapper.deleteEnvDataPointById(id);
    }
}
