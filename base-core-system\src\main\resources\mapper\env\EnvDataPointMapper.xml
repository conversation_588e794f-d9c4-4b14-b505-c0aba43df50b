<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.env.mapper.EnvDataPointMapper">

    <resultMap type="EnvDataPoint" id="EnvDataPointResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="posX" column="pos_x"/>
        <result property="posY" column="pos_y"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectEnvDataPointVo">
        select id, type, pos_x, pos_y, create_time, update_time, remark from env.env_data_point
    </sql>

    <select id="selectEnvDataPointList" parameterType="EnvDataPoint" resultMap="EnvDataPointResult">
        <include refid="selectEnvDataPointVo"/>
        <where>
            <if test="type != null ">and type = #{type}</if>
            <if test="posX != null  and posX != ''">and pos_x = #{posX}</if>
            <if test="posY != null  and posY != ''">and pos_y = #{posY}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
        </where>
    </select>

    <select id="selectEnvDataPointById" parameterType="Integer" resultMap="EnvDataPointResult">
        <include refid="selectEnvDataPointVo"/>
        where id = #{id}
    </select>

    <insert id="insertEnvDataPoint" parameterType="EnvDataPoint" useGeneratedKeys="true" keyProperty="id">
        insert into env_data_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null ">type,</if>
            <if test="posX != null  and posX != ''">pos_x,</if>
            <if test="posY != null  and posY != ''">pos_y,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="remark != null  and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null ">#{type},</if>
            <if test="posX != null  and posX != ''">#{posX},</if>
            <if test="posY != null  and posY != ''">#{posY},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="remark != null  and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateEnvDataPoint" parameterType="EnvDataPoint">
        update env.env_data_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null ">type = #{type},</if>
            <if test="posX != null  and posX != ''">pos_x = #{posX},</if>
            <if test="posY != null  and posY != ''">pos_y = #{posY},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
            <if test="remark != null  and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEnvDataPointById" parameterType="Integer">
        delete from env.env_data_point where id = #{id}
    </delete>

    <delete id="deleteEnvDataPointByIds" parameterType="String">
        delete from env.env_data_point where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>