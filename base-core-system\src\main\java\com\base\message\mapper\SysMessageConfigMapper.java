package com.base.message.mapper;

import java.util.List;

import com.base.message.domain.SysMessageConfig;

/**
 * 消息通知配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface SysMessageConfigMapper {
    /**
     * 查询消息通知配置
     *
     * @param id 消息通知配置主键
     * @return 消息通知配置
     */
    public SysMessageConfig selectSysMessageConfigById(Long id);

    /**
     * 查询消息通知配置列表
     *
     * @param sysMessageConfig 消息通知配置
     * @return 消息通知配置集合
     */
    public List<SysMessageConfig> selectSysMessageConfigList(SysMessageConfig sysMessageConfig);

    /**
     * 新增消息通知配置
     *
     * @param sysMessageConfig 消息通知配置
     * @return 结果
     */
    public int insertSysMessageConfig(SysMessageConfig sysMessageConfig);

    /**
     * 修改消息通知配置
     *
     * @param sysMessageConfig 消息通知配置
     * @return 结果
     */
    public int updateSysMessageConfig(SysMessageConfig sysMessageConfig);

    /**
     * 删除消息通知配置
     *
     * @param id 消息通知配置主键
     * @return 结果
     */
    public int deleteSysMessageConfigById(Long id);

    /**
     * 批量删除消息通知配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysMessageConfigByIds(Long[] ids);
}
