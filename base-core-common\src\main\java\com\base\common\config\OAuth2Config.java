package com.base.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "oauth2")
public class OAuth2Config {

    private static String host;

    private static String serverLoginUrl;

    private static String serverTokenUrl;

    private static String serverUserInfo;

    private static String serviceUrl;

    private static String clientId;
    
    private static String clientSecret;


    public static String getHost() {
        return host;
    }

    public void setHost(String host) {
        OAuth2Config.host = host;
    }

    public static String getServerLoginUrl() {
        return serverLoginUrl;
    }

    public void setServerLoginUrl(String serverLoginUrl) {
        OAuth2Config.serverLoginUrl = serverLoginUrl;
    }

    public static String getServerTokenUrl() {
        return serverTokenUrl;
    }

    public void setServerTokenUrl(String serverTokenUrl) {
        OAuth2Config.serverTokenUrl = serverTokenUrl;
    }

    public static String getServerUserInfo() {
        return serverUserInfo;
    }

    public void setServerUserInfo(String serverUserInfo) {
        OAuth2Config.serverUserInfo = serverUserInfo;
    }

    public static String getServiceUrl() {
        return serviceUrl;
    }

    public void setServiceUrl(String serviceUrl) {
        OAuth2Config.serviceUrl = serviceUrl;
    }

    public static String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        OAuth2Config.clientId = clientId;
    }

    public static String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        OAuth2Config.clientSecret = clientSecret;
    }
}
