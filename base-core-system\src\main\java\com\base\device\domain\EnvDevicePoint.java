package com.base.device.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 设备-点位 关联关系对象 env_device_point
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public class EnvDevicePoint extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** env_device 设备表ID */
    @Excel(name = "env_device 设备表ID")
    private Long deviceId;

    /** dev_base_point 点位表ID */
    @Excel(name = "dev_base_point 点位表ID")
    private String pointId;

    /** 自增ID */
    private Long joinId;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 是否显示 0显示 1隐藏 */
    @Excel(name = "是否显示 0显示 1隐藏")
    private Long status;

    /** 是否默认勾选中  0选中 1不选中 */
    @Excel(name = "是否默认勾选中  0选中 1不选中")
    @JsonAlias("default")
    @JsonProperty("default")
    private Long defaultFlag;

    @Excel(name = "超低限值")
    private Double lowLimit;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("deviceId", getDeviceId())
            .append("pointId", getPointId())
            .append("joinId", getJoinId())
            .append("sort", getSort())
            .append("status", getStatus())
            .append("default", getDefaultFlag())
            .toString();
    }
}
