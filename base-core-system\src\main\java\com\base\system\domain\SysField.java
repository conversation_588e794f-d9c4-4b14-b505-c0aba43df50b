package com.base.system.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 动态字段对象 sys_field
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public class SysField extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 类型 对应device中field_key 例:meter_type */
    @Excel(name = "类型 对应device中field_key 例:meter_type")
    @NotBlank(message = "动态字段类型不能为空",  groups = {SysFieldAdd.class})
    @JsonProperty("field_key")
    private String fieldKey;

    /** field名称 设备名称/设备编号... */
    @Excel(name = "field名称 设备名称/设备编号...")
    @NotBlank(message = "动态字段类型不能为空", groups = {SysFieldAdd.class})
    @JsonProperty("field_label")
    private String fieldLabel;

    /** field对应key 例:name/e9_code... */
    @Excel(name = "field对应key 例:name/e9_code...")
    @JsonProperty("field_value")
    private String fieldValue;

    /** 字段类型 例:int/float/str/img/dict/device... */
    @Excel(name = "字段类型 例:int/float/str/img/dict/device...")
    @NotBlank(message = "动态字段类型不能为空", groups = {SysFieldAdd.class})
    @JsonProperty("field_type")
    private String fieldType;

    /** 字段默认值 */
    @Excel(name = "字段默认值")
    @JsonProperty("field_default")
    private String fieldDefault;

    /** 删除标识 */
    @JsonProperty("del_flag")
    private Long delFlag;

    /** 是否为通用字段  0为专属字段, 可删除 1为通用字段, 即不可删除   */
    @Excel(name = "是否为通用字段  0为专属字段, 可删除 1为通用字段, 即不可删除  ")
    @JsonProperty("field_required")
    private Long fieldRequired;

    /** 0为动态字段(系统自动更新)  1为静态字段(人工填写) */
    @Excel(name = "0为动态字段(系统自动更新)  1为静态字段(人工填写)")
    @JsonProperty("field_static")
    private Long fieldStatic;

    /** 动态字段场景 关联字典 sys_field_scene */
    @Excel(name = "动态字段场景 关联字典 sys_field_scene")
    @JsonProperty("field_scene")
    private String fieldScene;

    /** 字段类型为dict时需要选则关联的字典类型 */
    @Excel(name = "字段类型为dict时需要选则关联的字典类型")
    @JsonProperty("dict_type")
    private String dictType;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonProperty("sort")
    private Long sort;

    /** 状态标识  0正常  1停用 */
    @Excel(name = "状态标识  0正常  1停用")
    @JsonProperty("status")
    private Long status;

    /** $column.columnComment */
    @JsonProperty("field_id")
    @NotNull(message = "字段ID不能为空", groups = {SysFieldEdit.class})
    private Long fieldId;

    /** 默认该字段是否展示  0 默认展示  1 默认不展示 */
    @Excel(name = "默认该字段是否展示  0 默认展示  1 默认不展示")
    @JsonProperty("field_default_show_flag")
    private String fieldDefaultShowFlag;

    /** 字段配置版本 根据业务小类 下载设备导入模板的时候 添加或者更新版本号 */
    @Excel(name = "字段配置版本 根据业务小类 下载设备导入模板的时候 添加或者更新版本号")
    @JsonProperty("field_config_version")
    private String fieldConfigVersion;

    /** 是否用来筛选：0否，1是，默认为0 */
    @Excel(name = "是否用来筛选：0否，1是，默认为0")
    @JsonProperty("field_filter")
    private Long fieldFilter;

    private Object value;

    private Object sourceValue;

    private Long joinId;

    public interface SysFieldAdd{
    }

    public interface SysFieldEdit{
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("fieldKey", getFieldKey())
            .append("fieldLabel", getFieldLabel())
            .append("fieldValue", getFieldValue())
            .append("fieldType", getFieldType())
            .append("fieldDefault", getFieldDefault())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("fieldRequired", getFieldRequired())
            .append("fieldStatic", getFieldStatic())
            .append("fieldScene", getFieldScene())
            .append("dictType", getDictType())
            .append("sort", getSort())
            .append("status", getStatus())
            .append("fieldId", getFieldId())
            .append("remark", getRemark())
            .append("fieldDefaultShowFlag", getFieldDefaultShowFlag())
            .append("fieldConfigVersion", getFieldConfigVersion())
            .append("fieldFilter", getFieldFilter())
            .toString();
    }
}
