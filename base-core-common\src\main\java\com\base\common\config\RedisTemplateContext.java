package com.base.common.config;

import com.base.common.enums.RedisIndex;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class RedisTemplateContext {
    @Resource(name = "redisTemplateBase")
    private RedisTemplate baseRedisTemplate;

    @Resource(name = "redisTemplateDex")
    private RedisTemplate dexRedisTemplate;

    @Resource(name = "redisTemplateAtlanta")
    private RedisTemplate atlantaRedisTemplate;

    private final ThreadLocal<RedisTemplate> currentRedisTemplate = new ThreadLocal<>();

    private final Map<RedisIndex, RedisTemplate> templateMap = new HashMap<>();

    public void setRedisTemplate(RedisIndex name) {
        if (name.equals(RedisIndex.BASE)){
            currentRedisTemplate.set(baseRedisTemplate);
        }else if (name.equals(RedisIndex.DEX)){
            currentRedisTemplate.set(dexRedisTemplate);
        }else if (name.equals(RedisIndex.ATLANTA)){
            currentRedisTemplate.set(atlantaRedisTemplate);
        }else{
            throw new IllegalArgumentException("Unknown redis template: " + name);
        }
    }

    public RedisTemplate getRedisTemplate() {
        RedisTemplate redisTemplate = currentRedisTemplate.get();
        if (redisTemplate == null){
            return baseRedisTemplate;
        }
        return redisTemplate;
    }

    public void clear() {
        currentRedisTemplate.remove();
    }
}
