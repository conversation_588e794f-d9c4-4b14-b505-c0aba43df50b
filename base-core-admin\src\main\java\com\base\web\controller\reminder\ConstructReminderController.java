package com.base.web.controller.reminder;

import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.redis.RedisCache;
import com.base.reminder.domain.ReminderInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 构建提醒控制器
 * 用于处理与构建提醒相关的操作，包括保存密钥、获取状态、获取设置和保存设置等。
 */
@RestController
@RequestMapping("/construct")
public class ConstructReminderController extends BaseController {

    // Redis缓存键前缀
    private static final String REMINDER_INFO_KEY = "construct:reminder:info";

    // 永久通行证前缀
    private static final String PERMANENT_PASS_PREFIX = "ZS";

    // 临时通行证前缀
    private static final String TEMPORARY_PASS_PREFIX = "LS";

    @Autowired
    private RedisCache redisCache; // Redis缓存工具类

    /**
     * 保存构建提醒密钥
     *
     * @param pass_key 密钥
     * @return 操作结果
     */
    @PostMapping("/reminder/key/save")
    public AjaxResult constructReminderKeySave(@RequestParam String pass_key) {
        // 从Redis获取提醒信息
        Map<String, Object> info = redisCache.getCacheObject(REMINDER_INFO_KEY);
        if (info == null) {
            info = new HashMap<>();
        }
        info.put("pass_key", pass_key);
        if (passKeyCondition(info)) {
            info.put("pass_key_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            info.put("switch", 0); // 激活成功关闭提醒功能
            redisCache.setCacheObject(REMINDER_INFO_KEY, info); // 将更新后的信息保存到Redis
            return success();
        }
        return error("密钥错误!请输入正确的密钥");
    }

    /**
     * 获取构建提醒状态
     *
     * @return 提醒状态
     */
    @GetMapping("/reminder/state")
    public AjaxResult constructReminderState() {
        // 从Redis获取提醒信息
        Map<String, Object> info = redisCache.getCacheObject(REMINDER_INFO_KEY);
        if (info == null) {
            // 初始化
            ReminderInfo reminderInfo = new ReminderInfo();
            info = new HashMap<>();
            info.put("create_time", reminderInfo.getCreateTime());
            info.put("permanent_pass_key", reminderInfo.getPermanentPassKey());
            info.put("temporary_pass_key", reminderInfo.getTemporaryPassKey());
            redisCache.setCacheObject(REMINDER_INFO_KEY, info); // 将初始化信息保存到Redis
        } else {
            String passKey = (String) info.get("pass_key");
            // 判断临时激活码是否过期
            if (passKey != null && !passKey.isEmpty() && !passKey.startsWith(PERMANENT_PASS_PREFIX) &&
                    (new Date().getTime() - parseDate((String) info.get("pass_key_time")).getTime()) >= 3 * 24 * 60 * 60 * 1000) {
                info.put("temporary_pass_key", ReminderInfo.generateTemporaryPassKey()); // 重置临时激活码
                info.put("pass_key", ""); // 清空用户输入激活码
                info.put("switch", 1); // 清空用户输入激活码
                redisCache.setCacheObject(REMINDER_INFO_KEY, info); // 更新临时密钥并保存到Redis
            }
        }

        Map<String, Object> res = new HashMap<>();
        res.put("pass_state", 0);
        res.put("stage", 1);
        res.put("sub_stage", 1);
        res.put("content", "");
        res.put("switch", 1);
        res.put("limit_state", 1);
        res.put("expire_day", 0);

        info = redisCache.getCacheObject(REMINDER_INFO_KEY); // 重新从Redis获取提醒信息
        if (info.get("switch") != null && (Integer) info.get("switch") == 1) {
            res.put("stage", info.get("stage"));
            res.put("pass_state", passKeyCondition(info) ? 1 : 0);
            res.put("limit_state", info.get("limit_state"));
            if (info.get("stage") != null && (Integer) info.get("stage") == 1) {
                res.put("content", contentFormat((String) info.get("try_reminder_content"),
                        (String) info.get("try_date")));
            } else if (info.get("stage") != null && (Integer) info.get("stage") == 2) {
                String refundDateStr = (String) info.get("refund_date");
                if (refundDateStr != null && !refundDateStr.isEmpty()) {
                    Date refundDate = parseDate(refundDateStr);
                    if (refundDate.before(new Date())) {
                        res.put("content", info.get("countdown_over_reminder_content"));
                        res.put("sub_stage", 3);
                        res.put("expire_day", Math.abs((new Date().getTime() - refundDate.getTime()) / (24 * 60 * 60 * 1000)));
                    } else {
                        Date now = parseDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
                        res.put("content", contentFormat((String) info.get("countdown_reminder_content"),
                                (int) ((refundDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)),
                                refundDateStr));
                        res.put("sub_stage", 2);
                    }
                }
            }
        } else {
            res.put("switch", 0);
        }

        return success(res);
    }

    /**
     * 获取构建提醒设置
     *
     * @return 提醒设置
     */
    @GetMapping("/reminder/setting/get")
    public AjaxResult constructReminderSettingGet() {
        // 从Redis获取提醒设置
        Map<String, Object> info = redisCache.getCacheObject(REMINDER_INFO_KEY);
        if (info == null) {
            // 初始化
            ReminderInfo reminderInfo = new ReminderInfo();
            info = new HashMap<>();
            info.put("create_time", reminderInfo.getCreateTime());
            info.put("permanent_pass_key", reminderInfo.getPermanentPassKey());
            info.put("temporary_pass_key", reminderInfo.getTemporaryPassKey());
            redisCache.setCacheObject(REMINDER_INFO_KEY, info); // 将初始化设置保存到Redis
            return success(info);
        }
        return success(info);
    }

    /**
     * 保存构建提醒设置
     *
     * @param switchState                       开关状态
     * @param stage                             阶段
     * @param try_date                          试用日期
     * @param try_reminder_content              试用提醒内容
     * @param refund_date                       退款日期
     * @param refund_countdown_reminder_day_num 退款倒计时提醒天数
     * @param reminder_content                  提醒内容
     * @param countdown_reminder_content        倒计时提醒内容
     * @param countdown_over_reminder_content   倒计时结束提醒内容
     * @param limit_state                       限制状态
     * @return 操作结果
     */
    @PostMapping("/reminder/setting/save")
    public AjaxResult constructReminderSettingSave(@RequestParam int switchState,
                                                   @RequestParam int stage,
                                                   @RequestParam(required = false) String try_date,
                                                   @RequestParam(required = false) String try_reminder_content,
                                                   @RequestParam(required = false) String refund_date,
                                                   @RequestParam(required = false) Integer refund_countdown_reminder_day_num,
                                                   @RequestParam(required = false) String reminder_content,
                                                   @RequestParam(required = false) String countdown_reminder_content,
                                                   @RequestParam(required = false) String countdown_over_reminder_content,
                                                   @RequestParam int limit_state) {
        // 从Redis获取旧的提醒设置
        Map<String, Object> oldInfo = redisCache.getCacheObject(REMINDER_INFO_KEY);
        Map<String, Object> info = new HashMap<>();
        info.put("switch", switchState);
        info.put("stage", stage);
        info.put("try_date", try_date);
        info.put("try_reminder_content", try_reminder_content);
        info.put("refund_date", refund_date);
        info.put("refund_countdown_reminder_day_num", refund_countdown_reminder_day_num);
        info.put("reminder_content", reminder_content);
        info.put("countdown_reminder_content", countdown_reminder_content);
        info.put("countdown_over_reminder_content", countdown_over_reminder_content);
        info.put("create_time", oldInfo.get("create_time"));
        info.put("limit_state", limit_state);
        info.put("pass_key", oldInfo.get("pass_key"));
        info.put("pass_key_time", oldInfo.get("pass_key_time"));
        info.put("permanent_pass_key", oldInfo.get("permanent_pass_key"));
        info.put("temporary_pass_key", oldInfo.get("temporary_pass_key"));
        redisCache.setCacheObject(REMINDER_INFO_KEY, info); // 将新的设置保存到Redis
        return success();
    }

    /**
     * 检查密钥条件
     *
     * @param info 提醒信息
     * @return 是否满足条件
     */
    private boolean passKeyCondition(Map<String, Object> info) {
        String passKey = (String) info.get("pass_key");
        if (passKey != null && passKey.startsWith(TEMPORARY_PASS_PREFIX)) {
            return passKey.equals(info.get("temporary_pass_key"));
        } else {
            return passKey.equals(info.get("permanent_pass_key"));
        }
    }

    /**
     * 解析日期字符串
     *
     * @param dateStr 日期字符串
     * @return 日期对象
     */
    private Date parseDate(String dateStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateStr);
        } catch (Exception e) {
            try {
                return new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
            } catch (Exception ex) {
                return new Date(0);
            }
        }
    }

    /**
     * 格式化内容
     *
     * @param content 内容模板
     * @param args    参数
     * @return 格式化后的内容
     */
    private String contentFormat(String content, Object... args) {
        if (content != null && !content.isEmpty()) {
            // 替换 ${ 为 {，并使用 MessageFormat 进行格式化
            String formattedContent = content.replace("${", "{");
            return MessageFormat.format(formattedContent, args);
        }
        return "";
    }
}
