package com.base.env.service;

import com.base.env.domain.EnvDataPoint;

import java.util.List;

/**
 * 企业厂界范围坐标配置Service接口
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
public interface IEnvDataPointService {
    /**
     * 查询企业厂界范围坐标配置
     *
     * @param id 企业厂界范围坐标配置ID
     * @return 企业厂界范围坐标配置
     */
    public EnvDataPoint selectEnvDataPointById(Integer id);

    /**
     * 查询企业厂界范围坐标配置列表
     *
     * @param envDataPoint 企业厂界范围坐标配置
     * @return 企业厂界范围坐标配置集合
     */
    public List<EnvDataPoint> selectEnvDataPointList(EnvDataPoint envDataPoint);

    /**
     * 新增企业厂界范围坐标配置
     *
     * @param envDataPoint 企业厂界范围坐标配置
     * @return 结果
     */
    public int insertEnvDataPoint(EnvDataPoint envDataPoint);

    /**
     * 修改企业厂界范围坐标配置
     *
     * @param envDataPoint 企业厂界范围坐标配置
     * @return 结果
     */
    public int updateEnvDataPoint(EnvDataPoint envDataPoint);

    /**
     * 批量删除企业厂界范围坐标配置
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteEnvDataPointByIds(String ids);

    /**
     * 删除企业厂界范围坐标配置信息
     *
     * @param id 企业厂界范围坐标配置ID
     * @return 结果
     */
    public int deleteEnvDataPointById(Integer id);
}
