package com.base.web.controller.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.annotation.Log;
import com.base.common.config.BaseConfig;
import com.base.common.constant.Constants;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.core.text.Convert;
import com.base.common.enums.BusinessType;
import com.base.common.utils.StringUtils;
import com.base.system.service.ISysUserService;
import com.base.task.domain.Task;
import com.base.task.domain.TaskScheduleFile;
import com.base.task.domain.TaskType;
import com.base.task.service.ITaskScheduleFileService;
import com.base.task.service.ITaskService;
import com.base.task.service.ITaskTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 任务类型 Controller
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Api(tags = "任务类型管理")
@RestController
@RequestMapping("/api/task/taskType")
public class TaskTypeController extends BaseController {
    @Autowired
    private ITaskTypeService taskTypeService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ITaskService taskService;

    @Autowired
    private ITaskScheduleFileService taskScheduleFileService;

    /**
     * 下拉框-任务类型列表
     */
    @GetMapping(value = "/allList")
    @ApiOperation("下拉框-任务类型列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "menuStatus", value = "是否为菜单：0否（可删除），1是（不可删除）", required = false, dataType = "int", paramType = "query"),
    })
    public AjaxResult allList(Integer menuStatus) {
        JSONArray result = new JSONArray();
        TaskType taskType = new TaskType();
        taskType.setMenuStatus(menuStatus);
        List<TaskType> list = taskTypeService.selectTaskTypeList(taskType);
        list.forEach(item -> {
            JSONObject obj = new JSONObject();
            obj.put("id", item.getId());
            obj.put("name", item.getName());
            // 是否定期生成：0否，1是
            obj.put("periodicStatus", item.getPeriodicStatus());
            // 紧急程度：1一般，2紧急
            obj.put("urgencyLevel", item.getUrgencyLevel());
            // 是否需要复核：0否，1是，默认为0
            obj.put("reviewStatus", item.getReviewStatus());
            obj.put("releaseInfo", item.getReleaseInfo());
            obj.put("progressInfo", item.getProgressInfo());
            obj.put("finishInfo", item.getFinishInfo());
            result.add(obj);
        });
        return AjaxResult.success(result);
    }

    /**
     * 查询任务类型列表
     */
    @GetMapping("/list")
    @ApiOperation("任务类型列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "当前页", required = true, defaultValue = "1", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "每页显示数", required = true, defaultValue = "10", dataType = "int", paramType = "query"),
    })
    public TableDataInfo list() {
        startPage();
        List<TaskType> list = taskTypeService.selectTaskTypeList(null);
        List<String> userNameList = list.stream()
                .map(TaskType::getExecutor)
                .filter(executor -> StringUtils.isNotEmpty(executor))
                .flatMap(executor -> Arrays.stream(executor.split(",")))
                .collect(Collectors.toList());
        Map<String, String> userMap = sysUserService.getUserInfoMap(userNameList);
        for (TaskType taskType : list) {
            String executor = taskType.getExecutor();
            if (StringUtils.isNotEmpty(executor)) {
                // 拆分 executor 并将对应的值拼接
                String executorName = Arrays.stream(executor.split(","))
                        .map(userMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));
                taskType.setExecutorName(executorName);
            }
        }
        return getDataTable(list);
    }

    /**
     * 获取任务类型详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("任务类型详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        TaskType taskType = taskTypeService.selectTaskTypeById(id);
        if (taskType != null && StringUtils.isNotEmpty(taskType.getExecutor())) {
            List<String> userNameList = Arrays.asList(taskType.getExecutor().split(","));
            Map<String, String> userMap = sysUserService.getUserInfoMap(userNameList);
            // 拆分 executor 并将对应的值拼接
            String executorName = Arrays.stream(taskType.getExecutor().split(","))
                    .map(userMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));
            taskType.setExecutorName(executorName);
        }

        return success(taskType);
    }

    /**
     * 查看任务排期
     */
    @GetMapping(value = "/getTaskScheduleList")
    @ApiOperation("查看任务排期")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskTypeId", value = "任务类型id", required = true, dataType = "Long", paramType = "query"),
    })
    public AjaxResult getTaskScheduleList(Long taskTypeId) {
        JSONArray result = new JSONArray();
        List<Task> taskScheduleList = taskService.getTaskScheduleList(taskTypeId);
        List<String> userNameList = taskScheduleList.stream()
                .map(Task::getExecutor)
                .filter(executor -> StringUtils.isNotEmpty(executor))
                .flatMap(executor -> Arrays.stream(executor.split(",")))
                .collect(Collectors.toList());
        Map<String, String> userMap = sysUserService.getUserInfoMap(userNameList);

        for (Task task : taskScheduleList) {
            JSONObject obj = new JSONObject();
            JSONArray executorNameArray = new JSONArray();

            List<String> taskSplit = Arrays.asList(task.getTaskInfoExecutors().split(","));
            for (String info : taskSplit) {
                JSONObject infoObj = new JSONObject();
                // 任务id__status__executor字符串拼接
                String[] infoSplit = info.split("__");
                if (infoSplit != null && infoSplit.length > 0) {
                    infoObj.put("id", infoSplit[0]);
                }
                if (infoSplit != null && infoSplit.length > 1) {
                    infoObj.put("status", infoSplit[1]);
                }
                if (infoSplit != null && infoSplit.length > 2) {
                    String executor = infoSplit[2];
                    if (StringUtils.isNotEmpty(executor)) {
                        String executorName = userMap.get(executor);
                        infoObj.put("executor", infoSplit[2]);
                        infoObj.put("executorName", executorName);
                    }
                }
                executorNameArray.add(infoObj);
            }
            obj.put("deadline", task.getStartDatetime());
            obj.put("taskFileStrIds", task.getTaskFileStrIds());
            obj.put("executorNameData", executorNameArray);
            result.add(obj);
        }
        return success(result);
    }

    /**
     * 新增任务类型
     */
    @Log(title = "任务类型", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增任务类型")
    public AjaxResult add(@RequestBody TaskType taskType) {
        if (null != taskType.getPeriodicStatus() && 1 == taskType.getPeriodicStatus()) {
            if (null == taskType.getStartTime()) {
                return error("开始时间必填");
            }
            // 当前日期 2024-09-01 00:00:00
            Date currentDate = DateUtil.beginOfDay(DateUtil.date());
            if (taskType.getStartTime().compareTo(currentDate) <= 0) {
                return error("开始时间必须大于今天日期");
            }
            if (taskType.getAdvanceTime() != null) {
                DateTime dateTime = DateUtil.offsetDay(taskType.getStartTime(), -taskType.getAdvanceTime());
                if (dateTime.compareTo(currentDate) <= 0) {
                    return error("任务提前生成时间必须大于今天日期");
                }
            }
        }
        return toAjax(taskTypeService.insertTaskType(taskType));
    }

    /**
     * 修改任务类型
     */
    @Log(title = "任务类型", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改任务类型")
    public AjaxResult edit(@RequestBody TaskType taskType) {
        if (null != taskType.getPeriodicStatus() && 1 == taskType.getPeriodicStatus()) {
            if (null == taskType.getStartTime()) {
                return error("开始时间必填");
            }
            // 当前日期 2024-09-01 00:00:00
            Date currentDate = DateUtil.beginOfDay(DateUtil.date());
            if (taskType.getStartTime().compareTo(currentDate) <= 0) {
                return error("开始时间必须大于今天日期");
            }
            if (taskType.getAdvanceTime() != null) {
                DateTime dateTime = DateUtil.offsetDay(taskType.getStartTime(), -taskType.getAdvanceTime());
                if (dateTime.compareTo(currentDate) <= 0) {
                    return error("任务提前生成时间必须大于今天日期");
                }
            }
            // 重置下一次执行时间
            taskType.setNextStartTime(taskType.getStartTime());
        }
        return toAjax(taskTypeService.updateTaskType(taskType));
    }

    /**
     * 删除任务类型
     */
    @Log(title = "任务类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除任务类型")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(taskTypeService.deleteTaskTypeByIds(ids));
    }

    /**
     * 下载上报文件
     */
    @GetMapping("/download")
    @ApiOperation("下载上报文件")
    public JSONObject download(HttpServletResponse response, String taskFileStrIds) {
        JSONObject result = new JSONObject();
        try {
            if (StringUtils.isEmpty(taskFileStrIds)) {
                result.put("code", 500);
                result.put("msg", "无下载文件");
                return result;
            }
            String[] fileNameArray = Convert.toStrArray(taskFileStrIds);
            List<Long> longList = Arrays.stream(fileNameArray).map(Long::valueOf).collect(Collectors.toList());
            TaskScheduleFile taskScheduleFile = new TaskScheduleFile();
            taskScheduleFile.setIds(longList);
            List<TaskScheduleFile> taskFileList = taskScheduleFileService.selectTaskScheduleFileList(taskScheduleFile);

            if (StringUtils.isEmpty(taskFileList)) {
                result.put("code", 500);
                result.put("msg", "无下载文件");
                return result;
            }
            List<String> fileNameList = taskFileList.stream().map(item -> item.getFilePath()).collect(Collectors.toList());
            byte[] data = downloadFile(fileNameList);
            response.reset();
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment; filename=\"download.zip\"");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream; charset=UTF-8");
            IOUtils.write(data, response.getOutputStream());
            return null;
        } catch (Exception e) {
            logger.error("=====> 下载上报文件-出错", e);
            result.put("code", 500);
            result.put("msg", "下载上报文件出错");
            return result;
        }
    }

    private byte[] downloadFile(List<String> fileNameList) throws Exception {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(outputStream);
        // 本地资源路径 /home/<USER>/code/profile
        String localPath = BaseConfig.getProfile();
        for (String fileName : fileNameList) {
            // fileName /profile/upload/2024/07/15/ico_20240715122858A044.png
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(fileName, Constants.RESOURCE_PREFIX);
            InputStream fis = new FileInputStream(new File(downloadPath));
            // 获取文件名，不包含路径
            String zipEntryName = new File(downloadPath).getName();
            // 添加到zip
            zip.putNextEntry(new ZipEntry(zipEntryName));
            // 读取文件内容并写入到zip输出流中
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zip.write(buffer, 0, length);
            }
            zip.flush();
            zip.closeEntry();
        }
        IOUtils.closeQuietly(zip);
        return outputStream.toByteArray();
    }
}
