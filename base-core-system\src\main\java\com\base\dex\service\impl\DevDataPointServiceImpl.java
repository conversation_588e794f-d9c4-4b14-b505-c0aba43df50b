package com.base.dex.service.impl;

import com.base.common.enums.TimeType;
import com.base.common.utils.DateUtils;
import com.base.common.utils.StringUtils;
import com.base.device.domain.EnvDevicePoint;
import com.base.device.service.IEnvDevicePointService;
import com.base.dex.domain.DevBasePoint;
import com.base.dex.domain.DevDataPointMin;
import com.base.dex.mapper.DevDataPointMinMapper;
import com.base.dex.service.IDevBasePointService;
import com.base.dex.service.IDevDataPointMinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 点位分钟数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class DevDataPointServiceImpl implements IDevDataPointMinService {
    @Autowired
    private DevDataPointMinMapper devDataPointMinMapper;

    @Autowired
    private IotDBService iotDBService;

    @Autowired
    private IDevBasePointService devBasePointService;

    @Autowired
    private IEnvDevicePointService envDevicePointService;

    /**
     * 查询点位分钟数据
     *
     * @param pointId 点位分钟数据主键
     * @return 点位分钟数据
     */
    @Override
    public DevDataPointMin selectDevDataPointMinByPointId(String pointId) {
        return devDataPointMinMapper.selectDevDataPointMinByPointId(pointId);
    }

    /**
     * 查询点位分钟数据列表
     *
     * @param devDataPointMin 点位分钟数据
     * @return 点位分钟数据
     */
    @Override
    public List<DevDataPointMin> selectDevDataPointMinList(DevDataPointMin devDataPointMin) {
        return devDataPointMinMapper.selectDevDataPointMinList(devDataPointMin);
    }

    /**
     * 新增点位分钟数据
     *
     * @param devDataPointMin 点位分钟数据
     * @return 结果
     */
    @Override
    public int insertDevDataPointMin(DevDataPointMin devDataPointMin) {
        devDataPointMin.setCreateTime(DateUtils.getNowDate());
        return devDataPointMinMapper.insertDevDataPointMin(devDataPointMin);
    }

    /**
     * 修改点位分钟数据
     *
     * @param devDataPointMin 点位分钟数据
     * @return 结果
     */
    @Override
    public int updateDevDataPointMin(DevDataPointMin devDataPointMin) {
        return devDataPointMinMapper.updateDevDataPointMin(devDataPointMin);
    }

    /**
     * 批量删除点位分钟数据
     *
     * @param pointIds 需要删除的点位分钟数据主键
     * @return 结果
     */
    @Override
    public int deleteDevDataPointMinByPointIds(String[] pointIds) {
        return devDataPointMinMapper.deleteDevDataPointMinByPointIds(pointIds);
    }

    /**
     * 删除点位分钟数据信息
     *
     * @param pointId 点位分钟数据主键
     * @return 结果
     */
    @Override
    public int deleteDevDataPointMinByPointId(String pointId) {
        return devDataPointMinMapper.deleteDevDataPointMinByPointId(pointId);
    }

    /**
     * 获取点位分钟数据列表
     *
     * @param devDataPointMin 点位分钟数据
     * @return 点位分钟数据集合
     */
    @Override
    public List<DevDataPointMin> getDevDataPointMinList(DevDataPointMin devDataPointMin) {
        return devDataPointMinMapper.getDevDataPointMinList(devDataPointMin);
    }

    public Map<String, Object> getIotDataPointList(String pointId, String timeTypeStr, Date startTime, Date endTime) {
        // 处理 timeTypeStr
        timeTypeStr = timeTypeStr.replace("his", "min");
        String freq;
        if (timeTypeStr.contains("_")) {
            String[] parts = timeTypeStr.split("_");
            freq = parts[0];
            timeTypeStr = parts[1];
        } else {
            freq = "1";
        }
        TimeType timeType = TimeType.fromType(timeTypeStr);

        // 查询基础点位信息
        DevBasePoint devBasePoint = devBasePointService.selectDevBasePointByPointId(pointId);
        String viewFormula = devBasePoint.getViewFormula();

        // 构造最终 end_time（模拟 eval 表达式逻辑）
        endTime = DateUtils.compute(endTime, timeType, 1); // 模拟 get_datetime(..., second=-1)

        // 构造 SQL 查询语句
        String sql = StringUtils.format("SELECT max_value(value) as value FROM point_{}.{} " +
                "GROUP BY ([{}, {}), {}{}) ORDER BY time ASC",
                timeTypeStr, pointId, DateUtils.formatDateTime(startTime), DateUtils.formatDateTime(endTime), freq, timeType.getIotType());

        // 执行 IoTDB 查询
        List<Map<String, Object>> data = iotDBService.executeQueryStatement(sql);

        // 构建查询结果映射
        Map<String, Double> dataDict = new HashMap<>();
        for (Map<String, Object> item : data) {
            Date time = (Date) item.get("time");
            dataDict.put(DateUtils.formatDateTime(time), Double.parseDouble(item.get("value").toString()));
        }

        // 初始化返回列表
        List<Double> valueList = new ArrayList<>();
        List<String> viewValueList = new ArrayList<>();
//        List<Date> timeList = new ArrayList<>();
        List<Double> lowLimitList = new ArrayList<>();

        // 获取低限值
        List<EnvDevicePoint> envDevicePoints = envDevicePointService.selectEnvDevicePointList(new EnvDevicePoint().setPointId(pointId));
        Double lowLimit = StringUtils.isNotEmpty(envDevicePoints) ? (Double) envDevicePoints.get(0).getLowLimit() : null;

        // 遍历时间范围并填充数据
        List<LocalDateTime> localDateTimes = DateUtils.generateRange(startTime, endTime, timeType.getStep(Integer.parseInt(freq)));
        localDateTimes = localDateTimes.subList(0, localDateTimes.size() - 1);
        for (LocalDateTime time : localDateTimes) {
            String timeKey = DateUtils.formatDateTime(time);
            Double rawValue = dataDict.get(timeKey);
            double value = 0.0;

            String viewValue;
            if (rawValue == null || Double.isNaN(rawValue)) {
                viewValue = "-";
            } else {
                value = rawValue;
                viewValue = (viewFormula != null) ? evaluateFormula(viewFormula, value) : String.valueOf(value);
            }

            valueList.add(value);
            viewValueList.add(viewValue);
//            timeList.add(timeKey);
            if (lowLimit != null) {
                lowLimitList.add(lowLimit);
            }
        }

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("value_list", valueList);
        result.put("view_value_list", viewValueList);
        result.put("low_limit_list", lowLimitList);
        return result;
    }


    // 模拟公式计算
    private String evaluateFormula(String formula, double value) {
        return formula.replace("{}", String.valueOf(value));
    }

    // 格式化时间
    private String formatTime(LocalDateTime time) {
        return "'" + time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "'";
    }

    // 模拟 TimeType 工具类


}
