package com.base.system.service.impl;

import com.base.system.mapper.TodoReadStatusMapper;
import com.base.system.domain.TodoReadStatus;
import com.base.system.service.ITodoReadStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 待办事项已读状态 服务层实现
 */
@Service
public class TodoReadStatusServiceImpl implements ITodoReadStatusService {
    @Autowired
    private TodoReadStatusMapper todoReadStatusMapper;

    /**
     * 查询待办事项已读状态
     */
    @Override
    public TodoReadStatus selectTodoReadStatusById(Long readStatusId) {
        return todoReadStatusMapper.selectTodoReadStatusById(readStatusId);
    }

    /**
     * 查询待办事项已读状态列表
     */
    @Override
    public List<TodoReadStatus> selectTodoReadStatusList(TodoReadStatus todoReadStatus) {
        return todoReadStatusMapper.selectTodoReadStatusList(todoReadStatus);
    }

    /**
     * 新增待办事项已读状态
     */
    @Override
    public int insertTodoReadStatus(TodoReadStatus todoReadStatus) {
        return todoReadStatusMapper.insertTodoReadStatus(todoReadStatus);
    }

    /**
     * 修改待办事项已读状态
     */
    @Override
    public int updateTodoReadStatus(TodoReadStatus todoReadStatus) {
        return todoReadStatusMapper.updateTodoReadStatus(todoReadStatus);
    }

    /**
     * 删除待办事项已读状态
     */
    @Override
    public int deleteTodoReadStatusById(Long readStatusId) {
        return todoReadStatusMapper.deleteTodoReadStatusById(readStatusId);
    }

    /**
     * 批量删除待办事项已读状态
     */
    @Override
    public int deleteTodoReadStatusByIds(Long[] readStatusIds) {
        return todoReadStatusMapper.deleteTodoReadStatusByIds(readStatusIds);
    }
}
