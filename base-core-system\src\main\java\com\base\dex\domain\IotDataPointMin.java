package com.base.dex.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IotDataPointMin extends IotBaseRecord{

    private String pointId;

    private Double value;

    private Double sourceValue;

    private String viewValue;

    private String validFlag;

    private String pointFlag;

}
