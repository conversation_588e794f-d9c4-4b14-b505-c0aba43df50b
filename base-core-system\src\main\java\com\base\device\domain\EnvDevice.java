package com.base.device.domain;

import com.alibaba.fastjson2.JSONObject;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.base.dex.domain.DevBasePoint;
import com.base.system.domain.SysField;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 设备基础信息对象 env_device
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public class EnvDevice extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 设备名称 */
    @JsonAlias("device_name")
    @Excel(name = "设备名称")
    @NotBlank(message="设备名称不能为空", groups = {EnvDeviceAdd.class})
    private String deviceName;

    /** 设备编号 */
    @JsonAlias("e9_code")
    @Excel(name = "设备编号")
    @NotBlank(message="设备编号不能为空", groups = {EnvDeviceAdd.class})
    private String e9Code;

    /** 所属分厂/工序/工段 */
    @JsonAlias("process_id")
    @Excel(name = "所属分厂/工序/工段")
    @NotBlank(message="所属区域不能为空", groups = {EnvDeviceAdd.class})
    private Long processId;

    /** X坐标 */
    @JsonAlias("pos_x")
    @Excel(name = "X坐标")
    private Double posX;

    /** Y坐标 */
    @JsonAlias("pos_y")
    @Excel(name = "Y坐标")
    private Double posY;

    /** Z坐标 */
    @JsonAlias("pos_z")
    @Excel(name = "Z坐标")
    private Double posZ;

    /** 设备主类(生产/治理/监测...) 对应字典:device_main_type */
    @JsonAlias("main_type")
    @Excel(name = "设备主类(生产/治理/监测...) 对应字典:device_main_type")
    private String mainType;

    /** 设备类型(输送设备/除尘器/TSP监测仪表...) 对应字典:device_sub_type */
    @JsonAlias("sub_type")
    @Excel(name = "设备类型(输送设备/除尘器/TSP监测仪表...) 对应字典:device_sub_type")
    @NotBlank(message="设备类型不能为空", groups = {EnvDeviceAdd.class})
    private String subType;

    /** 设备IP */
    @JsonAlias("ip")
    @Excel(name = "设备IP")
    private String ip;

    /** 设备端口 */
    @JsonAlias("port")
    @Excel(name = "设备端口")
    private String port;

    /** 显示状态（0显示 1隐藏） */
    @JsonAlias("show_flag")
    @Excel(name = "显示状态", readConverterExp = "0=显示,1=隐藏")
    private Long showFlag;

    /** 删除标志（0代表存在 2代表删除） */
    @JsonAlias("del_flag")
    private Long delFlag;

    /** 排序 */
    @JsonAlias("sort")
    @Excel(name = "排序")
    private Long sort;

    /** 设备运行状态 字典 device_run_state */
    @JsonAlias("run_state")
    @Excel(name = "设备运行状态 字典 device_run_state")
    private String runState;

    /** 动态字段KEY */
    @JsonAlias("field_key")
    @Excel(name = "动态字段KEY")
    private String fieldKey;

    /** 所属部门 */
    @JsonAlias("dept_id")
    @Excel(name = "所属部门")
    private Long deptId;

    /** $column.columnComment */
    @JsonAlias("field_json")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private JSONObject fieldJson;

    /** 所在位置 */
    @JsonAlias("location")
    @Excel(name = "所在位置")
    private String location;

    /** $column.columnComment */
    @JsonAlias("device_id")
    @NotNull(message = "找不到设备", groups = {EnvDeviceEdit.class})
    private Long deviceId;

    @JsonAlias("format_json")
    private List<SysField> formatJson;

    /**
     * 默认查询分类
     */
    @JsonAlias("scene")
    private String scene = "env_device";

    /**
     * 绑定的因子
     */
    @JsonAlias("point_names")
    private String pointNames;

    /**
     * 绑定的因子数量
     */
    @JsonAlias("point_count")
    private String pointCount;

    /**
     * 查询哪些设备ID
     */
    @JsonAlias("device_id_list")
    private List<Long> deviceIdList;

    /**
     * 排序规则
     */
    @JsonAlias("order_by")
    private String orderBySort = "asc";

    /**
     * 排序字段
     */
    @JsonAlias("order_by_field")
    private String orderByField = "sort";

    /**
     * 查询条件
     */
    @JsonAlias("query_str")
    private String queryStr;

    /**
     * 主类集合
     */
    @JsonAlias("main_type_list")
    private List<String> mainTypeList;

    /**
     * 子类集合
     */
    @JsonAlias("sub_type_list")
    private List<String> subTypeList;

    /**
     * 动态字段场景
     */
    @JsonAlias("field_scene")
    private String fieldScene;

    /**
     * 区域集合
     */
    @JsonAlias("process_id_list")
    private List<Long> processIdList;

    private List<Long> delFlagList;

    private String uuid;

    private List<DevBasePoint> pointList;

    private String msg;

    public interface EnvDeviceAdd{

    }

    public interface EnvDeviceEdit{

    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("deviceName", getDeviceName())
            .append("e9Code", getE9Code())
            .append("processId", getProcessId())
            .append("posX", getPosX())
            .append("posY", getPosY())
            .append("posZ", getPosZ())
            .append("mainType", getMainType())
            .append("subType", getSubType())
            .append("ip", getIp())
            .append("port", getPort())
            .append("showFlag", getShowFlag())
            .append("delFlag", getDelFlag())
            .append("sort", getSort())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("runState", getRunState())
            .append("fieldKey", getFieldKey())
            .append("deptId", getDeptId())
            .append("fieldJson", getFieldJson())
            .append("location", getLocation())
            .append("deviceId", getDeviceId())
            .toString();
    }
}
