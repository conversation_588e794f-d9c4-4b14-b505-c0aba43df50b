package com.base.web.controller.product;

import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.system.domain.Product;
import com.base.system.service.IProductInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/product")
public class ProductController extends BaseController {

    @Autowired
    private IProductInfoService productInfoService;

    // 新增接口，返回产品列表中的produceList
    @GetMapping("/list")
    public AjaxResult getProductList(String keyword) {
        List<Product> productList = productInfoService.getProductList(keyword);
        return success(productList); // 返回产品列表
    }

    // 新增接口：标记产品为已读
    @PostMapping("/read")
    public AjaxResult markProductAsRead(@RequestParam("productId") int productId) {
        productInfoService.markProductAsRead(productId); // 调用服务层方法
        return success("产品已标记为已读");
    }

}
