package com.base.system.service.impl;

import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.domain.entity.SysSceneDict;
import com.base.common.utils.DictUtils;
import com.base.common.utils.StringUtils;
import com.base.system.mapper.SysSceneDictMapper;
import com.base.system.service.ISysSceneDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 菜单-字典 关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Service
public class SysSceneDictServiceImpl implements ISysSceneDictService
{
    @Autowired
    private SysSceneDictMapper sysSceneDictMapper;

    /**
     * 查询菜单-字典 关联关系
     *
     * @param joinId 菜单-字典 关联关系主键
     * @return 菜单-字典 关联关系
     */
    @Override
    public SysSceneDict selectSysSceneDictByJoinId(Long joinId)
    {
        return sysSceneDictMapper.selectSysSceneDictByJoinId(joinId);
    }

    /**
     * 查询菜单-字典 关联关系列表
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 菜单-字典 关联关系
     */
    @Override
    public List<SysSceneDict> selectSysSceneDictList(SysSceneDict sysSceneDict)
    {
        return sysSceneDictMapper.selectSysSceneDictList(sysSceneDict);
    }

    /**
     * 新增菜单-字典 关联关系
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 结果
     */
    @Override
    public int insertSysSceneDict(SysSceneDict sysSceneDict)
    {
        int res = sysSceneDictMapper.insertSysSceneDict(sysSceneDict);
        if (res > 0){
            this.reloadCache(sysSceneDict.getScene());
        }
        return res;
    }

    /**
     * 修改菜单-字典 关联关系
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 结果
     */
    @Override
    public int updateSysSceneDict(SysSceneDict sysSceneDict)
    {
        int res = sysSceneDictMapper.updateSysSceneDict(sysSceneDict);
        if (res > 0){
            this.reloadCache(sysSceneDict.getScene());
        }
        return res;
    }

    /**
     * 批量删除菜单-字典 关联关系
     *
     * @param joinIds 需要删除的菜单-字典 关联关系主键
     * @return 结果
     */
    @Override
    public int deleteSysSceneDictByJoinIds(Long[] joinIds)
    {
        int res = 0;
        for (Long joinId : joinIds)
        {
            res += this.deleteSysSceneDictByJoinId(joinId);
        }
        return res;
    }

    /**
     * 删除菜单-字典 关联关系信息
     *
     * @param joinId 菜单-字典 关联关系主键
     * @return 结果
     */
    @Override
    public int deleteSysSceneDictByJoinId(Long joinId)
    {
        SysSceneDict data = this.selectSysSceneDictByJoinId(joinId);
        int res = this.sysSceneDictMapper.deleteSysSceneDictByJoinId(joinId);
        this.reloadCache(data.getScene());
        return res;
    }

    /**
     * 刷新数据方法。
     *
     * 根据提供的场景和字典类型，先删除所有相关记录，然后插入新的字典代码列表。
     *
     * @param scene       场景标识
     * @param dictType    字典类型
     * @param dictCodeList 新的字典代码列表
     */
    @Override
    public void refreshData(String scene, String dictType, List<Long> dictCodeList) {
        // 删除所有与指定场景和字典类型相关的记录
        sysSceneDictMapper.deleteBySceneAndDictType(scene, dictType);

        ArrayList<SysSceneDict> dictArrayList = new ArrayList<SysSceneDict>();
        // 插入新的字典代码列表
        if (dictCodeList != null && !dictCodeList.isEmpty()) {
            for (Long dictCode : dictCodeList) {
                SysSceneDict sysSceneDict = new SysSceneDict();
                sysSceneDict.setScene(scene);
                sysSceneDict.setDictType(dictType);
                sysSceneDict.setDictCode(dictCode);
                dictArrayList.add(sysSceneDict);
            }
        }
        if (StringUtils.isNotEmpty(dictArrayList)){
            sysSceneDictMapper.batchSysSceneDict(dictArrayList);
        }

        // 清理场景缓存（如果需要的话）
//        cleanSceneCache(scene);
    }

    /**
     * 根据scene数组查询菜单-字典 关联关系列表
     *
     * @param scenes 场景数组
     * @return 菜单-字典 关联关系集合
     */
    @Override
    public List<SysSceneDict> selectSysSceneDictListByScenes(List<String> scenes) {
        if (StringUtils.isEmpty(scenes)){
            return new ArrayList<>();
        }
        ArrayList<SysSceneDict> sysSceneDicts = new ArrayList<>();
        scenes.forEach(scene -> {
            sysSceneDicts.addAll(this.selectSysSceneDictByScene(scene));
        });
        return sysSceneDicts;
    }

    @Override
    public void insertSysSceneByDict(SysDictData dict) {
        if (StringUtils.isNotBlank(dict.getScene())){
            SysSceneDict sysSceneDict = new SysSceneDict().setScene(dict.getScene()).setDictType(dict.getDictType()).setDictCode(dict.getDictCode());
            this.insertSysSceneDict(sysSceneDict);
        }
    }

    @Override
    public void reloadCache(String scene) {
        List<SysSceneDict> sysSceneDicts = this.selectSysSceneDictList(new SysSceneDict().setScene(scene));
        DictUtils.setSceneDictCache(scene, sysSceneDicts);
    }

    public List<SysSceneDict> selectSysSceneDictByScene(String scene){
        return DictUtils.getSceneDictCache(scene);
    }

    @Override
    public SysSceneDict selectChild(String parentType, String childType, String childValue){
        // Step 1: 查询 scene_list
        List<SysSceneDict> sceneList = sysSceneDictMapper.findByChildTypeAndValue(childType, childValue);

        // Step 2: 获取 main_type_dict
        Map<String, String> mainTypeDict = DictUtils.getDictValueLabelMap(parentType);

        // Step 3: 遍历查找匹配项
        for (SysSceneDict scene : sceneList) {
            if (mainTypeDict.containsKey(scene.getScene())) {
                return scene;
            }
        }

        // Step 4: 默认返回空对象
        return new SysSceneDict();
    }

}
