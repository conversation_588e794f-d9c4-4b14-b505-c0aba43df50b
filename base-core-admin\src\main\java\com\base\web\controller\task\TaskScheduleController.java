package com.base.web.controller.task;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.task.domain.TaskSchedule;
import com.base.task.service.ITaskScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 任务进度Controller
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Api(tags = "任务管理")
@RestController
@RequestMapping("/api/task/taskSchedule")
public class TaskScheduleController extends BaseController {
    @Autowired
    private ITaskScheduleService taskScheduleService;

    /**
     * 新增任务进度
     */
    @Log(title = "任务进度", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增任务进度")
    public AjaxResult add(@RequestBody TaskSchedule taskSchedule) {
        return toAjax(taskScheduleService.insertTaskSchedule(taskSchedule));
    }

}
