package com.base.common.annotation;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MqttSubscribe {
    /**
     * 需要监听的topic
     * 单级通配符（+）：用于匹配主题中的单个层级。例如，主题为 “home/+/temperature”，它可以匹配 “home/livingroom/temperature”、“home/bedroom/temperature” 等主题，但不能匹配 “home/livingroom/light/temperature”，因为 “+” 只能匹配一个层级。
     * 多级通配符（#）：用于匹配主题中的多个层级。例如，主题为 “home/#”，它可以匹配 “home/livingroom/temperature”、“home/bedroom/light”、“home/livingroom/light/temperature” 等所有以 “home/” 开头的主题。
     * @return
     */
    String topic();

    /**
     * 默认质量等级, 0为只管发送, 1为保证收到一次, 2为保证收到, 但是可能不止收到一次
     * @return
     */
    int qos() default 1;
}
