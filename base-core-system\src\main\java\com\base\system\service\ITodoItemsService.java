package com.base.system.service;

import com.base.system.domain.TodoItems;
import com.base.system.domain.vo.TodoItemsQueryVO;
import com.base.system.domain.vo.TodoItemsVO;

import java.util.List;
import java.util.Map;

/**
 * 待办事项 服务层接口
 */
public interface ITodoItemsService {
    /**
     * 查询待办事项
     *
     * @param todoId 待办事项ID
     * @return 待办事项
     */
    public TodoItems selectTodoItemsById(Long todoId);

    /**
     * 查询待办事项列表
     *
     * @param todoItems 待办事项
     * @return 待办事项集合
     */
    public List<TodoItems> selectTodoItemsList(TodoItems todoItems);

    /**
     * 新增待办事项
     *
     * @param todoItems 待办事项
     * @return 结果
     */
    public int insertTodoItems(TodoItems todoItems);

    /**
     * 修改待办事项
     *
     * @param todoItems 待办事项
     * @return 结果
     */
    public int updateTodoItems(TodoItems todoItems);

    /**
     * 查询待办事项详情
     *
     * @param todoId 待办事项ID
     * @return 待办事项详情
     */
    public TodoItemsVO selectTodoItemsDetailById(Long todoId);

    /**
     * 根据ID删除待办事项
     *
     * @param todoId 待办事项ID
     * @return 结果
     */
    public int deleteTodoItemsById(Long todoId);

    /**
     * 批量删除待办事项
     *
     * @param todoIds 需要删除的待办事项ID集合
     * @return 结果
     */
    public int deleteTodoItemsByIds(Long[] todoIds);

    /**
     * 查询待办事项列表及其已读状态
     *
     * @param queryVO 查询条件（新增排序字段）
     * @return 待办事项列表及其已读状态
     */
    public List<TodoItemsVO> selectTodoItemsListWithReadStatus(TodoItemsQueryVO queryVO);

    /**
     * 根据查询条件获取待办事项列表
     *
     * @param queryVO 查询条件
     * @return 待办事项列表
     */
    public List<TodoItems> selectTodoItemsByQuery(TodoItemsQueryVO queryVO);

    /**
     * 统计待办事项状态的任务数量
     *
     * @return 各状态任务数量统计结果
     */
    public Map<String, Integer> getTodoStatusStatistics();
}
