package com.base.web.controller.api;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.utils.DateUtils;
import com.base.common.utils.StringUtils;
import com.base.dex.domain.DevDataPointHour;
import com.base.dex.service.IDevDataPointHourService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 地图模块 Controller
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping(value = "/api/analysis")
@Api(tags = "地图模块")
public class AnalysisController extends BaseController {

    @Autowired
    private IDevDataPointHourService devDataPointHourService;

    @GetMapping("/getHeatmapAnalysis")
    @ApiOperation(value = "热力图分析", httpMethod = "GET", tags = "热力图分析", notes = "")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", required = true, value = "监测指标：pm2_5,pm10", dataType = "String", paramType = "query", defaultValue = "pm_25"),
            @ApiImplicitParam(name = "startDate", required = true, value = "开始时间，格式：yyyy-MM-dd", dataType = "String", paramType = "query")})
    public AjaxResult getHeatmapAnalysis(String type, String startDate) {
        if (StringUtils.isEmpty(type)) {
            return AjaxResult.error("监测指标：pm2_5或pm10必填");
        }
        try {
            Date date = DateUtil.parse(startDate);
            // 监测指标：PM2.5、PM10 查询整个厂区PM2.5、PM10的小时均值
            DevDataPointHour devDataPointHour = new DevDataPointHour();
            JSONArray hourMapArray = new JSONArray();
            List<String> hours = DateUtils.getBetweenHours(DateUtil.format(DateUtil.parse(startDate), "yyyy-MM-dd HH"),
                    DateUtil.format(DateUtil.endOfDay(DateUtil.parse(startDate)), "yyyy-MM-dd HH"));
            long time = System.currentTimeMillis();
            for (String hour : hours) {
                // 开始日期 yyyy-MM-dd HH
                devDataPointHour.setHour(hour);
                devDataPointHour.setFactor(type);
                // 查询颗粒物小时数据列表
                List<DevDataPointHour> hourList = devDataPointHourService.getDevDataPointHourList(devDataPointHour);

                JSONObject imgObj = devDataPointHourService.getHeatMapImg(hourList, 1024, type, date, hour + "_");
                hourMapArray.add(imgObj);
            }
            long end = System.currentTimeMillis() - time;
            logger.info("==========> 生成24小时热力图用时：" + (end / 1000) + "秒 <========== ");
            // 24小时热力图数据
            return AjaxResult.success(hourMapArray);
        } catch (Exception e) {
            logger.error("=====> 生成热力图出错", e);
            return AjaxResult.error("生成热力图出错");
        }
    }

}
