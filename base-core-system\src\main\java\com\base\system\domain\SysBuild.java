package com.base.system.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.Date;

/**
 * 系统建筑物实体类
 * 用于存储和传输建筑物相关信息
 */
@Data
public class SysBuild {
    /**
     * 建筑物ID
     */
    @JsonAlias("build_id")
    private String buildId;

    /**
     * 建筑物名称
     */
    @JsonAlias("build_name")
    private String buildName;

    /**
     * X坐标
     */
    @JsonAlias("pos_x")
    private Float posX;

    /**
     * Y坐标
     */
    @JsonAlias("pos_y")
    private Float posY;

    /**
     * Z坐标
     */
    @JsonAlias("pos_z")
    private Float posZ;

    /**
     * 排序值
     */
    @JsonAlias("sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @JsonAlias("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @JsonAlias("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @JsonAlias("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @JsonAlias("update_by")
    private String updateBy;

    /**
     * 删除标志
     */
    @JsonAlias("del_flag")
    private Integer delFlag;

    /**
     * 备注
     */
    @JsonAlias("remark")
    private String remark;
}