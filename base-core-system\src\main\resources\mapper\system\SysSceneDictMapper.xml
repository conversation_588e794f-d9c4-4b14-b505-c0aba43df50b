<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.SysSceneDictMapper">

    <resultMap type="SysSceneDict" id="SysSceneDictResult">
        <result property="scene"    column="scene"    />
        <result property="dictType"    column="dict_type"    />
        <result property="dictLabel"    column="dict_label"    />
        <result property="joinId"    column="join_id"    />
        <result property="dictCode"    column="dict_code"    />
    </resultMap>

    <sql id="selectSysSceneDictVo">
        select scene, dict_type, dict_label, join_id, dict_code from sys_scene_dict
    </sql>

    <select id="selectSysSceneDictList" parameterType="SysSceneDict" resultMap="SysSceneDictResult">
        <include refid="selectSysSceneDictVo"/>
        <where>
            <if test="scene != null  and scene != ''"> and scene = #{scene}</if>
            <if test="dictType != null  and dictType != ''"> and dict_type = #{dictType}</if>
            <if test="dictLabel != null  and dictLabel != ''"> and dict_label = #{dictLabel}</if>
            <if test="dictCode != null "> and dict_code = #{dictCode}</if>
        </where>
    </select>

    <select id="selectSysSceneDictByJoinId" parameterType="Long" resultMap="SysSceneDictResult">
        <include refid="selectSysSceneDictVo"/>
        where join_id = #{joinId}
    </select>

    <insert id="insertSysSceneDict" parameterType="SysSceneDict">
        insert into sys_scene_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scene != null and scene != ''">scene,</if>
            <if test="dictType != null and dictType != ''">dict_type,</if>
            <if test="dictLabel != null">dict_label,</if>
            <if test="joinId != null">join_id,</if>
            <if test="dictCode != null">dict_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scene != null and scene != ''">#{scene},</if>
            <if test="dictType != null and dictType != ''">#{dictType},</if>
            <if test="dictLabel != null">#{dictLabel},</if>
            <if test="joinId != null">#{joinId},</if>
            <if test="dictCode != null">#{dictCode},</if>
         </trim>
    </insert>

    <update id="updateSysSceneDict" parameterType="SysSceneDict">
        update sys_scene_dict
        <trim prefix="SET" suffixOverrides=",">
            <if test="scene != null and scene != ''">scene = #{scene},</if>
            <if test="dictType != null and dictType != ''">dict_type = #{dictType},</if>
            <if test="dictLabel != null">dict_label = #{dictLabel},</if>
            <if test="dictCode != null">dict_code = #{dictCode},</if>
        </trim>
        where join_id = #{joinId}
    </update>

    <delete id="deleteSysSceneDictByJoinId" parameterType="Long">
        delete from sys_scene_dict where join_id = #{joinId}
    </delete>

    <delete id="deleteSysSceneDictByJoinIds" parameterType="String">
        delete from sys_scene_dict where join_id in
        <foreach item="joinId" collection="array" open="(" separator="," close=")">
            #{joinId}
        </foreach>
    </delete>

    <delete id="deleteBySceneAndDictType" parameterType="map">
        DELETE FROM sys_scene_dict
        WHERE scene = #{scene}
          AND dict_type = #{dictType}
    </delete>

    <insert id="batchSysSceneDict">
        insert into sys_scene_dict( scene, dict_type, dict_label, dict_code) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.scene}, #{item.dictType}, #{item.dictLabel}, #{item.dictCode})
        </foreach>
    </insert>

    <select id="selectSysSceneDictListByScenes" resultMap="SysSceneDictResult">
        SELECT
        join_id,
        scene,
        dict_type,
        dict_code,
        dict_label,
        join_status
        FROM
        sys_scene_dict
        WHERE
        scene IN
        <foreach item="scene" index="index" collection="scenes" open="(" separator="," close=")">
            #{scene}
        </foreach>
    </select>

    <select id="findByChildTypeAndValue" resultMap="SysSceneDictResult">
        SELECT ssd.*
        FROM sys_scene_dict ssd
                 JOIN sys_dict_data sdd
                      ON ssd.dict_code = sdd.dict_code
        WHERE sdd.dict_type = #{childType}
          AND sdd.dict_value = #{childValue};
    </select>


</mapper>
