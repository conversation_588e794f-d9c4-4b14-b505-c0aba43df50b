package com.base.web.controller.flowable;

import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.TreeSelect;
import com.base.common.core.domain.entity.SysDept;
import com.base.common.core.domain.entity.SysUser;
import com.base.system.domain.TodoItems;
import com.base.system.domain.dto.BpmnCallBackDto;
import com.base.system.service.ISysDeptService;
import com.base.system.service.ISysUserService;
import com.base.system.service.ITodoItemsService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/17
 */
@Api(tags = "工作流外部对接接口")
@RestController
@RequestMapping("/flowable")
public class FlowableServerController extends BaseController {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ITodoItemsService todoItemsService;

    @GetMapping("/getUserList")
    public AjaxResult getUserList(String clientId) {
        return AjaxResult.success(sysUserService.selectAllUser());
    }

    @GetMapping("/getDeptTree")
    public AjaxResult getDeptTree(String clientId) {
        List<SysDept> deptList = deptService.selectAllDeptList();
        List<TreeSelect> treeSelects = deptService.buildDeptTreeSelect(deptList);
        return AjaxResult.success(treeSelects);
    }

    /**
     * 审批回调接口
     *
     * @param callbackDto 回调参数
     * @return 操作结果
     */
    @PostMapping("/callback")
    public AjaxResult handleApproveCallback(@RequestBody BpmnCallBackDto callbackDto) {
        String eventType = callbackDto.getEventType();
        // 更新任务待办事项状态为已完成
        String extendId = callbackDto.getProcessInstanceId() + "_" + callbackDto.getTaskInstanceId();
        SysUser createUser = sysUserService.selectUserById(Long.parseLong(callbackDto.getCreateId()));
        String createUserName = "无";
        if (createUser != null) {
            createUserName = createUser.getNickName();
        }
        String title = createUserName + "发起的" + callbackDto.getCategoryName() + "类型的审批";
        if ("bpmn_instance_start".equals(eventType)) {
            // 创建新待办事项，接收人设为 createId
            TodoItems todoItem = new TodoItems();
            todoItem.setTitle(title);
            todoItem.setContent("您的审批任务已发起");
            todoItem.setStatus("2"); // 在办
            todoItem.setTag("审批");
            todoItem.setSenderId(Long.valueOf(callbackDto.getCreateId()));
            todoItem.setReceiverId(Long.valueOf(callbackDto.getCreateId())); // 接收人为 createId
            todoItem.setSceneType("系统待办");
            todoItem.setExtendId(extendId);
            todoItem.setProductId(null);
            todoItem.setMenuId(1406L);
            todoItem.setRemark("普通");
            todoItem.setUrlParams("processInstanceId=" + callbackDto.getProcessInstanceId());

            todoItemsService.insertTodoItems(todoItem);
            logger.info("待办事项已创建");
            return AjaxResult.success("待办事项已创建");
        } else if ("bpmn_instance_end".equals(eventType)) {
            TodoItems query = new TodoItems();
            query.setExtendId(extendId);
            List<TodoItems> items = todoItemsService.selectTodoItemsList(query);
            if (items != null && !items.isEmpty()) {
                TodoItems item = items.get(0);
                item.setStatus("3"); // 办结
                todoItemsService.updateTodoItems(item);
                logger.info("待办事项已完成");
                return AjaxResult.success("待办事项已完成");
            } else {
                logger.info("未找到对应的待办事项");
                return AjaxResult.error("未找到对应的待办事项");
            }
        } else if ("bpmn_task_start".equals(eventType)) {
            // 创建新任务待办事项，接收人设为 completeId
            TodoItems todoItem = new TodoItems();
            todoItem.setTitle(title);
            todoItem.setContent("您有新的任务审批需要处理");
            todoItem.setStatus("1"); // 待办
            todoItem.setTag("任务审批");
            todoItem.setSenderId(Long.valueOf(callbackDto.getCreateId()));
            todoItem.setReceiverId(Long.valueOf(callbackDto.getCompleteId())); // 接收人为 completeId
            todoItem.setSceneType("系统待办");
            todoItem.setExtendId(extendId);
            todoItem.setProductId(null);
            todoItem.setMenuId(1406L);
            todoItem.setRemark("普通");
            todoItem.setUrlParams("processInstanceId=" + callbackDto.getProcessInstanceId());

            todoItemsService.insertTodoItems(todoItem);
            logger.info("任务待办事项已创建");
            return AjaxResult.success("任务待办事项已创建");
        } else if ("bpmn_task_end".equals(eventType)) {
            TodoItems query = new TodoItems();
            query.setExtendId(extendId);
            List<TodoItems> items = todoItemsService.selectTodoItemsList(query);
            if (items != null && !items.isEmpty()) {
                TodoItems item = items.get(0);
                item.setStatus("3"); // 办结
                todoItemsService.updateTodoItems(item);
                logger.info("任务待办事项已完成");
                return AjaxResult.success("任务待办事项已完成");
            } else {
                logger.info("未找到对应的任务待办事项");
                return AjaxResult.error("未找到对应的任务待办事项");
            }
        } else if ("bpmn_task_delete".equals(eventType)) {
            TodoItems query = new TodoItems();
            query.setExtendId(extendId);
            List<TodoItems> items = todoItemsService.selectTodoItemsList(query);
            if (items != null && !items.isEmpty()) {
                for (TodoItems item : items) {
                    todoItemsService.deleteTodoItemsById(item.getTodoId());
                }
                logger.info("待办事项已删除");
                return AjaxResult.success("待办事项已删除");
            } else {
                logger.info("未找到对应的待办事项");
                return AjaxResult.error("未找到对应的待办事项");
            }
        } else {
            logger.info("不支持的事件类型: " + eventType);
            return AjaxResult.error("不支持的事件类型");
        }
    }

}
