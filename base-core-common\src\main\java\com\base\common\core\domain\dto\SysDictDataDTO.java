package com.base.common.core.domain.dto;

import com.base.common.core.domain.entity.SysDictData;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

@Data
public class SysDictDataDTO {

    @JsonAlias("dict_data_item")
    @Valid
    private SysDictData dictDataItem;

    @JsonAlias("dict_type")
    private List<String> dictType;

    private String status;

    private String scene;
}
