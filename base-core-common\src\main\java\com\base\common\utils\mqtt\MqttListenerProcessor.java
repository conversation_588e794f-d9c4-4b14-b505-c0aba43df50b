package com.base.common.utils.mqtt;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.base.common.annotation.MqttSubscribe;
import com.base.common.config.MQTTConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Configuration
@Slf4j
public class MqttListenerProcessor implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Autowired
    private MqttClient mqttClient;

    @Autowired
    private MQTTConfig mqttConfig;

    @Resource(name = "threadPoolTaskExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    // 精确匹配的主题处理器
    private final Map<String, List<HandlerMethod>> exactTopicHandlers = new ConcurrentHashMap<>();
    // 通配符匹配的主题处理器（按复杂程度分组）
    private final Map<Integer, List<WildcardTopicHandler>> wildcardTopicHandlers = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() throws MqttException {
        Boolean enabled = mqttConfig.getEnabled();
        if (!enabled) {
            log.info("MQTT未连接...");
            return;
        }

        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(org.springframework.stereotype.Component.class);
        for (Object bean : beans.values()) {
            Class<?> targetClass = bean.getClass();
            for (Method method : targetClass.getDeclaredMethods()) {
                MqttSubscribe subscribe = AnnotatedElementUtils.findMergedAnnotation(method, MqttSubscribe.class);
                if (subscribe != null) {
                    HandlerMethod handler = new HandlerMethod(bean, method);
                    String topic = subscribe.topic();
                    if (isWildcardTopic(topic)) {
                        // 处理通配符主题
                        int complexity = calculateWildcardComplexity(topic);
                        wildcardTopicHandlers.computeIfAbsent(complexity, k -> new ArrayList<>())
                                .add(new WildcardTopicHandler(topic, handler));
                    } else {
                        // 处理精确主题
                        exactTopicHandlers.computeIfAbsent(topic, k -> new ArrayList<>())
                                .add(handler);
                    }

                }
            }
        }
        Set<String> maxCoverageTopics = findMaxCoverageTopics();

        for (Map.Entry<String, List<HandlerMethod>> entry : exactTopicHandlers.entrySet()) {
            String topic = entry.getKey();
            if (maxCoverageTopics.contains(topic)){
                registerAdapter(topic);
            }
        }

        wildcardTopicHandlers.forEach((complexity, handlers) -> {
            handlers.forEach(h -> {
                String topicPattern = h.getTopicPattern();
                try {
                    if (maxCoverageTopics.contains(topicPattern)){
                        registerAdapter(topicPattern);
                    }
                } catch (MqttException e) {
                    throw new RuntimeException(e);
                }
            });
        });

    }

    private void registerAdapter(String topic) throws MqttException {
        // 支持通配符订阅
        mqttClient.subscribe(topic, 1);

        mqttClient.setCallback(new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                log.error("MQTT连接丢失", cause);
            }

            @Override
            public void messageArrived(String topic, MqttMessage message) throws Exception {
                handleMessage(topic, message);
            }

            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                // 处理消息送达确认
            }
        });
    }

    private void handleMessage(String topic, MqttMessage message) {
        Object payload = StringUtils.toEncodedString(message.getPayload(), StandardCharsets.UTF_8);
        log.info("MQTT 收到消息, topic: {}, messageId: {}, payload: {}", topic, message.getId(), payload);
        List<HandlerMethod> handlers = findHandlers(topic);
        if (handlers != null) {
            for (HandlerMethod handler : handlers) {
                threadPoolTaskExecutor.submit(() -> {
                    try {
                        Method method = handler.method;
                        Object bean = handler.bean;
                        Parameter parameter = method.getParameters()[0];
                        Class<?> parameterType = parameter.getType();

                        Object convertedPayload = convertPayload(payload, parameterType);
                        method.invoke(bean, convertedPayload);
                    } catch (Exception e) {
                        log.error("MQTT消息接收异常", e);
                    }
                });
            }
        }
    }

    // 查找匹配的处理器
    public List<HandlerMethod> findHandlers(String topic) {

        // 1. 先查找精确匹配
        List<HandlerMethod> handlers = new ArrayList<>(exactTopicHandlers.getOrDefault(topic, Collections.emptyList()));

        // 2. 再查找通配符匹配（按复杂度升序）
        wildcardTopicHandlers.keySet().stream()
                .sorted()
                .forEach(complexity -> {
                    wildcardTopicHandlers.get(complexity).forEach(wildcardHandler -> {
                        if (matchWildcardTopic(wildcardHandler.getTopicPattern(), topic)) {
                            handlers.add(wildcardHandler.getHandler());
                        }
                    });
                });

        return handlers;
    }

    private Object convertPayload(Object payload, Class<?> targetType) {
        if (payload == null) {
            return null;
        }
        String payloadStr = payload.toString();

        // 使用反射和泛型实现更灵活的类型转换
        if (targetType == String.class) {
            return payloadStr;
        } else if (targetType.isPrimitive() || targetType.equals(Boolean.class) || targetType.equals(Integer.class) || targetType.equals(Long.class) || targetType.equals(Double.class) || targetType.equals(Float.class)) {
            try {
                if (targetType == Integer.class || targetType == int.class) {
                    return Integer.parseInt(payloadStr);
                } else if (targetType == Long.class || targetType == long.class) {
                    return Long.parseLong(payloadStr);
                } else if (targetType == Double.class || targetType == double.class) {
                    return Double.parseDouble(payloadStr);
                } else if (targetType == Float.class || targetType == float.class) {
                    return Float.parseFloat(payloadStr);
                } else if (targetType == Boolean.class || targetType == boolean.class) {
                    return Boolean.parseBoolean(payloadStr);
                }
            } catch (NumberFormatException e) {
                log.error("类型转换异常", e);
            }
        } else if (targetType.isArray()) {
            Class<?> componentType = targetType.getComponentType();
            JSONArray jsonArray = JSON.parseArray(payloadStr);
            Object array = java.lang.reflect.Array.newInstance(componentType, jsonArray.size());
            for (int i = 0; i < jsonArray.size(); i++) {
                java.lang.reflect.Array.set(array, i, jsonArray.getObject(i, componentType));
            }
            return array;
        } else if (List.class.isAssignableFrom(targetType)) {
            return JSON.parseArray(payloadStr, Object.class);
        } else if (Set.class.isAssignableFrom(targetType)) {
            List<Object> list = JSON.parseArray(payloadStr, Object.class);
            return new HashSet<>(list);
        } else if (Map.class.isAssignableFrom(targetType)) {
            return JSON.parseObject(payloadStr, targetType);
        } else {
            return JSON.parseObject(payloadStr, targetType);
        }
        return null;
    }

    private static class HandlerMethod {
        final Object bean;
        final Method method;

        HandlerMethod(Object bean, Method method) {
            this.bean = bean;
            this.method = method;
        }
    }

    // 内部类：存储通配符主题及其处理器
    private static class WildcardTopicHandler {
        private final String topicPattern;
        private final HandlerMethod handler;

        public WildcardTopicHandler(String topicPattern, HandlerMethod handler) {
            this.topicPattern = topicPattern;
            this.handler = handler;
        }

        public String getTopicPattern() {
            return topicPattern;
        }

        public HandlerMethod getHandler() {
            return handler;
        }
    }


    // 判断是否为通配符主题
    private boolean isWildcardTopic(String topic) {
        return topic.contains("+") || topic.contains("#");
    }

    // 计算通配符主题的复杂度（用于优化匹配顺序）
    private int calculateWildcardComplexity(String topic) {
        int complexity = 0;
        String[] levels = topic.split("/");

        for (String level : levels) {
            if ("#".equals(level)) {
                complexity += 100;  // 多级通配符权重高
            } else if ("+".equals(level)) {
                complexity += 10;   // 单级通配符权重次之
            } else {
                complexity += 1;    // 普通层级权重最低
            }
        }

        return complexity;
    }

    // 通配符主题匹配逻辑
    private boolean matchWildcardTopic(String pattern, String topic) {
        // 将MQTT通配符转换为正则表达式
        String regex = pattern.replace(".", "\\.")
                .replace("+", "[^/]+")
                .replace("#", ".+");

        return Pattern.matches(regex, topic);
    }

    // 找出所有最大覆盖范围的主题
    public Set<String> findMaxCoverageTopics() {
        // 收集所有主题
        Set<String> allTopics = new HashSet<>(exactTopicHandlers.keySet());
        wildcardTopicHandlers.forEach((k, v) ->
                v.forEach(m -> {allTopics.add(m.getTopicPattern());})
        );

        // 存储不能被其他主题覆盖的主题
        Set<String> maxCoverageTopics = new HashSet<>(allTopics);

        // 两两比较，移除被其他主题覆盖的主题
        for (String topicA : new HashSet<>(maxCoverageTopics)) {
            for (String topicB : maxCoverageTopics) {
                if (!topicA.equals(topicB) && isTopicCovered(topicA, topicB)) {
                    maxCoverageTopics.remove(topicA);
                    break;
                }
            }
        }

        return maxCoverageTopics;
    }

    // 判断主题A是否被主题B覆盖
    private boolean isTopicCovered(String topicA, String topicB) {
        // 如果B是通配符主题，检查A是否匹配B的模式
        if (isWildcardTopic(topicB)) {
            return matchWildcardTopic(topicB, topicA);
        }

        // 如果B不是通配符主题，则只有A和B完全相同才算覆盖
        return topicA.equals(topicB);
    }

    // 找出能够覆盖指定主题的所有最大主题
    public Set<String> findTopicsCovering(String targetTopic) {
        Set<String> allTopics = new HashSet<>(exactTopicHandlers.keySet());
        wildcardTopicHandlers.forEach((k, v) ->
                v.forEach(m -> {allTopics.add(m.getTopicPattern());})
        );


        // 从覆盖主题中找出最大的主题（即不被其他覆盖主题覆盖的主题）
        Set<String> maxCoveringTopics = allTopics.stream()
                .filter(topic -> isTopicCovered(targetTopic, topic)).collect(Collectors.toSet());

        for (String topicA : new HashSet<>(maxCoveringTopics)) {
            for (String topicB : maxCoveringTopics) {
                if (!topicA.equals(topicB) && isTopicCovered(topicA, topicB)) {
                    maxCoveringTopics.remove(topicA);
                    break;
                }
            }
        }

        return maxCoveringTopics;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
