package com.base.system.service.impl;

import com.base.common.core.domain.entity.SysMenu;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StringUtils;
import com.base.system.domain.Product;
import com.base.system.domain.SysOften;
import com.base.system.mapper.SysOftenMapper;
import com.base.system.service.IProductInfoService;
import com.base.system.service.ISysMenuService;
import com.base.system.service.ISysOftenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户常用功能Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Service
public class SysOftenServiceImpl implements ISysOftenService {
    @Autowired
    private SysOftenMapper sysOftenMapper;

    @Autowired
    ISysMenuService sysMenuService;

    @Autowired
    IProductInfoService productInfoService;

    /**
     * 查询用户常用功能
     *
     * @param oftenId 用户常用功能主键
     * @return 用户常用功能
     */
    @Override
    public SysOften selectSysOftenByOftenId(Long oftenId) {
        return sysOftenMapper.selectSysOftenByOftenId(oftenId);
    }

    @Override
    public List<SysOften> selectList(SysOften sysOften) {
        return sysOftenMapper.selectSysOftenList(sysOften);
    }

    /**
     * 查询用户常用功能列表
     *
     * @param sysOften 用户常用功能
     * @return 用户常用功能
     */
    @Override
    public List<SysOften> selectSysOftenList(SysOften sysOften) {
        List<SysOften> returnList = new ArrayList<>();
        List<SysOften> sysOftens = sysOftenMapper.selectSysOftenList(sysOften);
        Map<Long, SysMenu> sysMenuMap = sysMenuService.selectMenuList(new SysMenu().setStatus("0").setVisible("0"), SecurityUtils.getUserId()).stream().collect(Collectors.toMap(SysMenu::getMenuId, menu -> menu));
        sysOftens.forEach(sysOften1 -> {
            if (StringUtils.equals(sysOften1.getOftenType(), "menu")) {
                // 菜单快捷方式
                SysMenu sysMenu = sysMenuMap.get(Long.parseLong(sysOften1.getExternalId()));
                sysOften1.setSysMenu(sysMenu).setOftenName(sysMenu.getMenuName()).setOftenIcon(sysMenu.getIcon());
                returnList.add(sysOften1);
            } else if (StringUtils.equals(sysOften1.getOftenType(), "app")) {
                // 产品快捷方式
                Product product = productInfoService.getProductDetailById(Integer.parseInt(sysOften1.getExternalId()));
                if (StringUtils.equals(product.getIsEnabled(), "已启用")) {
                    sysOften1.setOftenIcon(product.getIcon()).setOftenName(product.getName()).setOftenImage(product.getAppImage());
                    returnList.add(sysOften1);
                }
            } else if (StringUtils.equals(sysOften1.getOftenType(), "customize")) {
                // 自定义快捷方式
                returnList.add(sysOften1);
            }
        });
        return returnList;
    }

    /**
     * 新增用户常用功能
     *
     * @param sysOften 用户常用功能
     * @return 结果
     */
    @Override
    public int insertSysOften(SysOften sysOften) {
        sysOften.setCreateTime(DateUtils.getNowDate());
        sysOften.setCreateBy(SecurityUtils.getUsername());
        return sysOftenMapper.insertSysOften(sysOften);
    }

    /**
     * 修改用户常用功能
     *
     * @param sysOften 用户常用功能
     * @return 结果
     */
    @Override
    public int updateSysOften(SysOften sysOften) {
        return sysOftenMapper.updateSysOften(sysOften);
    }

    /**
     * 批量删除用户常用功能
     *
     * @param oftenIds 需要删除的用户常用功能主键
     * @return 结果
     */
    @Override
    public int deleteSysOftenByOftenIds(Long[] oftenIds) {
        return sysOftenMapper.deleteSysOftenByOftenIds(oftenIds);
    }

    /**
     * 删除用户常用功能信息
     *
     * @param oftenId 用户常用功能主键
     * @return 结果
     */
    @Override
    public int deleteSysOftenByOftenId(Long oftenId) {
        return sysOftenMapper.deleteSysOftenByOftenId(oftenId);
    }
}
