package com.base.dex.service.impl;

import com.base.common.utils.StringUtils;
import com.base.dex.domain.DevBasePoint;
import com.base.dex.mapper.DevBasePointMapper;
import com.base.dex.service.IDevBasePointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备点Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class DevBasePointServiceImpl implements IDevBasePointService
{
    @Autowired
    private DevBasePointMapper devBasePointMapper;

    /**
     * 查询设备点
     *
     * @param pointId 设备点主键
     * @return 设备点
     */
    @Override
    public DevBasePoint selectDevBasePointByPointId(String pointId)
    {
        return devBasePointMapper.selectDevBasePointByPointId(pointId);
    }

    /**
     * 查询设备点列表
     *
     * @param devBasePoint 设备点
     * @return 设备点
     */
    @Override
    public List<DevBasePoint> selectDevBasePointList(DevBasePoint devBasePoint)
    {
        return devBasePointMapper.selectDevBasePointList(devBasePoint);
    }

    /**
     * 新增设备点
     *
     * @param devBasePoint 设备点
     * @return 结果
     */
    @Override
    public int insertDevBasePoint(DevBasePoint devBasePoint)
    {
        return devBasePointMapper.insertDevBasePoint(devBasePoint);
    }

    @Override
    public int batchDevBasePoint(List<DevBasePoint> devBasePointList)
    {
        if (StringUtils.isNotEmpty(devBasePointList)){
            return devBasePointMapper.batchDevBasePoint(devBasePointList);
        }
        return 0;
    }

    /**
     * 修改设备点
     *
     * @param devBasePoint 设备点
     * @return 结果
     */
    @Override
    public int updateDevBasePoint(DevBasePoint devBasePoint)
    {
        return devBasePointMapper.updateDevBasePoint(devBasePoint);
    }

    /**
     * 批量删除设备点
     *
     * @param pointIds 需要删除的设备点主键
     * @return 结果
     */
    @Override
    public int deleteDevBasePointByPointIds(String[] pointIds)
    {
        return devBasePointMapper.deleteDevBasePointByPointIds(pointIds);
    }

    /**
     * 删除设备点信息
     *
     * @param pointId 设备点主键
     * @return 结果
     */
    @Override
    public int deleteDevBasePointByPointId(String pointId)
    {
        return devBasePointMapper.deleteDevBasePointByPointId(pointId);
    }
}
