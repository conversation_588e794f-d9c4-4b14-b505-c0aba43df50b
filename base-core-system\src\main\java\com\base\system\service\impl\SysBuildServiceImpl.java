package com.base.system.service.impl;

import com.base.system.domain.SysBuild;
import com.base.system.mapper.SysBuildMapper;
import com.base.system.service.ISysBuildService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统建筑物服务实现类
 * 实现了建筑物相关的业务逻辑
 */
@Service
public class SysBuildServiceImpl implements ISysBuildService {

    @Autowired
    private SysBuildMapper sysBuildMapper;

    /**
     * 根据ID获取建筑物信息
     *
     * @param buildId 建筑物ID
     * @return 建筑物信息
     */
    @Override
    public SysBuild getById(String buildId) {
        return sysBuildMapper.getById(buildId);
    }

    /**
     * 根据条件查询建筑物列表
     *
     * @param sysBuild 查询条件
     * @return 建筑物列表
     */
    @Override
    public List<SysBuild> selectList(SysBuild sysBuild) {
        return sysBuildMapper.selectList(sysBuild);
    }

    /**
     * 添加新建筑物
     *
     * @param sysBuild 新建筑物信息
     * @return 添加后的建筑物信息
     */
    @Override
    public SysBuild add(SysBuild sysBuild) {
        sysBuildMapper.insert(sysBuild);
        return sysBuild;
    }

    /**
     * 编辑建筑物信息
     *
     * @param sysBuild 要编辑的建筑物信息
     * @return 编辑后的建筑物信息
     */
    @Override
    public SysBuild edit(SysBuild sysBuild) {
        sysBuildMapper.update(sysBuild);
        return sysBuild;
    }

    /**
     * 根据ID删除建筑物
     *
     * @param buildId 要删除的建筑物ID
     * @return 是否删除成功
     */
    @Override
    public boolean deleteById(String buildId) {
        return sysBuildMapper.deleteById(buildId) > 0;
    }
}