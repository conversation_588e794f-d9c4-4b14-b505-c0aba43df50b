package com.base.task.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StringUtils;
import com.base.common.wechat.api.WechatApi;
import com.base.message.domain.SysMessage;
import com.base.message.service.ISysMessageService;
import com.base.system.service.ISysConfigService;
import com.base.system.service.ISysUserService;
import com.base.task.domain.Task;
import com.base.task.domain.TaskFile;
import com.base.task.domain.TaskType;
import com.base.task.mapper.TaskMapper;
import com.base.task.service.ITaskFileService;
import com.base.task.service.ITaskService;
import com.base.task.service.ITaskTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Service
public class TaskServiceImpl implements ITaskService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private ITaskFileService taskFileService;

    @Autowired
    private ITaskTypeService taskTypeService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private WechatApi wechatApi;

    @Autowired
    private ISysMessageService sysMessageService;

    /**
     * 查询任务
     *
     * @param id 任务主键
     * @return 任务
     */
    @Override
    public Task selectTaskById(Long id) {
        return taskMapper.selectTaskById(id);
    }

    /**
     * 查询任务列表
     *
     * @param task 任务
     * @return 任务
     */
    @Override
    public List<Task> selectTaskList(Task task) {
        return taskMapper.selectTaskList(task);
    }

    /**
     * 新增任务
     *
     * @param task 任务
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertTask(Task task) {
        task.setCreateBy(SecurityUtils.getUsername());
        task.setCreateTime(DateUtils.getNowDate());
        task.setTaskNumber(DateUtils.dateTimeNow() + RandomUtil.randomNumbers(18));
        int result = taskMapper.insertTask(task);
        // 任务文件
        if (StringUtils.isNotEmpty(task.getTaskFileList())) {
            for (TaskFile item : task.getTaskFileList()) {
                if (StringUtils.isEmpty(item.getFilePath())) {
                    continue;
                }
                String fileSuffix = item.getFilePath().substring(item.getFilePath().lastIndexOf(".") + 1);
                item.setFileSuffix(fileSuffix);
                item.setTaskId(task.getId());
                item.setCreateBy(SecurityUtils.getUsername());
                item.setCreateTime(DateUtils.getNowDate());
                taskFileService.insertTaskFile(item);
            }
        }
        // 查询任务类型名称
        TaskType taskType = taskTypeService.selectTaskTypeById(task.getTaskTypeId());
        String taskTypeName = "";
        if (taskType != null && StringUtils.isNotEmpty(taskType.getName())) {
            taskTypeName = taskType.getName();
        }
        // 保存消息通知
        saveSysMessage(task, taskTypeName, SecurityUtils.getUsername());
        // 获取App前端地址
        String appWebUrl = configService.selectConfigByKey("sys.app.web.url");
        // 向用户发送信息
        getUserInfo(task.getId(), task.getName(), task.getDeadline(), task.getExecutor(), appWebUrl);
        return result;
    }

    /**
     * 保存消息通知
     * 根据任务的紧急状态生成并保存系统消息
     *
     * @param task         Task对象，包含任务的详细信息
     * @param taskTypeName 任务类型名称
     * @param createBy     创建者
     */
    private void saveSysMessage(Task task, String taskTypeName, String createBy) {
        // 紧急状态：0不紧急，1一般，2紧急，默认2紧急
        String urgencyLevel = "紧急";
        if (0 == task.getUrgencyLevel()) {
            urgencyLevel = "不紧急";
        } else if (1 == task.getUrgencyLevel()) {
            urgencyLevel = "一般";
        }
        // 消息通知
        SysMessage sysMessage = new SysMessage();
        sysMessage.setType("1");
        sysMessage.setMessageConfigId(1L);
        sysMessage.setParam("{\"id\":\"" + task.getId() + "\"}");
        sysMessage.setStatus(0);
        sysMessage.setTitle("我的任务");
        sysMessage.setContent("您有【（" + taskTypeName + "）" + task.getName() + "】待执行，紧急程度：【" + urgencyLevel + "】");
        sysMessage.setUserId(task.getExecutor());
        sysMessage.setCreateBy(createBy);
        sysMessageService.insertSysMessage(sysMessage);
    }

    /**
     * 修改任务
     *
     * @param task 任务
     * @return 结果
     */
    @Override
    public int updateTask(Task task) {
        task.setUpdateBy(SecurityUtils.getUsername());
        task.setUpdateTime(DateUtils.getNowDate());
        return taskMapper.updateTask(task);
    }

    /**
     * 批量删除任务
     *
     * @param ids 需要删除的任务主键
     * @return 结果
     */
    @Override
    public int deleteTaskByIds(Long[] ids) {
        return taskMapper.deleteTaskByIds(ids);
    }

    /**
     * 删除任务信息
     *
     * @param id 任务主键
     * @return 结果
     */
    @Override
    public int deleteTaskById(Long id) {
        return taskMapper.deleteTaskById(id);
    }

    /**
     * 统计进行中、我发起、已完成的数量
     *
     * @param task 任务对象
     * @return 进行中、我发起、已完成的数量
     */
    @Override
    public Long getCountTask(Task task) {
        return taskMapper.getCountTask(task);
    }

    /**
     * 查看任务排期
     *
     * @param taskTypeId 任务类型id
     * @return 任务排期列表
     */
    @Override
    public List<Task> getTaskScheduleList(Long taskTypeId) {
        return taskMapper.getTaskScheduleList(taskTypeId);
    }

    /**
     * 批量添加任务
     *
     * @param list 任务列表
     * @return 结果
     */
    @Override
    public int batchInsertTask(List<Task> list) {
        return taskMapper.batchInsertTask(list);
    }

    /**
     * 定时生成任务
     *
     * @param type 生成类型：0系统自动执行，1人工手动执行
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getGenTask(Integer type) {
        long time = System.currentTimeMillis();
        // 当前日期 2024-09-01 00:00:00
        Date currentDate = DateUtil.beginOfDay(DateUtil.date());
        logger.info("==========> 定时生成任务 currentDate={}", currentDate);
        TaskType taskType = new TaskType();
        // 是否定期生成：0否，1是
        taskType.setPeriodicStatus(1);
        List<TaskType> taskTypeList = taskTypeService.selectTaskTypeList(taskType);
        List<Task> taskList = new ArrayList<>();
        for (TaskType itemType : taskTypeList) {
            // 任务周期
            Integer taskCycle = itemType.getTaskCycle();
            // 开始时间
            Date startTime = itemType.getStartTime();
            // 任务提前生成时间(天数)
            Integer advanceTime = itemType.getAdvanceTime();
            if (advanceTime == null) {
                advanceTime = 0;
            }
            Date nextStartTime = itemType.getNextStartTime();

            boolean bool = taskCycle != null && taskCycle >= 0
                    && StringUtils.isNotEmpty(itemType.getExecutor());
            if (bool) {
                Date deadline;
                if (nextStartTime != null) {
                    deadline = nextStartTime;
                } else {
                    deadline = startTime;
                }
                Date generateDate = DateUtil.offsetDay(deadline, -advanceTime);
                boolean shouldGenerateTask = deadline != null && currentDate.compareTo(generateDate) == 0;
                if (shouldGenerateTask) {
                    String[] executorNameArray = itemType.getExecutor().split(",");
                    for (String executor : executorNameArray) {
                        Task task = new Task();
                        task.setTaskTypeId(itemType.getId());
                        // 使用任务类型名称作为任务名称
                        task.setName(itemType.getName());
                        task.setTaskNumber(DateUtils.dateTimeNow() + RandomUtil.randomNumbers(18));
                        // 使用任务类型名称作为任务描述
                        task.setDescription(itemType.getName());
                        task.setDeadline(DateUtil.endOfDay(deadline));
                        task.setExecutor(executor);
                        task.setUrgencyLevel(itemType.getUrgencyLevel());
                        task.setStatus(0);
                        task.setCreateBy(itemType.getCreateBy());
                        task.setCreateTime(DateUtils.getNowDate());
                        task.setSourceType(1);
                        task.setRemark(type + "");
                        taskList.add(task);
                    }
                    Date offsetDay = DateUtil.offsetDay(deadline, taskCycle);
                    taskType.setId(itemType.getId());
                    taskType.setCurrentStatus(1);
                    taskType.setNextStartTime(offsetDay);
                    taskType.setUpdateBy(itemType.getUpdateBy());
                    taskType.setPeriodicStatus(null);
                    taskTypeService.updateTaskType(taskType);
                }
            }
        }
        if (StringUtils.isNotEmpty(taskList)) {
            // 批量生成任务
            int result = batchInsertTask(taskList);
            logger.info("==========> 批量生成任务成功，总记录：{}", result);
            String appWebUrl = configService.selectConfigByKey("sys.app.web.url");
            for (Task task : taskList) {
                // 保存消息通知
                saveSysMessage(task, task.getName(), null);
                // 向用户发送信息
                getUserInfo(task.getId(), task.getName(), task.getDeadline(), task.getExecutor(), appWebUrl);
            }
        }
        long end = (System.currentTimeMillis() - time) / 1000;
        logger.info("==========> 生成排期任务开始，用时：{}秒 <========== ", end);
    }

    /**
     * 获取用户信息
     *
     * @param taskId    任务id
     * @param taskName  任务名称
     * @param endTime   截止时间
     * @param userName  用户名
     * @param appWebUrl app前端地址
     */
    private void getUserInfo(Long taskId, String taskName, Date endTime, String userName, String appWebUrl) {
        if (StringUtils.isNotEmpty(userName)) {
            SysUser sysUser = userService.selectUserByUserName(userName);
            if (sysUser != null) {
                if (StringUtils.isNotEmpty(sysUser.getOpenId())) {
                    wechatApi.sendTaskMsg(sysUser.getOpenId(), taskId, taskName, endTime, sysUser.getNickName(), appWebUrl);
                }
                if (StringUtils.isNotEmpty(sysUser.getQyUserId())) {
                    wechatApi.sendTaskQyMsg(sysUser.getQyUserId(), taskId, taskName, endTime, sysUser.getNickName(), appWebUrl);
                }
            }
        }
    }

}
