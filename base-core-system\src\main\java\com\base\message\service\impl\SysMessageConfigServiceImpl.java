package com.base.message.service.impl;

import java.util.List;

import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.base.message.mapper.SysMessageConfigMapper;
import com.base.message.domain.SysMessageConfig;
import com.base.message.service.ISysMessageConfigService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 消息通知配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Service
public class SysMessageConfigServiceImpl implements ISysMessageConfigService {
    @Autowired
    private SysMessageConfigMapper sysMessageConfigMapper;

    /**
     * 查询消息通知配置
     *
     * @param id 消息通知配置主键
     * @return 消息通知配置
     */
    @Override
    public SysMessageConfig selectSysMessageConfigById(Long id) {
        return sysMessageConfigMapper.selectSysMessageConfigById(id);
    }

    /**
     * 查询消息通知配置列表
     *
     * @param sysMessageConfig 消息通知配置
     * @return 消息通知配置
     */
    @Override
    public List<SysMessageConfig> selectSysMessageConfigList(SysMessageConfig sysMessageConfig) {
        return sysMessageConfigMapper.selectSysMessageConfigList(sysMessageConfig);
    }

    /**
     * 新增消息通知配置
     *
     * @param sysMessageConfig 消息通知配置
     * @return 结果
     */
    @Override
    public int insertSysMessageConfig(SysMessageConfig sysMessageConfig) {
        sysMessageConfig.setCreateBy(SecurityUtils.getUsername());
        sysMessageConfig.setCreateTime(DateUtils.getNowDate());
        return sysMessageConfigMapper.insertSysMessageConfig(sysMessageConfig);
    }

    /**
     * 修改消息通知配置
     *
     * @param sysMessageConfig 消息通知配置
     * @return 结果
     */
    @Override
    public int updateSysMessageConfig(SysMessageConfig sysMessageConfig) {
        sysMessageConfig.setUpdateBy(SecurityUtils.getUsername());
        sysMessageConfig.setUpdateTime(DateUtils.getNowDate());
        return sysMessageConfigMapper.updateSysMessageConfig(sysMessageConfig);
    }

    /**
     * 批量更新消息通知配置，添加、修改和删除
     *
     * @param sysMessageConfigList 消息通知配置列表
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int batchSave(List<SysMessageConfig> sysMessageConfigList) {
        int result = 0;
        for (SysMessageConfig config : sysMessageConfigList) {
            if (config.getId() != null && config.getDelFlag() != null && 2 == config.getDelFlag()) {
                // 删除操作
                result += deleteSysMessageConfigById(config.getId());
            } else if (config.getId() != null) {
                // 修改操作
                result += updateSysMessageConfig(config);
            } else {
                // 新增操作
                result += insertSysMessageConfig(config);
            }
        }
        return result;
    }

    /**
     * 批量删除消息通知配置
     *
     * @param ids 需要删除的消息通知配置主键
     * @return 结果
     */
    @Override
    public int deleteSysMessageConfigByIds(Long[] ids) {
        return sysMessageConfigMapper.deleteSysMessageConfigByIds(ids);
    }

    /**
     * 删除消息通知配置信息
     *
     * @param id 消息通知配置主键
     * @return 结果
     */
    @Override
    public int deleteSysMessageConfigById(Long id) {
        return sysMessageConfigMapper.deleteSysMessageConfigById(id);
    }
}
