package com.base.dex.mapper;

import com.base.dex.domain.DevDataAll;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 所有设备数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Repository
public interface DevDataAllMapper
{
    /**
     * 查询所有设备数据
     *
     * @param monitorTime 所有设备数据主键
     * @return 所有设备数据
     */
    public DevDataAll selectDevDataAllByMonitorTime(String monitorTime);

    /**
     * 查询所有设备数据列表
     *
     * @param devDataAll 所有设备数据
     * @return 所有设备数据集合
     */
    public List<DevDataAll> selectDevDataAllList(DevDataAll devDataAll);

    /**
     * 新增所有设备数据
     *
     * @param devDataAll 所有设备数据
     * @return 结果
     */
    public int insertDevDataAll(DevDataAll devDataAll);

    /**
     * 修改所有设备数据
     *
     * @param devDataAll 所有设备数据
     * @return 结果
     */
    public int updateDevDataAll(DevDataAll devDataAll);

    /**
     * 删除所有设备数据
     *
     * @param monitorTime 所有设备数据主键
     * @return 结果
     */
    public int deleteDevDataAllByMonitorTime(String monitorTime);

    /**
     * 批量删除所有设备数据
     *
     * @param monitorTimes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDevDataAllByMonitorTimes(String[] monitorTimes);

    /**
     * 批量新增所有设备数据
     *
     * @param devDataAllList 所有设备数据列表
     * @return 结果
     */
    public int batchDevDataAll(List<DevDataAll> devDataAllList);

   List<DevDataAll> selectByTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
   }
