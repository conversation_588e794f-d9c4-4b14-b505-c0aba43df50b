<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.task.mapper.TaskTypeMapper">
    
    <resultMap type="TaskType" id="TaskTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="periodicStatus"    column="periodic_status"    />
        <result property="executor"    column="executor"    />
        <result property="urgencyLevel"    column="urgency_level"    />
        <result property="taskCycle"    column="task_cycle"    />
        <result property="startTime"    column="start_time"    />
        <result property="advanceTime"    column="advance_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="currentStatus"    column="current_status"    />
        <result property="nextStartTime"    column="next_start_time"    />
        <result property="menuStatus"    column="menu_status"    />
        <result property="sort"    column="sort"    />
        <result property="userName"    column="nick_name"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="releaseInfo"    column="release_info"    />
        <result property="progressInfo"    column="progress_info"    />
        <result property="finishInfo"    column="finish_info"    />
    </resultMap>

    <sql id="selectTaskTypeVo">
        select a.id, a.name, a.periodic_status, a.executor, a.urgency_level, a.task_cycle, a.start_time, a.advance_time, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, a.del_flag, a.current_status, a.next_start_time, a.menu_status, a.sort,
               a.review_status, a.release_info, a.progress_info, a.finish_info, u.nick_name
        from public.task_type a
        left join public.sys_user u on u.user_name = a.create_by
    </sql>

    <select id="selectTaskTypeList" parameterType="TaskType" resultMap="TaskTypeResult">
        <include refid="selectTaskTypeVo"/>
        <where>
            a.del_flag = 0
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="periodicStatus != null "> and a.periodic_status = #{periodicStatus}</if>
            <if test="executor != null  and executor != ''"> and a.executor = #{executor}</if>
            <if test="urgencyLevel != null "> and a.urgency_level = #{urgencyLevel}</if>
            <if test="taskCycle != null "> and a.task_cycle = #{taskCycle}</if>
            <if test="startTime != null "> and a.start_time = #{startTime}</if>
            <if test="advanceTime != null "> and a.advance_time = #{advanceTime}</if>
            <if test="menuStatus != null "> and a.menu_status = #{menuStatus}</if>
        </where>
        order by a.sort
    </select>
    
    <select id="selectTaskTypeById" parameterType="Long" resultMap="TaskTypeResult">
        <include refid="selectTaskTypeVo"/>
        where a.id = #{id}
    </select>
        
    <insert id="insertTaskType" parameterType="TaskType" useGeneratedKeys="true" keyProperty="id">
        insert into task_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="periodicStatus != null">periodic_status,</if>
            <if test="executor != null">executor,</if>
            <if test="urgencyLevel != null">urgency_level,</if>
            <if test="taskCycle != null">task_cycle,</if>
            <if test="startTime != null">start_time,</if>
            <if test="advanceTime != null">advance_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="currentStatus != null">current_status,</if>
            <if test="nextStartTime != null">next_start_time,</if>
            <if test="menuStatus != null">menu_status,</if>
            <if test="sort != null">sort,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="releaseInfo != null">release_info,</if>
            <if test="progressInfo != null">progress_info,</if>
            <if test="finishInfo != null">finish_info,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="periodicStatus != null">#{periodicStatus},</if>
            <if test="executor != null">#{executor},</if>
            <if test="urgencyLevel != null">#{urgencyLevel},</if>
            <if test="taskCycle != null">#{taskCycle},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="advanceTime != null">#{advanceTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="currentStatus != null">#{currentStatus},</if>
            <if test="nextStartTime != null">#{nextStartTime},</if>
            <if test="menuStatus != null">#{menuStatus},</if>
            <if test="sort != null">#{sort},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="releaseInfo != null">#{releaseInfo},</if>
            <if test="progressInfo != null">#{progressInfo},</if>
            <if test="finishInfo != null">#{finishInfo},</if>
         </trim>
    </insert>

    <update id="updateTaskType" parameterType="TaskType">
        update task_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="periodicStatus != null">periodic_status = #{periodicStatus},</if>
            <if test="executor != null">executor = #{executor},</if>
            <if test="urgencyLevel != null">urgency_level = #{urgencyLevel},</if>
            <if test="taskCycle != null">task_cycle = #{taskCycle},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="advanceTime != null">advance_time = #{advanceTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="currentStatus != null">current_status = #{currentStatus},</if>
            <if test="nextStartTime != null">next_start_time = #{nextStartTime},</if>
            <if test="menuStatus != null">menu_status = #{menuStatus},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="releaseInfo != null">release_info = #{releaseInfo},</if>
            <if test="progressInfo != null">progress_info = #{progressInfo},</if>
            <if test="finishInfo != null">finish_info = #{finishInfo},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTaskTypeById" parameterType="Long">
        update task_type set del_flag = 2 where id = #{id}
    </update>

    <update id="deleteTaskTypeByIds" parameterType="String">
        update task_type set del_flag = 2 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>