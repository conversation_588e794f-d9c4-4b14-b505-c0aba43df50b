package com.base.system.domain.dto;

import lombok.Data;

/**
 * 审批回调类
 *
 * <AUTHOR>
 * @date 2022/11/17
 */
@Data
public class BpmnCallBackDto {

    /**
     * 时间类型
     * bpmn_instance_start-实例开始
     * bpmn_instance_end-实例结束
     * bpmn_task_start-任务开始
     * bpmn_task_end-任务结束
     * bpmn_task_delete-任务删除
     */
    private String eventType;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 任务实例id
     */
    private String taskInstanceId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 结果
     * agree-同意
     * refuse-拒绝
     * terminated-中止
     * run-进行中
     * not_start-未开始
     */
    private String result;

    /**
     * 结束时间
     */
    private String finishTime;

    /**
     * 当前任务完成人id
     */
    private String completeId;

    /**
     * 流程发起人id
     */
    private String createId;

    /**
     * 审批类型
     */
    private String auditType;

    /**
     * 审批类型名称
     */
    private String categoryName;

    public BpmnCallBackDto() {
    }

    @Override
    public String toString() {
        return "BpmnCallBackDto{" +
                "eventType='" + eventType + '\'' +
                ", processInstanceId='" + processInstanceId + '\'' +
                ", taskInstanceId='" + taskInstanceId + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", result='" + result + '\'' +
                ", finishTime='" + finishTime + '\'' +
                ", completeId='" + completeId + '\'' +
                ", createId='" + createId + '\'' +
                ", auditType='" + auditType + '\'' +
                '}';
    }
}
