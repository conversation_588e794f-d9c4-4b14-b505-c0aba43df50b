package com.base.common.core.domain.entity;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 菜单-字典 关联关系对象 sys_scene_dict
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
public class SysSceneDict extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 场景标识 */
    @Excel(name = "场景标识")
    private String scene;

    /** sys_dict_data 表中 dict_type */
    @Excel(name = "sys_dict_data 表中 dict_type")
    private String dictType;

    /** 别名 需要时候填写即可 */
    @Excel(name = "别名 需要时候填写即可")
    private String dictLabel;

    /** $column.columnComment */
    private Long joinId;

    /** dict_data 中的dict_code */
    @Excel(name = "dict_data 中的dict_code")
    private Long dictCode;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("scene", getScene())
            .append("dictType", getDictType())
            .append("dictLabel", getDictLabel())
            .append("joinId", getJoinId())
            .append("dictCode", getDictCode())
            .toString();
    }
}
