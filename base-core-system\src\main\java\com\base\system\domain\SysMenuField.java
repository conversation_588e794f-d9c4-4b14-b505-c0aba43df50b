package com.base.system.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;

@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 菜单-动态字段 关联关系对象 sys_menu_field
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public class SysMenuField extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 菜单ID */
    @Excel(name = "菜单ID")
    @NotNull(message = "所属菜单不能为空", groups = {SysMenuFieldAdd.class})
    private Long menuId;

    /** 动态字段ID */
    @Excel(name = "动态字段ID")
    @NotNull(message = "动态字段ID不能为空", groups = {SysMenuFieldAdd.class})
    private Long fieldId;

    /** 动态字段别名 */
    @Excel(name = "动态字段别名")
    private String fieldName;

    /** 主键 */
    @NotNull(message = "主键不能为空", groups = {SysMenuFieldSort.class})
    private Long joinId;

    /** 排序 */
    @Excel(name = "排序")
    @NotNull(message = "排序不能为空", groups = {SysMenuFieldSort.class})
    private Long sort;

    /** 动态字段表field_key */
    @Excel(name = "动态字段表field_key")
    @NotNull(message = "动态字段类型不能为空", groups = {SysMenuFieldAdd.class})
    private String fieldKey;

    public interface SysMenuFieldAdd{

    }

    public interface SysMenuFieldSort{

    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("menuId", getMenuId())
            .append("fieldId", getFieldId())
            .append("fieldName", getFieldName())
            .append("joinId", getJoinId())
            .append("sort", getSort())
            .append("fieldKey", getFieldKey())
            .toString();
    }
}
