package com.base.system.service;

import com.base.system.domain.SysMenuField;

import java.util.List;

/**
 * 菜单-动态字段 关联关系Service接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface ISysMenuFieldService
{
    /**
     * 查询菜单-动态字段 关联关系
     *
     * @param joinId 菜单-动态字段 关联关系主键
     * @return 菜单-动态字段 关联关系
     */
    public SysMenuField selectSysMenuFieldByJoinId(Long joinId);

    /**
     * 查询菜单-动态字段 关联关系列表
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 菜单-动态字段 关联关系集合
     */
    public List<SysMenuField> selectSysMenuFieldList(SysMenuField sysMenuField);

    /**
     * 新增菜单-动态字段 关联关系
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 结果
     */
    public int insertSysMenuField(SysMenuField sysMenuField);

    int batchSysMenuField(List<SysMenuField> sysMenuFieldList);

    /**
     * 修改菜单-动态字段 关联关系
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 结果
     */
    public int updateSysMenuField(SysMenuField sysMenuField);

    /**
     * 批量删除菜单-动态字段 关联关系
     *
     * @param joinIds 需要删除的菜单-动态字段 关联关系主键集合
     * @return 结果
     */
    public int deleteSysMenuFieldByJoinIds(Long[] joinIds);

    /**
     * 删除菜单-动态字段 关联关系信息
     *
     * @param joinId 菜单-动态字段 关联关系主键
     * @return 结果
     */
    public int deleteSysMenuFieldByJoinId(Long joinId);

    List<SysMenuField> selectSysMenuFieldList(Long menuId, String fieldKey, boolean queryAll);
    List<SysMenuField> selectSysMenuFieldList(Long menuId, List<String> fieldKey, boolean queryAll);
    List<SysMenuField> selectSysMenuFieldList(Long menuId, String fieldKey);
    List<SysMenuField> selectSysMenuFieldList(Long menuId, List<String> fieldKey);

    List<SysMenuField> selectByMenuId(Long menuId);

    void clear(Long menuId);
}
