version: "3.3"

services:

  base-core-admin:
    container_name: base-core-admin
    image: base-core-admin:latest
    environment:
      - LANG=C.UTF-8
      - LANGUAGE=C.UTF-8
      - LC_ALL=C.UTF-8
    build:
      context: .  # 这里把当前路径的上一级加到上下文环境中
      dockerfile: Dockerfile  #指定dockerfile
    stdin_open: true
    tty: true
    restart: always  # 服务器重启后docker自启动
    volumes:
      - ~/code/config/base-core-service:/config
      - ~/code/profile:/profile
      - ~/code/base-core-service:/app
    network_mode: host
