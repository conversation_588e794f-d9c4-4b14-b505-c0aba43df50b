package com.base.system.task;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.config.OAuth2Config;
import com.base.common.core.domain.entity.SysDept;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.utils.StringUtils;
import com.base.system.service.ISysDeptService;
import com.base.system.service.ISysUserService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * VAMA项目同步用户信息
 */
@Component("VamaUserDeptTask")
public class VamaUserDeptTask {

    private static final Logger log = LoggerFactory.getLogger(VamaUserDeptTask.class);

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysDeptService deptService;

    private static final String host = "http://**********:8087";

    public List<SysUser>  getUserByDeptId(String deptIdStr, String accessToken){
        /**
         {
         "code": 200,
         "message": null,
         "data": [
         {
         "Name": "CN=测试用户1",   # 名称
         "DistinguishedName": "CN=测试用户1,OU=Construction,OU=VAMA,DC=vamatest,DC=com",  # LDAP路径
         "ObjectGuid": "691a2c5c5f7d4037a29b1cd65a587ff4",  # 唯一标识
         "Properties": {
         "samAccountName": "testuser1",  # 账号
         "userAccountControl": "512",
         "displayName": "测试用户1",  # 显示名称
         "name": "测试用户1",  # 名称
         "description": null,  # 描述
         "userSort": "999"  # 排序值
         },
         "ObjectType": 4,  # 对象类型
         "ObjectTypeStr": "User"  # 对象类型名称
         }
         ]
         }
         */

        // 获取部门下用户信息
        ArrayList<SysUser> sysUserList = new ArrayList<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + accessToken);
        String userUrl = StringUtils.format("{}/Api/Org/{}/Children/User", host, deptIdStr);
        String userResString = HttpRequest.get(userUrl).addHeaders(headers).execute().body();
        JSONObject userRes = JSONObject.parseObject(userResString);
        log.info("获取用户信息, url: {}, deptId: {} headers: {}, res: {}", userUrl, deptIdStr, headers, userResString);
        if (!StringUtils.equals(userRes.get("code").toString(), "0")) {
            log.error("Vama同步用户信息失败, 获取用户失败, 部门ID: {}, 参数:{}, 返回值:{}", deptIdStr, accessToken, userRes);
            return sysUserList;
        }
        List<JSONObject> list = userRes.getList("data", JSONObject.class);
        list.forEach(userItem -> {
            JSONObject properties = userItem.getJSONObject("Properties");
            String username = properties.getString("samAccountName");
            String nickName = properties.getString("displayName");
            Integer status = properties.getInteger("userAccountControl");
            SysUser sysUser = new SysUser();
            sysUser.setUserName(username);
            sysUser.setNickName(nickName);
            sysUser.setStatus(getUserStatus(status));
            sysUser.setDelFlag("0");
            sysUser.setDeptId((long) Math.abs(deptIdStr.hashCode()));
            sysUserList.add(sysUser);
        });
        return sysUserList;
    }

    // 判断用户是否被停用的方法
    public static String getUserStatus(int userAccountControl) {
        // 与 0x0002 进行按位与操作
        return (userAccountControl & 0x0002) == 0x0002 ? "1" : "0";
    }

    public List<SysDept> getDeptList(String deptIdStr, String ancestors, String accessToken){
        List<SysDept> sysDeptList = new ArrayList<>();
        List<SysUser> sysUserList = new ArrayList<>();

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + accessToken);
        if (StringUtils.isNull(deptIdStr)) {
            return sysDeptList;
        }
        String deptUrl = StringUtils.format("{}/Api/Org/{}/Children/Org", host, deptIdStr);
        String deptResString = HttpRequest.get(deptUrl).addHeaders(headers).execute().body();
        JSONObject deptRes = JSONObject.parseObject(deptResString);
        log.info("获取部门信息, url: {}, deptId: {} headers: {}, res: {}", deptUrl, deptIdStr, headers, deptResString);
        /**
         {
         "code": 0,
         "message": null,
         "data": [
         {
         "HasChildOrganizationalUnit": true,
         "Name": "Chief Administrative Office",
         "DistinguishedName": "OU=Chief Administrative Office,OU=VAMA,DC=vamatest,DC=com",
         "ObjectGuid": "2765b4d46b7a41738c7c93ff03cbeb9b",
         "Properties": {
         "postalCode": "60010000"
         },
         "ObjectType": 2,
         "ObjectTypeStr": "OrganizationalUnit"
         }
         ]
         }
         */
        if (!StringUtils.equals(deptRes.get("code").toString(), "0")) {
            log.error("Vama同步部门信息失败, 获取部门失败, 部门ID: {}, 参数:{}, 返回值:{}", deptIdStr, accessToken, deptRes);
            return sysDeptList;
        }
        List<JSONObject> list = deptRes.getList("data", JSONObject.class);
        // 是否还有下级部门
        list.forEach(deptItem -> {
            Boolean hasChildOrganizationalUnit = deptItem.getBoolean("HasChildOrganizationalUnit");
            String deptId = deptItem.getString("ObjectGuid");
            JSONObject Properties = deptItem.getJSONObject("Properties");
            Long deptIdLong = (long) Math.abs(deptId.hashCode());
            SysDept sysDept = new SysDept();
            sysDept.setDeptId(deptIdLong);
            sysDept.setDeptName(deptItem.get("Name").toString());
            sysDept.setParentId((long) Math.abs(deptIdStr.hashCode()));
            sysDept.setAncestors(ancestors);
            sysDept.setStatus("0");
            sysDeptList.add(sysDept);
            if (hasChildOrganizationalUnit){
                // 获取用户信息
                sysDeptList.addAll(getDeptList(deptId, ancestors + "," + deptIdLong, accessToken));
            }
            sysUserList.addAll(this.getUserByDeptId(deptId, accessToken));
        });
        if (ObjectUtils.isNotEmpty(sysUserList)){
            // 保存/更新 用户信息
            sysUserService.importUser(sysUserList, true, "VamaTask");
        }
        return sysDeptList;
    }

    public void VamaUserDeptTask() {

        // 获取token
        String tokenUrl = host + "/token";
        Map<String, Object> tokenParams = new HashMap<>();
        tokenParams.put("client_id", OAuth2Config.getClientId());
        tokenParams.put("client_secret", OAuth2Config.getClientSecret());
        String tokenResString = HttpUtil.post(tokenUrl, JSONObject.toJSONString(tokenParams));
        log.info("获取Token, url: {}, params: {}, res: {}", tokenUrl, tokenParams, tokenResString);
        /**
         {
         "code": 0,  //不为0，则标识出错了。
         "message": "",
         "data": {
         "token": "M0E1NEMzOEItM0ZCRi01QTcyLTYxQTUtNkUwM0IyMjU1NTlCLjE3MjQwNjE5MzQubGJCK0loNzBnczJackNwV3g2YU9EMDY1SkhabW5NNWJVanh3MElVdEZqTT0=",  //token
         "expireTime": "1724061934"  //过期时间戳
         }
         }
         */
        JSONObject tokenRes = JSONObject.parseObject(tokenResString);
        String code = tokenRes.get("code").toString();
        if (!StringUtils.equals(code, "0")) {
            log.error("Vama同步部门/用户信息失败, 获取Token错误, 参数:{}, 返回值:{}", JSONObject.toJSONString(tokenParams), tokenResString);
            return;
        }
        String token = tokenRes.getJSONObject("data").getString("token");
        // 循环获取部门信息  基础ID为0
        String deptId = "0";

        List<SysDept> sysDeptList = this.getDeptList(deptId, deptId, token);
        if (ObjectUtils.isNotEmpty(sysDeptList)){
            deptService.importDept(sysDeptList, true, "VamaTask");
        }
    }
}
