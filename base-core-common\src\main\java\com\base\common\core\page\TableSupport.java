package com.base.common.core.page;

import com.base.common.core.text.Convert;
import com.base.common.utils.ServletUtils;
import com.base.common.utils.StringUtils;

/**
 * 表格数据处理
 *
 * <AUTHOR>
 */
public class TableSupport
{
    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";
    public static final String page_num = "page_num";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";
    public static final String page_size = "page_size";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 分页参数合理化
     */
    public static final String REASONABLE = "reasonable";

    /**
     * 封装分页对象
     */
    public static PageDomain getPageDomain()
    {
        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(Convert.toInt(StringUtils.isBlank(ServletUtils.getParameter(PAGE_NUM)) ? ServletUtils.getParameter(page_num) : ServletUtils.getParameter(PAGE_NUM), 1));
        pageDomain.setPageSize(Convert.toInt(StringUtils.isBlank(ServletUtils.getParameter(PAGE_SIZE)) ? ServletUtils.getParameter(page_size) : ServletUtils.getParameter(PAGE_SIZE), 10));
        pageDomain.setOrderByColumn(ServletUtils.getParameter(ORDER_BY_COLUMN));
        pageDomain.setIsAsc(ServletUtils.getParameter(IS_ASC));
        pageDomain.setReasonable(ServletUtils.getParameterToBool(REASONABLE));
        return pageDomain;
    }

    public static PageDomain buildPageRequest()
    {
        return getPageDomain();
    }
}
