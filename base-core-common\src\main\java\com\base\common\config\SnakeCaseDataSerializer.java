package com.base.common.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;

import java.io.IOException;

public class SnakeCaseDataSerializer extends JsonSerializer<Object> {

    private final ObjectMapper snakeMapper;

    public SnakeCaseDataSerializer() {
        this.snakeMapper = new ObjectMapper();
        this.snakeMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 将对象转换为中间结构，并使用 SnakeCase 序列化
        JsonNode tree = snakeMapper.valueToTree(value);
        gen.writeTree(tree);
    }
}
