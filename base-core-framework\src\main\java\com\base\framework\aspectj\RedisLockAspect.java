package com.base.framework.aspectj;

import com.base.common.annotation.RedisLock;
import com.base.common.constant.CacheConstants;
import com.base.common.core.redis.RedisCache;
import com.base.common.utils.CustomKeyParserUtils;
import com.base.common.utils.uuid.UUID;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.Duration;

@Aspect
@Component
public class RedisLockAspect {

    @Autowired
    private RedisCache redisCache;

    @Around("@annotation(redisLock)")
    public Object around(ProceedingJoinPoint joinPoint, RedisLock redisLock) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 解析 ${xxx} 表达式
        String key = CacheConstants.REDIS_LOCK_PREFIX + CustomKeyParserUtils.parse(redisLock.key(), method, joinPoint.getArgs());
        long expire = redisLock.expire();
        long waitTime = redisLock.waitTime();
        String lockValue = UUID.randomUUID().toString();

        long end = System.currentTimeMillis() + waitTime * 1000;
        while (System.currentTimeMillis() < end) {
            Boolean success = redisCache.setIfAbsent(key, lockValue, Duration.ofSeconds(expire));
            if (Boolean.TRUE.equals(success)) {
                try {
                    return joinPoint.proceed();
                } finally {
                    if (lockValue.equals(redisCache.getCacheObject(key))) {
                        redisCache.deleteObject(key);
                    }
                }
            }
            Thread.sleep(100);
        }

        throw new RuntimeException("获取Redis锁失败，key=" + key);
    }
}

