<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.TodoReadStatusMapper">

    <!-- 定义 resultMap -->
    <resultMap type="com.base.system.domain.TodoReadStatus" id="TodoReadStatusResult">
        <id     property="readStatusId" column="read_status_id" />
        <result property="userId"       column="user_id"        />
        <result property="todoId"       column="todo_id"        />
        <result property="readStatus"   column="read_status"    />
        <result property="readTime"     column="read_time"      />
        <result property="createTime"   column="create_time"    />
        <result property="updateTime"   column="update_time"    />
    </resultMap>

    <!-- 查询待办事项已读状态 -->
    <select id="selectTodoReadStatusById" parameterType="Long" resultMap="TodoReadStatusResult">
        SELECT *
        FROM todo_read_status
        WHERE read_status_id = #{readStatusId}
    </select>

    <!-- 查询待办事项已读状态列表 -->
    <select id="selectTodoReadStatusList" parameterType="com.base.system.domain.TodoReadStatus"
            resultMap="TodoReadStatusResult">
        SELECT * FROM todo_read_status
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="todoId != null">
                AND todo_id = #{todoId}
            </if>
            <if test="readStatus != null">
                AND read_status = #{readStatus}
            </if>
        </where>
    </select>

    <!-- 新增待办事项已读状态 -->
    <insert id="insertTodoReadStatus" parameterType="com.base.system.domain.TodoReadStatus">
        INSERT INTO todo_read_status (user_id, todo_id, read_status, read_time, create_time, update_time)
        VALUES (#{userId}, #{todoId}, #{readStatus}, #{readTime}, NOW(), NOW())
    </insert>

    <!-- 修改待办事项已读状态 -->
    <update id="updateTodoReadStatus" parameterType="com.base.system.domain.TodoReadStatus">
        UPDATE todo_read_status
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="todoId != null">
                todo_id = #{todoId},
            </if>
            <if test="readStatus != null">
                read_status = #{readStatus},
            </if>
            <if test="readTime != null">
                read_time = #{readTime},
            </if>
            update_time = NOW()
        </set>
        WHERE read_status_id = #{readStatusId}
    </update>

    <!-- 删除待办事项已读状态 -->
    <delete id="deleteTodoReadStatusById" parameterType="Long">
        DELETE
        FROM todo_read_status
        WHERE read_status_id = #{readStatusId}
    </delete>

    <!-- 批量删除待办事项已读状态 -->
    <delete id="deleteTodoReadStatusByIds" parameterType="Long[]">
        DELETE FROM todo_read_status WHERE read_status_id IN
        <foreach collection="array" item="readStatusId" open="(" separator="," close=")">
            #{readStatusId}
        </foreach>
    </delete>

</mapper>
