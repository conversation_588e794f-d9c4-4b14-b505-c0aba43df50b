package com.base.web.task;

import cn.hutool.core.date.DateUtil;
import com.base.common.utils.DateUtils;
import com.base.dex.domain.DevDataPointHour;
import com.base.dex.service.IDevDataPointHourService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 定时任务，生成热力图图片
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Component
public class HeatmapTask {
    private static final Logger logger = LoggerFactory.getLogger(HeatmapTask.class);

    @Autowired
    private IDevDataPointHourService devDataPointHourService;

    /**
     * 每天凌晨2点生成溯源24小时热力图图片
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void task() {
        logger.info("==========> 每天凌晨2点生成溯源24小时热力图图片定时任务(cron = \"0 0 2 * * ?\")启动...");
        try {
            Date date = DateUtil.date();
            logger.info("==========> task current date={}", date);
            // 当前时间前一天
            date = DateUtil.offsetDay(date, -1);
            logger.info("==========> task current date={}", date);
            // 监测指标：PM2.5、PM10 查询整个厂区PM2.5、PM10的小时均值
            DevDataPointHour devDataPointHour = new DevDataPointHour();

            List<String> hours = DateUtils.getBetweenHours(DateUtil.format(DateUtil.beginOfDay(date), "yyyy-MM-dd HH"),
                    DateUtil.format(DateUtil.endOfDay(date), "yyyy-MM-dd HH"));
            long time = System.currentTimeMillis();
            List<String> typeList = Arrays.asList("pm2_5", "pm10");
            for (String type : typeList) {
                for (String hour : hours) {
                    try {
                        // 开始日期 yyyy-MM-dd HH
                        devDataPointHour.setHour(hour);
                        devDataPointHour.setFactor(type);
                        // 查询颗粒物小时数据列表
                        List<DevDataPointHour> hourList = devDataPointHourService.getDevDataPointHourList(devDataPointHour);
                        // 生成24小时热力图图片
                        devDataPointHourService.getHeatMapImg(hourList, 1024, type, date, hour + "_");
                    } catch (Exception e) {
                        logger.error("==========> " + hour + "生成24小时热力图图片出错", e);
                        continue;
                    }
                }
            }
            long end = System.currentTimeMillis() - time;
            logger.debug("==========> 生成24小时热力图用时：" + (end / 1000) + "秒 <========== ");
            logger.debug("==========> 每天凌晨2点生成24小时热力图图片定时任务(cron = \"0 0 2 * * ?\")结束...");
        } catch (Exception e) {
            logger.error("==========> 每天凌晨2点生成24小时热力图图片定时任务出错", e);
        }
    }


}
