package com.base.device.service;

import com.base.device.domain.EnvDevicePoint;

import java.util.List;

/**
 * 设备-点位 关联关系Service接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface IEnvDevicePointService
{
    /**
     * 查询设备-点位 关联关系
     *
     * @param joinId 设备-点位 关联关系主键
     * @return 设备-点位 关联关系
     */
    public EnvDevicePoint selectEnvDevicePointByJoinId(Long joinId);

    /**
     * 查询设备-点位 关联关系列表
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 设备-点位 关联关系集合
     */
    public List<EnvDevicePoint> selectEnvDevicePointList(EnvDevicePoint envDevicePoint);

    List<EnvDevicePoint> selectByDeviceId(Long deviceId);

    List<EnvDevicePoint> selectByDeviceId(String pointId);

    /**
     * 新增设备-点位 关联关系
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 结果
     */
    public int insertEnvDevicePoint(EnvDevicePoint envDevicePoint);

    /**
     * 批量新增设备-点位 关联关系
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 结果
     */
    public int batchEnvDevicePoint(List<EnvDevicePoint> envDevicePointList);

    /**
     * 修改设备-点位 关联关系
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 结果
     */
    public int updateEnvDevicePoint(EnvDevicePoint envDevicePoint);

    /**
     * 批量删除设备-点位 关联关系
     *
     * @param joinIds 需要删除的设备-点位 关联关系主键集合
     * @return 结果
     */
    public int deleteEnvDevicePointByJoinIds(Long[] joinIds);

    /**
     * 删除设备-点位 关联关系信息
     *
     * @param joinId 设备-点位 关联关系主键
     * @return 结果
     */
    public int deleteEnvDevicePointByJoinId(Long joinId);
}
