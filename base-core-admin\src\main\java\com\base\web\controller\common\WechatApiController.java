package com.base.web.controller.common;

import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.utils.StringUtils;
import com.base.common.wechat.api.WechatApi;
import com.base.common.wechat.api.vo.QyMessageVo;
import com.base.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微信接口 Controller
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Api(tags = "任务管理")
@RestController
@RequestMapping("/api/wechat")
public class WechatApiController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(WechatApiController.class);

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private WechatApi wechatApi;

    /**
     * 企业微信-发送报警消息
     */
    @PostMapping("/sendAlarmQyMsg")
    @ApiOperation("企业微信-发送报警消息")
    public AjaxResult sendAlarmQyMsg(@RequestBody QyMessageVo item) {
        try {
            log.info("企业微信-发送报警消息入参：{}", item);
            if (item == null) {
                return AjaxResult.error("参数不能为空");
            }
            if (StringUtils.isEmpty(item.getQyUserId())) {
                return AjaxResult.error("企业微信用户ID不能为空");
            }
            if (StringUtils.isEmpty(item.getTitle())) {
                return AjaxResult.error("消息标题不能为空");
            }
            if (StringUtils.isEmpty(item.getDescription())) {
                return AjaxResult.error("消息内容不能为空");
            }
            if (StringUtils.isEmpty(item.getDetailUrl())) {
                return AjaxResult.error("跳转详情路径不能为空");
            }
            // http://127.0.0.1:8080/env-app/#/
            String appWebUrl = configService.selectConfigByKey("sys.app.web.url");
            // 企业微信-发送报警消息
            String result = wechatApi.sendAlarmQyMsg(item.getQyUserId(), item.getTitle(), item.getDescription(), appWebUrl + item.getDetailUrl());
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("=====> 企业微信-发送报警消息出错", e);
            return AjaxResult.error("企业微信-发送报警消息出错");
        }
    }

}
