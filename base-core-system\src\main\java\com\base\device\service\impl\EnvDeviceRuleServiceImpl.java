package com.base.device.service.impl;

import com.base.common.core.redis.RedisCache;
import com.base.common.enums.RunState;
import com.base.common.utils.StringUtils;
import com.base.device.domain.EnvDevice;
import com.base.device.domain.EnvDeviceRule;
import com.base.device.mapper.EnvDeviceRuleMapper;
import com.base.device.service.IEnvDeviceRuleService;
import com.base.dex.domain.DevBasePoint;
import com.base.dex.domain.dto.PointDataCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 设备运行规则(运行状态、报警等判断逻辑)Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
@Slf4j
public class EnvDeviceRuleServiceImpl implements IEnvDeviceRuleService
{
    @Autowired
    private EnvDeviceRuleMapper envDeviceRuleMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param ruleId 设备运行规则(运行状态、报警等判断逻辑)主键
     * @return 设备运行规则(运行状态、报警等判断逻辑)
     */
    @Override
    public EnvDeviceRule selectEnvDeviceRuleByRuleId(Long ruleId)
    {
        return envDeviceRuleMapper.selectEnvDeviceRuleByRuleId(ruleId);
    }

    /**
     * 查询设备运行规则(运行状态、报警等判断逻辑)列表
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 设备运行规则(运行状态、报警等判断逻辑)
     */
    @Override
    public List<EnvDeviceRule> selectEnvDeviceRuleList(EnvDeviceRule envDeviceRule)
    {
        return envDeviceRuleMapper.selectEnvDeviceRuleList(envDeviceRule);
    }

    /**
     * 新增设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 结果
     */
    @Override
    public int insertEnvDeviceRule(EnvDeviceRule envDeviceRule)
    {
        return envDeviceRuleMapper.insertEnvDeviceRule(envDeviceRule);
    }

    @Override
    public int batchEnvDeviceRule(List<EnvDeviceRule> envDeviceRuleList)
    {
        if (StringUtils.isNotEmpty(envDeviceRuleList)){
            return envDeviceRuleMapper.batchEnvDeviceRule(envDeviceRuleList);
        }
        return 0;
    }

    /**
     * 修改设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 结果
     */
    @Override
    public int updateEnvDeviceRule(EnvDeviceRule envDeviceRule)
    {
        return envDeviceRuleMapper.updateEnvDeviceRule(envDeviceRule);
    }

    /**
     * 批量删除设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param ruleIds 需要删除的设备运行规则(运行状态、报警等判断逻辑)主键
     * @return 结果
     */
    @Override
    public int deleteEnvDeviceRuleByRuleIds(Long[] ruleIds)
    {
        return envDeviceRuleMapper.deleteEnvDeviceRuleByRuleIds(ruleIds);
    }

    /**
     * 删除设备运行规则(运行状态、报警等判断逻辑)信息
     *
     * @param ruleId 设备运行规则(运行状态、报警等判断逻辑)主键
     * @return 结果
     */
    @Override
    public int deleteEnvDeviceRuleByRuleId(Long ruleId)
    {
        return envDeviceRuleMapper.deleteEnvDeviceRuleByRuleId(ruleId);
    }

    @Override
    public List<EnvDeviceRule> selectByDeviceIdIn(List<Long> deviceIds){
        return envDeviceRuleMapper.selectByDeviceIdIn(deviceIds);
    }


    public boolean getRuleResult(String rule, Map<String, PointDataCache> pointDict) {
        // 替换 ${point_id} 为 point_dict 中的 value 值
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)}");
        Matcher matcher = pattern.matcher(rule);
        StringBuffer newRule = new StringBuffer();

        while (matcher.find()) {
            String pointId = matcher.group(1);
            PointDataCache devPoint = pointDict.get(pointId);
            Double pointValue = devPoint.getValue();

            if (StringUtils.isNotNull(pointValue)){
                matcher.appendReplacement(newRule, String.valueOf(pointValue));
            }
        }
        matcher.appendTail(newRule);

        try {
            ScriptEngine engine = new ScriptEngineManager().getEngineByName("JavaScript");
            Object result = engine.eval(newRule.toString());
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("处理规则失败, rule: {}, new_rule: {} 失败原因: {}", rule, newRule, e.getMessage(), e);
            return false;
        }
    }

    public static final String[] RUN_STATE_NAME_LIST = {"运行", "电流", "水泵", "启停", "压差"};

    public RunState getRunState(EnvDevice envDevice, List<EnvDeviceRule> deviceRuleList,
                                       List<DevBasePoint> pointList, Map<String, PointDataCache> pointDataDict) {
        Map<String, DevBasePoint> pointBasicMap = new HashMap<>();
        if (pointList != null) {
            pointBasicMap = pointList.stream()
                    .collect(Collectors.toMap(
                            DevBasePoint::getPointId,
                            p -> p
                    ));
        }

        String mainType = envDevice.getMainType();
        if ("monitor".equals(mainType) || "station".equals(mainType)) {
            return RunState.NORMAL;
        }

        RunState runState = RunState.STOP;
        boolean defaultRuleFlag = true;

        if (StringUtils.isNotEmpty(deviceRuleList)) {
            deviceRuleList.sort(Comparator.comparingInt(r -> Math.toIntExact(r.getLevel())));

            for (EnvDeviceRule rule : deviceRuleList) {
                Long ruleRun = rule.getRun();
                String ruleResult = rule.getResult();
                Long ruleRunTime = rule.getRunTime();
                Long ruleId = rule.getRuleId();
                String expression = rule.getRule();

                if (ruleRun < 0) continue;
                if (!RunState.contains(ruleResult)) continue;

                if (RunState.RUN.getValue().equals(ruleResult)) {
                    defaultRuleFlag = false;
                }

                String deviceId = envDevice.getDeviceId().toString();
                String cacheKey = "device_rule_lock:" + deviceId + ":" + ruleId;

                boolean computeResult = getRuleResult(expression, pointDataDict);

                if (computeResult) {
                    if (ruleRunTime != null && ruleRunTime > 0) {
                        String ruleSt = redisCache.getCacheObject(cacheKey);
                        if (ruleSt != null) {
                            long now = System.currentTimeMillis() / 1000L;
                            if (now - Long.parseLong(ruleSt) < ruleRunTime) {
                                continue;
                            }
                        } else {
                            redisCache.setCacheObject(cacheKey, String.valueOf(System.currentTimeMillis() / 1000L));
                            continue;
                        }
                    }

                    runState = RunState.fromValue(ruleResult);
                    continue;
                } else {
                    redisCache.deleteObject(cacheKey);
                }
            }
        }

        if (defaultRuleFlag && pointList != null) {
            int runCount = 0;
            for (DevBasePoint point : pointList) {
                String pointCodeCn = point.getPointCodeCn();
                String pointId = point.getPointId();

                boolean matched = false;
                for (String keyword : RUN_STATE_NAME_LIST) {
                    if (pointCodeCn.contains(keyword)) {
                        matched = true;
                        break;
                    }
                }
                if (matched || pointList.size() == 1) {
                    PointDataCache pointData = pointDataDict.get(pointId);
                    Double value = pointData != null ? pointData.getValue() : null;
                    if (StringUtils.isNotNull(value)) {
                        if (Math.abs(value) >= 1) {
                            runCount++;
                        }
                    }
                }
            }

            if (runCount > 0) {
                runState = RunState.RUN;
            }
        }

        return runState;
    }

}
