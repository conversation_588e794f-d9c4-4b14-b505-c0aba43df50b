package com.base.common.wechat.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信配置信息
 *
 * <AUTHOR>
 * @date 2024-09-23 16:05
 */
@Component
@ConfigurationProperties(prefix = "qywechat")
public class WechatConfig {
    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 应用ID
     */
    private String agentId;

    /**
     * 应用的凭证密钥
     */
    private String corpSecret;

    /**
     * 企业微信AccessToken获取方式：1、密钥模式，2、接口，默认为1
     */
    private String accessTokenMode;

    /**
     * 企业微信AccessToken-接口地址
     */
    private String accessTokenUrl;

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getCorpSecret() {
        return corpSecret;
    }

    public void setCorpSecret(String corpSecret) {
        this.corpSecret = corpSecret;
    }

    public String getAccessTokenMode() {
        return accessTokenMode;
    }

    public void setAccessTokenMode(String accessTokenMode) {
        this.accessTokenMode = accessTokenMode;
    }

    public String getAccessTokenUrl() {
        return accessTokenUrl;
    }

    public void setAccessTokenUrl(String accessTokenUrl) {
        this.accessTokenUrl = accessTokenUrl;
    }
}
