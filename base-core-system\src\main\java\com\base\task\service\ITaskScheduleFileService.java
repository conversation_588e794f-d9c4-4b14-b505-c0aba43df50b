package com.base.task.service;

import com.base.task.domain.TaskScheduleFile;

import java.util.List;

/**
 * 任务进度文件Service接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public interface ITaskScheduleFileService {
    /**
     * 查询任务进度文件
     *
     * @param id 任务进度文件主键
     * @return 任务进度文件
     */
    public TaskScheduleFile selectTaskScheduleFileById(Long id);

    /**
     * 查询任务进度文件列表
     *
     * @param taskScheduleFile 任务进度文件
     * @return 任务进度文件集合
     */
    public List<TaskScheduleFile> selectTaskScheduleFileList(TaskScheduleFile taskScheduleFile);

    /**
     * 新增任务进度文件
     *
     * @param taskScheduleFile 任务进度文件
     * @return 结果
     */
    public int insertTaskScheduleFile(TaskScheduleFile taskScheduleFile);

    /**
     * 修改任务进度文件
     *
     * @param taskScheduleFile 任务进度文件
     * @return 结果
     */
    public int updateTaskScheduleFile(TaskScheduleFile taskScheduleFile);

    /**
     * 批量删除任务进度文件
     *
     * @param ids 需要删除的任务进度文件主键集合
     * @return 结果
     */
    public int deleteTaskScheduleFileByIds(Long[] ids);

    /**
     * 删除任务进度文件信息
     *
     * @param id 任务进度文件主键
     * @return 结果
     */
    public int deleteTaskScheduleFileById(Long id);
}
