package com.base.common.http;

import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

import java.lang.reflect.Proxy;

/**
 * <AUTHOR>
 * @date 2024/10/16
 */
public class ProxyFactoryBean<T> implements FactoryBean<T> {

    private final Class<T> interfaceType;

    @Autowired
    private Environment environment;

    public ProxyFactoryBean(Class<T> interfaceType) {
        this.interfaceType = interfaceType;
    }

    @Override
    public T getObject() {
        // 使用动态代理生成代理实例，将接口类和环境对象传入 HttpClientProxy
        return (T) Proxy.newProxyInstance(
            interfaceType.getClassLoader(),
            new Class[]{interfaceType},
            new HttpClientProxy(interfaceType, environment)
        );
    }

    @Override
    public Class<?> getObjectType() {
        return interfaceType;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }
}
