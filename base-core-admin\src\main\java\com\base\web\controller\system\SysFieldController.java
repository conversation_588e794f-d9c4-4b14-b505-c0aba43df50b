package com.base.web.controller.system;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.ServletUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.system.domain.SysField;
import com.base.system.domain.dto.SysFieldQueryDTO;
import com.base.system.domain.dto.SysMenuFieldQueryDTO;
import com.base.system.service.ISysFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * 动态字段Controller
 *
 * <AUTHOR>
 * @date  2025-04-29
 */
@RestController
@RequestMapping("/sys/field")
public class SysFieldController extends BaseController
{
    @Autowired
    private ISysFieldService sysFieldService;

    /**
     * 分页查询动态字段列表
     */
    @PreAuthorize("@ss.hasPermi('system:field:list')")
    @RequestMapping("/page")
    public TableDataInfo page(@RequestBody SysField sysField)
    {
        startPage();
        List<SysField> list = sysFieldService.selectSysFieldList(sysField);
        return getDataTable(list);
    }

    /**
     * 查询动态字段列表
     */
    @PreAuthorize("@ss.hasPermi('system:field:list')")
    @RequestMapping("/list")
    public Object list(@RequestBody SysFieldQueryDTO fieldQueryDTO)
    {
        boolean pageFlag = StringUtils.isNotBlank(ServletUtils.getParameter("page_num")) && StringUtils.isNotBlank(ServletUtils.getParameter("page_size"));
        if (pageFlag) {
            startPage();
        }
        List<SysField> list = sysFieldService.selectSysFieldList(fieldQueryDTO.getSysField());
        if (pageFlag) {
            return getDataTableSnake(list);
        }
        return AjaxResultSnake.success(list);
    }

    @PreAuthorize("@ss.hasPermi('system:field:list')")
    @GetMapping("/listByFieldKey")
    public AjaxResultSnake list(String fieldKey)
    {
        List<SysField> list = sysFieldService.selectByFieldKey(fieldKey);
        return AjaxResultSnake.success(list);
    }

    @PreAuthorize("@ss.hasPermi('system:field:list')")
    @RequestMapping("/menu/list")
    public AjaxResultSnake menuList(@RequestBody @Validated SysMenuFieldQueryDTO menuFieldQueryDTO)
    {
        List<String> fieldList;
        if (menuFieldQueryDTO.getFieldKey() instanceof String){
            fieldList = Collections.singletonList(menuFieldQueryDTO.getFieldKey().toString());
        }else if (menuFieldQueryDTO.getFieldKey() instanceof List) {
            fieldList = (List<String>) menuFieldQueryDTO.getFieldKey();
        }else{
            return AjaxResultSnake.error("缺少参数");
        }
        List<SysField> list = sysFieldService.selectByMenuAndFieldKey(menuFieldQueryDTO.getMenuId(), fieldList, menuFieldQueryDTO.isQueryAll());
        return AjaxResultSnake.success(list);
    }

    /**
     * 导出动态字段列表
     */
    @PreAuthorize("@ss.hasPermi('system:field:export')")
    @Log(title = "动态字段", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysField sysField)
    {
        List<SysField> list = sysFieldService.selectSysFieldList(sysField);
        ExcelUtil<SysField> util = new ExcelUtil<SysField>(SysField.class);
        util.exportExcel(response, list, "动态字段数据");
    }

    /**
     * 获取动态字段详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:field:query')")
    @GetMapping(value = "get")
    public AjaxResultSnake getInfo(@RequestParam("field_id") Long fieldId)
    {
        return AjaxResultSnake.success(sysFieldService.selectSysFieldByFieldId(fieldId));
    }

    /**
     * 新增动态字段
     */
    @PreAuthorize("@ss.hasPermi('system:field:add')")
    @Log(title = "动态字段", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResultSnake add(@RequestBody @Validated(SysField.SysFieldAdd.class) SysField sysField)
    {
        return AjaxResultSnake.success(sysFieldService.insertSysField(sysField));
    }

    /**
     * 修改动态字段
     */
    @PreAuthorize("@ss.hasPermi('system:field:edit')")
    @Log(title = "动态字段", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResultSnake edit(@RequestBody  @Validated(SysField.SysFieldEdit.class) SysField sysField)
    {
        return AjaxResultSnake.success(sysFieldService.updateSysField(sysField));
    }

    /**
     * 删除动态字段
     */
    @PreAuthorize("@ss.hasPermi('system:field:remove')")
    @Log(title = "动态字段", businessType = BusinessType.DELETE)
    @GetMapping("delete")
    public AjaxResultSnake remove(@RequestParam("field_id") Long fieldId)
    {
        return AjaxResultSnake.success(sysFieldService.deleteSysFieldByFieldId(fieldId));
    }


    /**
     * 复制动态字段
     *
     * @return 响应结果
     */
    @PostMapping("/replicate")
    public AjaxResultSnake replicate() {
        // 日志记录和权限校验通常通过拦截器或AOP实现，这里仅展示核心逻辑
        return AjaxResultSnake.success(sysFieldService.replicate());
    }
}
