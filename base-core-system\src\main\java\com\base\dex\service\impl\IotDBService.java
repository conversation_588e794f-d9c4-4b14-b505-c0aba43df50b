package com.base.dex.service.impl;

import com.base.common.config.IotProperties;
import com.base.common.utils.StringUtils;
import com.base.dex.domain.IotBaseRecord;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.isession.SessionDataSet;
import org.apache.iotdb.isession.pool.SessionDataSetWrapper;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.apache.iotdb.session.pool.SessionPool;
import org.apache.tsfile.enums.TSDataType;
import org.apache.tsfile.file.metadata.enums.CompressionType;
import org.apache.tsfile.file.metadata.enums.TSEncoding;
import org.apache.tsfile.read.common.Field;
import org.apache.tsfile.read.common.RowRecord;
import org.apache.tsfile.write.record.Tablet;
import org.apache.tsfile.write.schema.MeasurementSchema;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class IotDBService {

    @Resource(name="IotSessionPool")
    SessionPool sessionPool;

    @Resource
    IotProperties iotProperties;


    /**
     * 创建数据库
     *
     * @param dataenv 数据库名称 例如 root.ln
     */
    public void createDataenv(String dataenv) throws IoTDBConnectionException, StatementExecutionException {
        sessionPool.createDatabase(dataenv);
    }

    /**
     * 删除时间序列(单路径)
     */
    public void deleteTimeSeries(String path) throws IoTDBConnectionException, StatementExecutionException {
        path = this.formatPath(path);
        sessionPool.deleteTimeseries(path);
    }

    /**
     * 删除时间序列(多路径)
     */
    public void deleteTimeSeries(List<String> paths) throws IoTDBConnectionException, StatementExecutionException {
        paths = this.formatPath(paths);
        sessionPool.deleteTimeseries(paths);
    }

    /**
     * 检测时间序列是否存在
     */
    public boolean checkTimeSeriesExists(String path) throws IoTDBConnectionException, StatementExecutionException {
        path = this.formatPath(path);
        return sessionPool.checkTimeseriesExists(path);
    }

    /**
     * Tablet 数据写入(单个)
     */
    public void insertAlignedTablet(Tablet tablet) throws IoTDBConnectionException, StatementExecutionException {
        tablet.deviceId = this.formatPath(tablet.deviceId);
        sessionPool.insertAlignedTablet(tablet);
    }

    /**
     * Tablet 数据写入(多个)
     */
    public void insertAlignedTablets(Map<String, Tablet> tablets) throws IoTDBConnectionException, StatementExecutionException {
        Map<String, Tablet> formattedTablets = new HashMap<>();
        for (Map.Entry<String, Tablet> entry : tablets.entrySet()) {
            String formattedPath = this.formatPath(entry.getKey());
            Tablet tablet = entry.getValue();
            tablet.deviceId = formattedPath;
            // 将格式化后的路径和原有的Tablet对象添加到新Map中
            formattedTablets.put(formattedPath, tablet);
        }
        sessionPool.insertAlignedTablets(formattedTablets);
    }

    /**
     * 时间序列原始数据范围查询，指定的查询时间范围为左闭右开区间，包含开始时间但不包含结束时间；
     * timeout超时（毫秒）
     */
    public List<Map<String, Object>> executeRawDataQuery(List<String> paths, long startTime, long endTime, long timeout) throws IoTDBConnectionException, StatementExecutionException {
        paths = this.formatPath(paths);
        SessionDataSetWrapper wrapper = sessionPool.executeRawDataQuery(paths, startTime, endTime, timeout);
        SessionDataSet dataSet = wrapper.getSessionDataSet();
        return mappingResultSet(dataSet);
    }

    public List<Map<String, Object>> executeRawDataQuery(List<String> paths, long startTime, long endTime) throws IoTDBConnectionException, StatementExecutionException {
        return this.executeRawDataQuery(paths, startTime, endTime, 5000L);
    }

    /**
     * 直接执行查询语句
     */
    @SneakyThrows
    public List<Map<String, Object>> executeQueryStatement(String sql){
        String newSql = this.replaceDashesWithUnderscores(sql);
        try {
            SessionDataSetWrapper wrapper = sessionPool.executeQueryStatement(newSql);
            SessionDataSet dataSet = wrapper.getSessionDataSet();
            return mappingResultSet(dataSet);
        } catch (Exception e){
            log.error("IotDB sql执行失败:{}", newSql, e);
            throw e;
        }
    }

    private List<Map<String, Object>> mappingResultSet(SessionDataSet dataSet) throws StatementExecutionException, IoTDBConnectionException {
        List<String> columnNames = dataSet.getColumnNames();
        List<Map<String, Object>> resultList = new ArrayList<>();
        while (dataSet.hasNext()) {
            RowRecord rowRecord = dataSet.next();
            List<Field> fields = rowRecord.getFields();
            Map<String, Object> map = new HashMap<>();
            //time表示数据采集时间
            map.put("time", new Date(rowRecord.getTimestamp()));
            for (int i = 0; i < fields.size(); i++) {
                String[] columnNameList = StringUtils.split(columnNames.get(i + 1), ".");
                map.put(columnNameList[columnNameList.length-1], fields.get(i).getStringValue());
            }
            resultList.add(map);
        }
        return resultList;
    }

    /**
     * 将 IotBaseRecord 对象列表转换为 IoTDB Tablet
     *
     * @param deviceId 设备 ID
     * @param records  数据对象列表
     * @return 生成的 Tablet
     */
    public static Tablet convertToTablet(String deviceId, List<? extends IotBaseRecord> records) {
        if (records == null || records.isEmpty()) {
            throw new IllegalArgumentException("数据列表不能为空！");
        }

        // 通过反射获取所有字段
        Class<?> clazz = records.get(0).getClass();
        java.lang.reflect.Field[] fields = clazz.getDeclaredFields();
        List<MeasurementSchema> schemaList = new ArrayList<>();

        for (java.lang.reflect.Field field : fields) {
            if ("time".equals(field.getName())) continue; // 跳过 time 字段
            TSDataType tsDataType = getDataType(field.getType());
            schemaList.add(new MeasurementSchema(field.getName(), tsDataType, TSEncoding.PLAIN, CompressionType.SNAPPY));
        }

        // 创建 Tablet
        Tablet tablet = new Tablet(deviceId, schemaList);

        // 遍历数据对象并填充 Tablet
        for (IotBaseRecord record : records) {
            int rowIndex = tablet.rowSize++;
            tablet.addTimestamp(rowIndex, record.getTime().getTime());

            for (MeasurementSchema measurementSchema : schemaList) {
                try {
                    java.lang.reflect.Field field = clazz.getDeclaredField(measurementSchema.getMeasurementId());
                    field.setAccessible(true);
                    Object value = field.get(record);

                    if (value instanceof Number) {
                        tablet.addValue(field.getName(), rowIndex, ((Number) value).doubleValue());
                    } else if (value instanceof String) {
                        tablet.addValue(field.getName(), rowIndex, value.toString());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return tablet;
    }

    /**
     * 根据 Java 类型返回对应的 IoTDB TSDataType
     */
    private static TSDataType getDataType(Class<?> type) {
        if (Number.class.isAssignableFrom(type) || type == int.class || type == float.class || type == double.class
                || type == long.class || type == short.class || type == byte.class || type == BigDecimal.class) {
            return TSDataType.DOUBLE;
        } else if (type == String.class) {
            return TSDataType.TEXT;
        } else {
            throw new IllegalArgumentException("不支持的类型: " + type.getSimpleName());
        }
    }

    private List<String> formatPath(List<String> paths) {
        List<String> formattedPaths = new ArrayList<>();
        for (String path : paths) {
            formattedPaths.add(this.formatPath(path)); // 调用单路径处理方法
        }
        return formattedPaths;
    }

    private String formatPath(String path){
        if(StringUtils.startsWith(path, "root.") && StringUtils.split(path).length <= 1){
            return path;
        }
        String dataenv = iotProperties.getDataenv();
        if (StringUtils.isBlank(dataenv)){
            dataenv = "root.env";
        }
        return StringUtils.format("{}.{}", dataenv, path);
    }

    public String replaceDashesWithUnderscores(String sqlQuery) {
        String sqlQueryLower = sqlQuery.toLowerCase();
        String fromKeyword = "from";
        String[] keywords = {"where", "group by", "order by", "limit"};

        int fromIndex = sqlQueryLower.indexOf(fromKeyword);

        if (fromIndex == -1) {
            return sqlQuery;
        }

        int tableNameStart = fromIndex + fromKeyword.length();
        int tableNameEnd = sqlQuery.length();

        for (String keyword : keywords) {
            int keywordIndex = sqlQueryLower.indexOf(keyword, tableNameStart);
            if (keywordIndex != -1 && keywordIndex < tableNameEnd) {
                tableNameEnd = keywordIndex;
            }
        }

        String tableName = sqlQuery.substring(tableNameStart, tableNameEnd).trim();
        String modifiedTableName = formatPath(tableName.replace("-", "_"));

        return sqlQuery.substring(0, tableNameStart) + " " + modifiedTableName + " " + sqlQuery.substring(tableNameEnd);
    }

}




