<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.TodoItemsMapper">

    <resultMap type="com.base.system.domain.TodoItems" id="TodoItemsResult">
        <id property="todoId" column="todo_id"/>
        <result property="title" column="title"/>
        <result property="status" column="status"/>
        <result property="content" column="content"/>
        <result property="tag" column="tag"/>
        <result property="senderId" column="sender_id"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="remark" column="remark"/>
        <result property="sceneType" column="scene_type"/>
        <result property="productId" column="product_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="urlParams" column="url_params"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="extendId" column="extend_id"/>
    </resultMap>

    <select id="selectTodoItemsById" parameterType="Long" resultMap="TodoItemsResult">
        SELECT todo_id,
               title,
               status,
               content,
               tag,
               sender_id,
               receiver_id,
               remark,
               scene_type,
               product_id,
               menu_id,
               url_params,
               create_time,
               update_time,
               url_params,
               extend_id
        FROM todo_items
        WHERE todo_id = #{todoId}
    </select>

    <select id="selectTodoItemsList" parameterType="com.base.system.domain.TodoItems" resultMap="TodoItemsResult">
        SELECT todo_id, title, status, content, tag, sender_id, receiver_id, remark, scene_type, product_id, menu_id, url_params, extend_id,
        create_time, update_time
        FROM todo_items
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="receiverId != null">
                AND sender_id = #{receiverId}
            </if>
            <if test="extendId != null">
                AND extend_id = #{extendId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectTodoItemsByQuery" parameterType="com.base.system.domain.vo.TodoItemsQueryVO"
        resultMap="TodoItemsResult">
        SELECT todo_id, title, status, content, tag, sender_id, receiver_id, remark, scene_type, product_id, menu_id, url_params, extend_id,
        create_time, update_time
        FROM todo_items
        <where>
            <if test="receiverId != null">
                AND receiver_id = #{receiverId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        <!-- 新增：动态排序 -->
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy} DESC
        </if>
        <!-- 默认排序 -->
        <if test="orderBy == null or orderBy == ''">
            ORDER BY create_time DESC
        </if>
    </select>

    <!-- 统计各状态的待办事项数量（增加用户过滤） -->
    <select id="countTodoItemsByStatus" resultType="map">
        SELECT
            status AS status,
            COUNT(*) AS count
        FROM
            todo_items
        WHERE receiver_id = #{userId}
        GROUP BY
            status
    </select>

    <insert id="insertTodoItems" parameterType="com.base.system.domain.TodoItems">
        INSERT INTO todo_items (
        <if test="title != null and title != ''">title,</if>
        <if test="content != null">content,</if>
        <if test="tag != null">tag,</if>
        <if test="status != null">status,</if>
        <if test="senderId != null">sender_id,</if>
        <if test="receiverId != null">receiver_id,</if>
        <if test="createBy != null">create_by,</if>
        <if test="createTime != null">created_time,</if>
        <if test="updateBy != null">update_by,</if>
        <if test="updateTime != null">update_time,</if>
        <if test="remark != null">remark,</if>
        <if test="sceneType != null">scene_type,</if>
        <if test="productId != null">product_id,</if>
        <if test="menuId != null">menu_id,</if>
        <if test="urlParams != null and urlParams != ''">url_params,</if>
        <if test="extendId != null and extendId != ''">extend_id,</if>
        create_time
        ) VALUES (
        <if test="title != null and title != ''">#{title},</if>
        <if test="content != null">#{content},</if>
        <if test="tag != null">#{tag},</if>
        <if test="status != null">#{status},</if>
        <if test="senderId != null">#{senderId},</if>
        <if test="receiverId != null">#{receiverId},</if>
        <if test="createBy != null">#{createBy},</if>
        <if test="createTime != null">#{createTime},</if>
        <if test="updateBy != null">#{updateBy},</if>
        <if test="updateTime != null">#{updateTime},</if>
        <if test="remark != null">#{remark},</if>
        <if test="sceneType != null">#{sceneType},</if>
        <if test="productId != null">#{productId},</if>
        <if test="menuId != null">#{menuId},</if>
        <if test="urlParams != null and urlParams != ''">#{urlParams},</if>
        <if test="extendId != null and extendId != ''">#{extendId},</if>
        current_timestamp
        )
    </insert>

    <update id="updateTodoItems" parameterType="com.base.system.domain.TodoItems">
        UPDATE todo_items
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="tag != null">
                tag = #{tag},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="senderId != null">
                sender_id = #{senderId},
            </if>
            <if test="receiverId != null">
                receiver_id = #{receiverId},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                created_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="sceneType != null">
                scene_type = #{sceneType},
            </if>
            <if test="productId != null">
                product_id = #{productId},
            </if>
            <if test="menuId != null">
                menu_id = #{menuId},
            </if>
            <if test="urlParams != null and urlParams != ''">
                url_params = #{urlParams},
            </if>
            <if test="extendId != null and extendId != ''">
                extend_id = #{extendId},
            </if>
        </set>
        WHERE todo_id = #{todoId}
    </update>

    <delete id="deleteTodoItemsById" parameterType="Long">
        DELETE
        FROM todo_items
        WHERE todo_id = #{todoId}
    </delete>

    <delete id="deleteTodoItemsByIds" parameterType="Long[]">
        DELETE FROM todo_items WHERE todo_id IN
        <foreach collection="array" item="todoId" open="(" separator="," close=")">
            #{todoId}
        </foreach>
    </delete>

</mapper>
