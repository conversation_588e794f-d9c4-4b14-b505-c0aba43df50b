package com.base.system.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 国/省控站对象 sys_station
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SysStation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 国控站ID */
    @JsonAlias("station_id")
    @NotNull(message = "要修改的站点不能为空", groups = SysStationEdit.class)
    private Long stationId;

    /** 国控站名称 */
    @Excel(name = "国控站名称")
    @JsonAlias("station_name")
    private String stationName;

    /** 国控站位置 */
    @Excel(name = "国控站位置")
    @JsonAlias("station_location")
    private String stationLocation;

    /** 国控站类型 */
    @Excel(name = "国控站类型")
    @JsonAlias("station_type")
    private String stationType;

    /** 状态  0正常 1隐藏 */
    @Excel(name = "状态  0正常 1隐藏")
    @JsonAlias("status")
    private Long status;

    /** x坐标 */
    @Excel(name = "x坐标")
    @JsonAlias("pos_x")
    private String posX;

    /** y坐标 */
    @Excel(name = "y坐标")
    @JsonAlias("pos_y")
    private String posY;

    /** z坐标 */
    @Excel(name = "z坐标")
    @JsonAlias("pos_z")
    private String posZ;

    /** 删除标识 */
    @JsonAlias("del_flag")
    private Long delFlag;

    public interface SysStationEdit{

    }
}
