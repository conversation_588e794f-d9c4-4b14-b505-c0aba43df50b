package com.base.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Data
@Component
@ConfigurationProperties
public class RedisProperties {

    private String host = "localhost";
    private int port = 6379;
    private String password;
    private int database = 0;
    private Duration timeout;

    private Pool pool = new Pool();

    public static class Pool {
        private int maxActive = 8;
        private int maxIdle = 8;
        private int minIdle = 0;
        private long maxWait = -1;
    }

    public RedisStandaloneConfiguration getStandaloneConfig() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(host);
        config.setPort(port);
        config.setPassword(password != null ? RedisPassword.of(password) : RedisPassword.none());
        config.setDatabase(database);
        return config;
    }

    public JedisClientConfiguration getJedisClientConfiguration() {
        return JedisClientConfiguration.builder()
                .connectTimeout(timeout)
                .readTimeout(timeout)
                .build();
    }
}
