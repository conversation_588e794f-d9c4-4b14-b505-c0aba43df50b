package com.base.common.utils;

public class SnakeAndCamel {

    public static String toCamelCase(String snakeStr) {
        if (snakeStr == null || snakeStr.isEmpty()) {
            return snakeStr;
        }

        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;

        for (char c : snakeStr.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }

        return result.toString();
    }

}
