<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.device.mapper.EnvDevicePointMapper">

    <resultMap type="EnvDevicePoint" id="EnvDevicePointResult">
        <result property="deviceId"    column="device_id"    />
        <result property="pointId"    column="point_id"    />
        <result property="joinId"    column="join_id"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="defaultFlag"    column="default"    />
        <result property="lowLimit"    column="low_limit"    />
    </resultMap>

    <sql id="selectEnvDevicePointVo">
        select device_id, point_id, join_id, sort, status, "default", low_limit from device.env_device_point
    </sql>

    <select id="selectEnvDevicePointList" parameterType="EnvDevicePoint" resultMap="EnvDevicePointResult">
        <include refid="selectEnvDevicePointVo"/>
        <where>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="pointId != null  and pointId != ''"> and point_id = #{pointId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="defaultFlag != null "> and "default" = #{defaultFlag}</if>
            <if test="lowLimit != null "> and low_limit = #{lowLimit}</if>
        </where>
    </select>

    <select id="selectEnvDevicePointByJoinId" parameterType="Long" resultMap="EnvDevicePointResult">
        <include refid="selectEnvDevicePointVo"/>
        where join_id = #{joinId}
    </select>

    <insert id="insertEnvDevicePoint" parameterType="EnvDevicePoint" useGeneratedKeys="true" keyProperty="joinId">
        insert into device.env_device_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="pointId != null and pointId != ''">point_id,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="defaultFlag != null">"default",</if>
            <if test="lowLimit != null">"low_limit",</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="pointId != null and pointId != ''">#{pointId},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="defaultFlag != null">#{defaultFlag},</if>
            <if test="lowLimit != null">#{lowLimit},</if>
         </trim>
    </insert>

    <update id="updateEnvDevicePoint" parameterType="EnvDevicePoint">
        update device.env_device_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="pointId != null and pointId != ''">point_id = #{pointId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="defaultFlag != null">"default" = #{defaultFlag},</if>
            <if test="lowLimit != null">low_limit = #{lowLimit},</if>
        </trim>
        where join_id = #{joinId}
    </update>

    <delete id="deleteEnvDevicePointByJoinId" parameterType="Long">
        delete from device.env_device_point where join_id = #{joinId}
    </delete>

    <delete id="deleteEnvDevicePointByJoinIds" parameterType="String">
        delete from device.env_device_point where join_id in
        <foreach item="joinId" collection="array" open="(" separator="," close=")">
            #{joinId}
        </foreach>
    </delete>

    <insert id="batchEnvDevicePoint">
        insert into device.env_device_point( device_id, point_id, status, "default", low_limit) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.deviceId}, #{item.pointId}, #{item.status}, #{item.defaultFlag}, #{item.lowLimit})
        </foreach>
    </insert>
</mapper>
