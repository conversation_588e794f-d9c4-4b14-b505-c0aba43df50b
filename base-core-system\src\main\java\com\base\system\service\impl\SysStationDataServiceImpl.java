package com.base.system.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.core.redis.RedisCache;
import com.base.common.utils.DateUtils;
import com.base.common.utils.http.HttpUtils;
import com.base.system.domain.SysStation;
import com.base.system.domain.SysStationData;
import com.base.system.mapper.SysStationDataMapper;
import com.base.system.service.ISysConfigService;
import com.base.system.service.ISysStationDataService;
import com.base.system.service.ISysStationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
@Slf4j
public class SysStationDataServiceImpl implements ISysStationDataService
{
    @Autowired
    private SysStationDataMapper sysStationDataMapper;

    @Autowired
    private ISysStationService sysStationService;

    @Autowired
    private ISysConfigService sysConfigService;

     @Autowired
     RedisCache redisCache;

    /**
     * 查询【请填写功能名称】
     *
     * @param regionId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SysStationData selectSysStationDataById(int regionId)
    {
        return sysStationDataMapper.selectSysStationDataById(regionId);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sysStationData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SysStationData> selectSysStationDataList(SysStationData sysStationData)
    {
        return sysStationDataMapper.selectSysStationDataList(sysStationData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param sysStationData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSysStationData(SysStationData sysStationData)
    {
        return sysStationDataMapper.insertSysStationData(sysStationData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param sysStationData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSysStationData(SysStationData sysStationData)
    {
        return sysStationDataMapper.updateSysStationData(sysStationData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysStationDataByIds(int[] ids)
    {
        return sysStationDataMapper.deleteSysStationDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysStationDataByRegionId(int id)
    {
        return sysStationDataMapper.deleteSysStationDataById(id);
    }

    @Override
    public JSONArray getStationData() {
        Date queryTime = DateUtils.clearToHour(DateUtils.addHours(new Date(), -2));
        return this.getStationData(queryTime);
    }

    @Override
    public JSONArray getStationData(Date date) {
        JSONArray resp = new JSONArray();
        SysStationData queryStationData = new SysStationData().setTime(date);
        Map<String, SysStationData> stationDataMap = this.selectSysStationDataList(queryStationData).stream().collect(Collectors.toMap(SysStationData::getPointName, sysStation1 -> sysStation1));

        SysStation sysStationQuery = new SysStation().setStatus(0L);
        List<SysStation> sysStations = sysStationService.selectSysStationList(sysStationQuery);

        sysStations.forEach(sysStation -> {
            String stationName = sysStation.getStationName();
            SysStationData sysStationData = stationDataMap.get(stationName);
            String pm25 = (sysStationData != null && sysStationData.getPm25() != null) ? sysStationData.getPm25() : "0";
            String pm10 = (sysStationData != null && sysStationData.getPm10() != null) ? sysStationData.getPm10() : "0";

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("pointname", stationName);
            jsonObject.put("pm25", pm25);
            jsonObject.put("pm10", pm10);
            resp.add(jsonObject);
        });
        return resp;
    }

    @Override
    public JSONArray getStationDataChart(Date startTime) {
        // 查询数据
        List<SysStationData> sysStationDataList = sysStationDataMapper.selectStationDataByTime(startTime);

        // 查询要显示的点位
        SysStation sysStationQuery = new SysStation().setStatus(0L);
        Set<String> validStationNames = sysStationService.selectSysStationList(sysStationQuery).stream()
                .map(SysStation::getStationName)
                .collect(Collectors.toSet());

        // 过滤无需显示的数据
        sysStationDataList = sysStationDataList.stream()
                .filter(data -> validStationNames.contains(data.getPointName()))
                .collect(Collectors.toList());

        // 查询厂界微站的数据
        // 等待设备服务
        return null;
    }

    private static final String AIR_API_URL = "http://wx.e9st.cn/airpoint/get/city";


    public void airTask() throws UnsupportedEncodingException {

        // 获取当前时间往前推一个小时
        LocalDateTime beforeOneHour = LocalDateTime.now().minusHours(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:00:00");
        String formattedTime = beforeOneHour.format(formatter);

        // 获取城市名称
        String cityName = sysConfigService.selectConfigByKey("sys.corp.city");

        if (StringUtils.isBlank(cityName)){
            log.error("未找到城市信息, 无法获取国控站数据");
            return;
        }
        // 构造请求 URL
        String encodedCityName = URLEncoder.encode(cityName, String.valueOf(StandardCharsets.UTF_8));
        String url = String.format("%s?cityname=%s&datetime=%s", AIR_API_URL, encodedCityName, formattedTime);

        try {
            String airData = HttpUtils.sendGet(url);

            List<JSONObject> airJsonList = JSONArray.parseArray(airData, JSONObject.class);

            List<SysStation> stationList = sysStationService.selectSysStationList(new SysStation());

            Map<String, JSONObject> airMap = new HashMap<>();
            if (airJsonList != null) {
                for (JSONObject node : airJsonList) {
                    String pointName = node.getString("pointName");
                    airMap.put(pointName, node);
                }
            }

            List<SysStationData> stationDataList = new ArrayList<>();

            for (SysStation station : stationList) {
                JSONObject airDataNode = airMap.get(station.getStationName());
                if (airDataNode != null) {
                    SysStationData stationData = new SysStationData();
                    stationData.setPointName(station.getStationName());
                    stationData.setPointCode(String.valueOf(station.getStationId()));
                    stationData.setRegionName(cityName);
                    stationData.setPm25(airDataNode.getString("pm25"));
                    stationData.setPm10(airDataNode.getString("pm10"));
                    stationData.setSo2(airDataNode.getString("so2"));
                    stationData.setNo2(airDataNode.getString("no2"));
                    stationData.setO3(airDataNode.getString("o3"));
                    stationData.setCo(airDataNode.getString("co"));
                    stationData.setAqiScore(airDataNode.getString("aqi"));
                    stationData.setAqiTrend(airDataNode.getString("aqi"));
                    stationData.setTime(DateUtils.parseDate(formattedTime, "yyyy-MM-dd'T'HH:mm:ss"));

                    stationDataList.add(stationData);
                }
            }

            if (!stationDataList.isEmpty()) {
                this.batchSysStationData(stationDataList); // 假设支持批量插入并忽略冲突
            }

        } catch (Exception e) {
            log.error("获取国控站数据出错", e);
        }
    }

    @Override
    public int batchSysStationData(List<SysStationData> sysStationDataList){
        return this.sysStationDataMapper.batchSysStationData(sysStationDataList);
    }
}
