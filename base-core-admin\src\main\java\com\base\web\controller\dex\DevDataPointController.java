package com.base.web.controller.dex;

import com.base.common.core.domain.AjaxResultSnake;
import com.base.dex.service.impl.DevDataPointServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/dev_data_point")
@Validated
public class DevDataPointController {

    @Autowired
    private DevDataPointServiceImpl devDataPointService;

    @RequestMapping("/detail/v3")
    public AjaxResultSnake detailV3(
            @RequestParam(name = "point_id", required = false) @NotBlank(message = "点位ID不能为空") String pointId,
            @RequestParam(name = "time_type", required = false) @NotBlank(message = "时间间隔不能为空") String timeType,
            @RequestParam(name = "start_time", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @NotNull(message = "开始时间不能为空") Date startTime,
            @RequestParam(name = "end_time", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @NotNull(message = "结束时间不能为空") Date endTime) {

        // 参数校验通过后执行查询
        Map<String, Object> iotDataPointList = devDataPointService.getIotDataPointList(pointId, timeType, startTime, endTime);
        return AjaxResultSnake.success(iotDataPointList);
    }
}
