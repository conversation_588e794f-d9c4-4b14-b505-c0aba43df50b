package com.base.common.utils.sign;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.config.BaseConfig;
import com.base.common.exception.ServiceException;
import com.base.common.utils.file.FileUtils;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.nio.file.Files;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * RSA加密解密
 *
 * <AUTHOR>
 **/
public class RsaUtils {
    // Rsa 私钥
    public static String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDFjTI7J/vNObJd\n" +
            "U+zCqqvOaa+vEAOTnBtu4tk/B40CJyRRVzirg9MnRFZYwMslq7bFSAhhZXsVm8d7\n" +
            "sjeXs7zhRS1VlEXT9HBy68XARBZs8oYbIpmVZqPbJAXvEUZWpoA0PNbrEgC2ruFd\n" +
            "zqArl2ik4xq61kM00ni+7q/9ErIiSttCoXFtCnJDik+1lhzbScdemk0VjSUOFpdt\n" +
            "U/mhFRqdDBl2lwZFptmCDsKeO6/hm/ppFIjxtECpz9h8Lg9v71oIU3KsaEGyKy7z\n" +
            "RlCxSTVAFkOlIcWniqjNE4UADo6Em//JUXz2SiUwBxWvUCkDIWAE4RKSkvp8dmKm\n" +
            "X1RnmeIxAgMBAAECggEAVzqzdV+AVewmEAOgxYwq9KfCBWDi02jYzCuqlaQ15T93\n" +
            "T9tdkI5SzEHmhqYKjYW23IGkAa2XncD9YmcdGQUqX6X2EJW6B48ln/jvhqgJ5u1n\n" +
            "vc+XVVrO+1J6tEdCcbxvW6GJnJ6EZiXuKUe1fcoLUNNe2CUH4+kTm+s1FmAM/Aqe\n" +
            "x363qGV52nWmpK0SxKbb8QIWy00wJoWgKvFsTBUxSaJB2tELX5+QprdRJOYekXuQ\n" +
            "og3N84nkBTW+vwHkzj4pqzZkMiPIXyhJwQpa6UsJOdlHZzIlRuc4riSf9Qmi/6KR\n" +
            "f4fePyVgd0sMU8G6A4h+FqQmfrICXaoEgN3u75wnYQKBgQDtSmWsaEmGZn4mU741\n" +
            "d8KNg2yjA+dK5HniSlz6CZRu23p7hheYjT0swzI235JH1zUuxqQ3yiXgkbOFSbw7\n" +
            "QflOToGxDr4mmeJdlEGUbD55RwaFoHIQV3op3hAUNGvRdBrHIOiOBVtMdB48Fo0B\n" +
            "zqT3OccKtoqjU5LJxtBfi/4wHQKBgQDVIK8irOpfsznDspjDMPuNXkiBoM1Ugx98\n" +
            "9M6zUlhfMPNE5hYZuc1fVhDvozVu+tSdZ/WiOsozBTdy4OjedTzWFhHzpaBjSqsT\n" +
            "oyvEQJn44c71BYokTzFtGs9MZ2iQSjkhSNaCETDPPqE24oGaROaNkSw1hIS/jSJa\n" +
            "TZHTEXlGJQKBgQCq3XJ/Bg1DiRP3Vz/U0mq2addVPt9Sj1iwuKlgpntKpCH7RmBB\n" +
            "Shqt29SRrMVxk6bJCuJ/BcHmhiQFq0WuSeBSIbfDngEQXKiih7n9PUBzJdCtcg4Q\n" +
            "dF0UPsHHOsQUa41IrAqpEXRfhhGrZPWeQoWJPAJ2VWFZIDnHr7ClzHF5hQKBgF3H\n" +
            "TOjAFzzW2vL+UfOJSRayRssrG041iBRuj8kd0Z7JJjHJ4C2fsPtjHn6jXO6hVg6Q\n" +
            "4OdqYPBEOlrFFhJUmdlFys5fsYwfHdm9MZPgLR/zFCKCNn+1jPUKF+7xjXP4BN1d\n" +
            "dG207Lo0P7GrxwU/jgb6uu4tik/S5SvA+ApcpPU1AoGBANrJF0l7vjQ+4llylohL\n" +
            "+d7LoA3bOLXS/iCZyLhPXr6bvvS8Toymbv/95RU8lDdTr7TVRei9MvAcLEhzN2Qs\n" +
            "qgmkQwXgouwNuwCLi2JKnwoIxOI+6FgZsDkD3n+HqTWimfHgGs0SzI15DWM3pt+D\n" +
            "i+T2TWP4Km+Se0y6q4x5L39O";

    /**
     * 私钥解密
     *
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String text) throws Exception {
        return decryptByPrivateKey(privateKey, text);
    }

    /**
     * 公钥解密
     *
     * @param publicKeyString 公钥
     * @param text            待解密的信息
     * @return 解密后的文本
     */
    public static String decryptByPublicKey(String publicKeyString, String text) throws Exception {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 私钥加密
     *
     * @param privateKeyString 私钥
     * @param text             待加密的信息
     * @return 加密后的文本
     */
    public static String encryptByPrivateKey(String privateKeyString, String text) throws Exception {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 私钥解密
     *
     * @param privateKeyString 私钥
     * @param text             待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String privateKeyString, String text) throws Exception {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 公钥加密
     *
     * @param publicKeyString 公钥
     * @param text            待加密的文本
     * @return 加密后的文本
     */
    public static String encryptByPublicKey(String publicKeyString, String text) throws Exception {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 构建RSA密钥对
     *
     * @return 生成后的公私钥信息
     */
    public static RsaKeyPair generateKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
        String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
        String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
        return new RsaKeyPair(publicKeyString, privateKeyString);
    }

    public static PublicKey loadPublicKey(String filename) throws Exception {
        String key = new String(Files.readAllBytes(new File(filename).toPath()));
        key = key.replaceAll("-----\\w+ PUBLIC KEY-----", "").replaceAll("\\s", "");
        byte[] decoded = Base64.decodeBase64(key);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(decoded);
        return KeyFactory.getInstance("RSA").generatePublic(spec);
    }

    public static PrivateKey loadPrivateKey(String filename) throws Exception {
        String key = new String(Files.readAllBytes(new File(filename).toPath()));
        key = key.replaceAll("-----\\w+ PRIVATE KEY-----", "").replaceAll("\\s", "");
        byte[] decoded = Base64.decodeBase64(key);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(decoded);
        return KeyFactory.getInstance("RSA").generatePrivate(spec);
    }

    public static PublicKey loadClientPublic() throws Exception {
        return loadPublicKey(BaseConfig.getConfigPath() + File.separator + "client_public.pem");
    }

    public static PublicKey loadServerPublic() throws Exception {
        return loadPublicKey(BaseConfig.getConfigPath() + File.separator + "server_public.pem");
    }

    public static PrivateKey loadClientPrivate() throws Exception {
        return loadPrivateKey(BaseConfig.getConfigPath() + File.separator + "client_private.pem");
    }

    public static PrivateKey loadServerPrivate() throws Exception {
        return loadPrivateKey(BaseConfig.getConfigPath() + File.separator + "server_private.pem");
    }

    /**
     * RSA密钥对对象
     */
    public static class RsaKeyPair {
        private final String publicKey;
        private final String privateKey;

        public RsaKeyPair(String publicKey, String privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey() {
            return publicKey;
        }

        public String getPrivateKey() {
            return privateKey;
        }

    }

    public static void encryProductConfig() throws Exception {
        PublicKey clientPublicKey = loadPublicKey("config/client_public.pem");
        PrivateKey serverPrivateKey = loadPrivateKey("config/server_private.pem");

        // 加密 product.json
        String productConfigData = FileUtils.readFile("config/product.json");
        encryptAndSaveConfig(clientPublicKey, serverPrivateKey, productConfigData, "config/config.secure.json");

        // 加密 product_status.json
        String productStatusConfigData = FileUtils.readFile("config/product_status.json");
        encryptAndSaveConfig(clientPublicKey, serverPrivateKey, productStatusConfigData, "config/config.status.secure.json");

        System.out.println("配置文件加密完成。");
    }

    private static void encryptAndSaveConfig(PublicKey clientPublicKey, PrivateKey serverPrivateKey, String configData, String outputFilePath) throws Exception {
        // 1. 生成 AES 密钥
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(128);
        SecretKey aesKey = keyGen.generateKey();

        // 2. 加密配置内容
        Cipher aesCipher = Cipher.getInstance("AES");
        aesCipher.init(Cipher.ENCRYPT_MODE, aesKey);
        byte[] encryptedConfig = aesCipher.doFinal(configData.getBytes());

        // 3. 签名配置内容（防篡改）
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(serverPrivateKey);
        signature.update(configData.getBytes());
        byte[] signBytes = signature.sign();

        // 4. 加密 AES 密钥
        Cipher rsaCipher = Cipher.getInstance("RSA");
        rsaCipher.init(Cipher.ENCRYPT_MODE, clientPublicKey);
        byte[] encryptedAesKey = rsaCipher.doFinal(aesKey.getEncoded());

        // 5. 保存 JSON
        Map<String, String> bundle = new HashMap<>();
        bundle.put("ciphertext", Base64.encodeBase64String(encryptedConfig));
        bundle.put("aes_key", Base64.encodeBase64String(encryptedAesKey));
        bundle.put("signature", Base64.encodeBase64String(signBytes));

        String json = new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(bundle);
        Files.write(new File(outputFilePath).toPath(), json.getBytes());
    }

    /**
     * 将 product_status.json 的状态信息合并到 product.json 数据中
     *
     * @param productJson 解密后的 product.json 数据
     * @return 合并后的 JSON 字符串
     */
    private static String mergeProductStatus(String productJson) throws Exception {
        // 读取 config.status.secure.json 文件内容
        String secureJson = new String(Files.readAllBytes(new File("config/config.status.secure.json").toPath()));
        JSONObject secureBundle = JSONObject.parse(secureJson);

        // 加载私钥解密 AES 密钥
        PrivateKey clientPrivateKey = loadPrivateKey("config/client_private.pem");
        byte[] encryptedAesKey = Base64.decodeBase64(secureBundle.getString("aes_key"));
        Cipher rsaCipher = Cipher.getInstance("RSA");
        rsaCipher.init(Cipher.DECRYPT_MODE, clientPrivateKey);
        byte[] aesKeyBytes = rsaCipher.doFinal(encryptedAesKey);
        SecretKey aesKey = new SecretKeySpec(aesKeyBytes, "AES");

        // AES 解密配置内容
        Cipher aesCipher = Cipher.getInstance("AES");
        aesCipher.init(Cipher.DECRYPT_MODE, aesKey);
        byte[] statusDataBytes = aesCipher.doFinal(Base64.decodeBase64(secureBundle.getString("ciphertext")));
        String statusJson = new String(statusDataBytes);

        // 验签
        PublicKey serverPublicKey = loadPublicKey("config/server_public.pem");
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initVerify(serverPublicKey);
        signature.update(statusJson.getBytes());
        boolean isValid = signature.verify(Base64.decodeBase64(secureBundle.getString("signature")));

        if (!isValid) {
            throw new ServiceException("配置文件验证失败, 可能被篡改, 请联系管理员");
        }

        // 转换为 JSON 对象
        JSONObject statusData = JSONObject.parse(statusJson);
        JSONObject productData = JSONObject.parse(productJson);

        // 更新 expireTime
        productData.put("expireTime", statusData.getLong("expireTime"));

        // 构建 productStatus 映射表
        Map<Integer, JSONObject> statusMap = new HashMap<>();
        for (Object item : statusData.getJSONArray("productStatusList")) {
            JSONObject statusItem = (JSONObject) item;
            statusMap.put(statusItem.getIntValue("product_id"), statusItem);
        }

        // 遍历 productList 并更新状态
        JSONArray productList = productData.getJSONArray("productList");
        for (Object product : productList) {
            JSONObject productItem = (JSONObject) product;
            int productId = productItem.getIntValue("product_id");

            if (statusMap.containsKey(productId)) {
                JSONObject statusItem = statusMap.get(productId);
                productItem.put("status", statusItem.getString("status"));
                productItem.put("is_enabled", statusItem.getString("is_enabled"));
            }

            // 遍历 children 并更新状态
            if (productItem.containsKey("children")) {
                JSONArray children = productItem.getJSONArray("children");
                for (Object child : children) {
                    JSONObject childItem = (JSONObject) child;
                    int childProductId = childItem.getIntValue("product_id");

                    if (statusMap.containsKey(childProductId)) {
                        JSONObject statusItem = statusMap.get(childProductId);
                        childItem.put("status", statusItem.getString("status"));
                        childItem.put("is_enabled", statusItem.getString("is_enabled"));
                    }
                }
            }
        }

        return productData.toJSONString();
    }

    public static String getConfigData() throws Exception {
        double t2 = (double) new Date().getTime() / 1000;

        PrivateKey clientPrivateKey = loadPrivateKey("config/client_private.pem");
        PublicKey serverPublicKey = loadPublicKey("config/server_public.pem");

        // 读取文件内容
        String json = new String(Files.readAllBytes(new File("config/config.secure.json").toPath()));
        JSONObject bundle = JSONObject.parse(json);

        // 加载私钥解密 AES 密钥
        byte[] encryptedAesKey = Base64.decodeBase64(bundle.getString("aes_key"));
        Cipher rsaCipher = Cipher.getInstance("RSA");
        rsaCipher.init(Cipher.DECRYPT_MODE, clientPrivateKey);
        byte[] aesKeyBytes = rsaCipher.doFinal(encryptedAesKey);
        SecretKey aesKey = new SecretKeySpec(aesKeyBytes, "AES");

        // AES 解密配置
        Cipher aesCipher = Cipher.getInstance("AES");
        aesCipher.init(Cipher.DECRYPT_MODE, aesKey);
        byte[] configDataBytes = aesCipher.doFinal(Base64.decodeBase64(bundle.getString("ciphertext")));
        String configData = new String(configDataBytes);

        // 验签
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initVerify(serverPublicKey);
        signature.update(configData.getBytes());
        boolean isValid = signature.verify(Base64.decodeBase64(bundle.getString("signature")));

        if (isValid) {
            // 合并 product_status.json 的状态信息
            return mergeProductStatus(configData);
        } else {
            throw new ServiceException("配置文件验证失败, 可能被篡改, 请联系管理员");
        }
    }

    public static void main(String[] args) throws Exception {
        RsaUtils.encryProductConfig();
        System.out.println("RsaUtils.getConfigData() = " + RsaUtils.getConfigData());
    }

}
