package com.base.web.controller.system;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.enums.BusinessType;
import com.base.common.utils.poi.ExcelUtil;
import com.base.system.domain.SysStation;
import com.base.system.domain.dto.SysStationDTO;
import com.base.system.service.ISysStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 国/省控站Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/system/station")
public class SysStationController extends BaseController
{
    @Autowired
    private ISysStationService sysStationService;

    /**
     * 查询国/省控站列表
     */
    @PreAuthorize("@ss.hasPermi('system:station:list')")
    @RequestMapping("/list")
    public AjaxResultSnake list(@RequestBody SysStationDTO sysStationDTO)
    {
        List<SysStation> list = sysStationService.selectSysStationList(sysStationDTO.getSysStation());
        return AjaxResultSnake.success(list);
    }

    /**
     * 导出国/省控站列表
     */
    @PreAuthorize("@ss.hasPermi('system:station:export')")
    @Log(title = "国/省控站", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysStation sysStation)
    {
        List<SysStation> list = sysStationService.selectSysStationList(sysStation);
        ExcelUtil<SysStation> util = new ExcelUtil<SysStation>(SysStation.class);
        util.exportExcel(response, list, "国/省控站数据");
    }

    /**
     * 获取国/省控站详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:station:query')")
    @GetMapping(value = "/get")
    public AjaxResult getInfo(@RequestParam(name = "station_id") Long stationId)
    {
        return success(sysStationService.selectSysStationByStationId(stationId));
    }

    /**
     * 新增国/省控站
     */
    @PreAuthorize("@ss.hasPermi('system:station:add')")
    @Log(title = "国/省控站", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody SysStation sysStation)
    {
        return toAjax(sysStationService.insertSysStation(sysStation));
    }

    /**
     * 修改国/省控站
     */
    @PreAuthorize("@ss.hasPermi('system:station:edit')")
    @Log(title = "国/省控站", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody @Validated SysStationDTO sysStationDTO)
    {
        return toAjax(sysStationService.updateSysStation(sysStationDTO.getSysStation()));
    }

    /**
     * 删除国/省控站
     */
    @PreAuthorize("@ss.hasPermi('system:station:remove')")
    @Log(title = "国/省控站", businessType = BusinessType.DELETE)
	@GetMapping("delete/{stationIds}")
    public AjaxResult remove(@PathVariable Long[] stationIds)
    {
        return toAjax(sysStationService.deleteSysStationByStationIds(stationIds));
    }
}
