package com.base.system.domain.vo;

import lombok.Data;

@Data
public class TodoItemsQueryVO {
    /**
     * 待办事项状态（0：未完成，1：已完成）
     */
    private String status;
    
    /**
     * 模糊查询关键词，匹配title或content字段
     */
    private String keyword;
    
    /**
     * 接收人ID，关联sys_user表
     */
    private Long receiverId;
    
    /**
     * 排序字段，支持create_time(创建时间)、update_time(更新时间)等
     */
    private String orderBy;
}
