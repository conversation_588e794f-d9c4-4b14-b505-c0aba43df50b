package com.base.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.utils.StringUtils;
import com.base.dex.domain.DevDataPointHour;
import com.base.dex.service.IDevDataPointHourService;
import com.base.system.domain.SysConfig;
import com.base.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备点位小时数据 Controller
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping(value = "/api/device/dataPointHour")
@Api(tags = "设备点位小时数据")
public class DevDataPointHourController extends BaseController {

    @Autowired
    private IDevDataPointHourService devDataPointHourService;

    @Autowired
    private ISysConfigService configService;

    @GetMapping("/getHourAvg")
    @ApiOperation("企业数据-环境质量监测微站-小时平均值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", required = true, value = "监测指标：pm2_5,pm10", dataType = "String", paramType = "query", defaultValue = "pm_25"),
    })
    public AjaxResult getHourAvg(String type) {
        if (StringUtils.isEmpty(type)) {
            return AjaxResult.error("监测指标：pm2_5或pm10必填");
        }
        try {
            // 需求：企业数据：默认使用小型国标空气质量监测站（修改为小型空气质量监测站）数据进行对比，若无法采集，则采用与国标站相同时间的厂界监测微站的小时均值的平均值
            JSONObject result = new JSONObject();
            // 监测指标：PM2.5、PM10 查询整个厂区PM2.5、PM10的小时均值
            DevDataPointHour devDataPointHour = new DevDataPointHour();
            devDataPointHour.setFactor(type);
            // 小型空气质量监测站 14 ，厂界监测微站 21
            devDataPointHour.setSubType("14");
            DevDataPointHour avgItem = devDataPointHourService.getDevDataPointHourByAvg(devDataPointHour);
            if (avgItem == null) {
                devDataPointHour.setSubType("21");
                avgItem = devDataPointHourService.getDevDataPointHourByAvg(devDataPointHour);
            }
            if (avgItem != null) {
                result.put("monitorTime", avgItem.getMonitorTime());
                result.put("value", avgItem.getValue());
            }
            SysConfig configByKey = configService.getConfigByKey("sys.corp.location");
            String name = null;
            if (configByKey != null) {
                name = configByKey.getRemark();
            }
            result.put("name", name);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("=====> 查询企业数据-环境质量监测微站-小时平均值", e);
            return AjaxResult.error("查询企业数据-环境质量监测微站-小时平均值出错");
        }
    }

}
