package com.base.dex.service;

import com.base.dex.domain.DevDataPointMin;

import java.util.List;

/**
 * 点位分钟数据Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IDevDataPointMinService {
    /**
     * 查询点位分钟数据
     *
     * @param pointId 点位分钟数据主键
     * @return 点位分钟数据
     */
    public DevDataPointMin selectDevDataPointMinByPointId(String pointId);

    /**
     * 查询点位分钟数据列表
     *
     * @param devDataPointMin 点位分钟数据
     * @return 点位分钟数据集合
     */
    public List<DevDataPointMin> selectDevDataPointMinList(DevDataPointMin devDataPointMin);

    /**
     * 新增点位分钟数据
     *
     * @param devDataPointMin 点位分钟数据
     * @return 结果
     */
    public int insertDevDataPointMin(DevDataPointMin devDataPointMin);

    /**
     * 修改点位分钟数据
     *
     * @param devDataPointMin 点位分钟数据
     * @return 结果
     */
    public int updateDevDataPointMin(DevDataPointMin devDataPointMin);

    /**
     * 批量删除点位分钟数据
     *
     * @param pointIds 需要删除的点位分钟数据主键集合
     * @return 结果
     */
    public int deleteDevDataPointMinByPointIds(String[] pointIds);

    /**
     * 删除点位分钟数据信息
     *
     * @param pointId 点位分钟数据主键
     * @return 结果
     */
    public int deleteDevDataPointMinByPointId(String pointId);

    /**
     * 获取点位分钟数据列表
     *
     * @param devDataPointMin 点位分钟数据
     * @return 点位分钟数据集合
     */
    public List<DevDataPointMin> getDevDataPointMinList(DevDataPointMin devDataPointMin);
}
