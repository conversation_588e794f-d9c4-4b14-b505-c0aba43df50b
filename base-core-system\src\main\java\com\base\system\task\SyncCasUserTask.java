package com.base.system.task;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.config.CasConfig;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.utils.DesUtils;
import com.base.system.domain.vo.CasUserVo;
import com.base.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("userTask")
public class SyncCasUserTask {
    private static final Logger log = LoggerFactory.getLogger(SyncCasUserTask.class);

    @Autowired
    ISysUserService sysUserService;

    public void syncCasUserTask(){
        List<CasUserVo> casUserVos = readUserList();
        List<SysUser> userList = new ArrayList<>();
        casUserVos.forEach(casUser -> {
            if (casUser.getUserId().equals("1")) {
                //禁止修改管理员
                return;
            }

            SysUser sysUser = new SysUser();
            sysUser.setUserId(Long.valueOf(casUser.getUserId()));
            sysUser.setDeptId(Long.valueOf(casUser.getDeptId()));
            sysUser.setUserName(casUser.getUserName());
            sysUser.setNickName(casUser.getNickName());
            sysUser.setEmail(casUser.getEmail());
            sysUser.setSex(casUser.getSex());
            sysUser.setAvatar(casUser.getAvatar());
            sysUser.setStatus(casUser.getStatus());
            sysUser.setDelFlag(casUser.getDelFlag());
            sysUser.setRemark(casUser.getRemark());
            userList.add(sysUser);
        });
        sysUserService.importUser(userList, true, "casTask");
    }

    public List<CasUserVo> readUserList(){
        /*用户信息同步接口地址*/
        String url = "http://*************:8080/openapi/sync/userDetailList";
        /*初始化请求参数对象*/
        Map<String, Object> params = new HashMap<>();
        /*时间戳*/
        Long timeStamp = System.currentTimeMillis();
        /*appCode*/
        String appCode = "env";
        /*业务参数 - 根据接口要求拼装参数*/
        Map<String, Object> busiParam = new HashMap<>();
        String busiParamString = JSON.toJSONString(busiParam);
        /*签名值  sign=DesUtil.encryptDES(appCode+busiParamString+timeStamp,"密钥")*/
        String sign = DesUtils.encryptDES(appCode + busiParamString + timeStamp, CasConfig.getEncryptKey());
        params.put("timeStamp", timeStamp);
        params.put("appCode", appCode);
        params.put("busiParam", busiParamString);
        params.put("sign", sign);
        params.put("pageNum", 1);
        params.put("pageSize", 100000);
        /*调用用户信息同步接口*/
        String result = HttpUtil.post(url, JSON.toJSONString(params));
        /*输出结果信息*/
        /** {
         "code": 200,
         "msg": "操作成功",
         "data": {
         "records": [{}],//返回数据结构
         "total": 31,
         "size": 10,
         "current": 1,
         "orders": [],
         "optimizeCountSql": true,
         "searchCount": true,
         "countId": null,
         "maxLimit": null,
         "pages": 4
         },
         "ok": true
         } **/
        log.info(result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (!jsonObject.get("code").toString().equals("200")){
            log.error("同步用户信息失败, {}", result);
            throw new RuntimeException("同步用户信息失败");
        }
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray records = data.getJSONArray("records");
        return JSONArray.parseArray(records.toJSONString(), CasUserVo.class);
    }
}
