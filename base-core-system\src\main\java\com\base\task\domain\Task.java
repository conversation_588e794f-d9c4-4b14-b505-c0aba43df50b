package com.base.task.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务对象 task
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@ApiModel("任务对象")
public class Task extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 任务类型id
     */
    @Excel(name = "任务类型id")
    @ApiModelProperty("任务类型id")
    private Long taskTypeId;

    /**
     * 任务名称
     */
    @Excel(name = "任务名称")
    @ApiModelProperty("任务名称")
    private String name;

    /**
     * 任务编号
     */
    @Excel(name = "任务编号")
    @ApiModelProperty("任务编号")
    private String taskNumber;

    /**
     * 任务描述
     */
    @Excel(name = "任务描述")
    @ApiModelProperty("任务描述")
    private String description;

    /**
     * 任务地点
     */
    @Excel(name = "任务地点")
    @ApiModelProperty("任务地点")
    private String location;

    /**
     * 任务截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("任务截止时间")
    private Date deadline;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务实际完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("yyyy-MM-dd HH:mm:ss")
    private Date completionTime;

    /**
     * 执行人
     */
    @Excel(name = "执行人")
    @ApiModelProperty("执行人")
    private String executor;

    /**
     * 紧急状态：0不紧急，1一般，2紧急，默认2紧急
     */
    @Excel(name = "紧急状态：0不紧急，1一般，2紧急，默认2紧急")
    @ApiModelProperty("紧急状态：0不紧急，1一般，2紧急，默认2紧急")
    private Integer urgencyLevel;

    /**
     * 任务完成状态：0进行中 1已完成
     */
    @Excel(name = "任务完成状态：0进行中 1已完成")
    @ApiModelProperty("任务完成状态：0进行中 1已完成")
    private Integer status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     * 执行人名称
     */
    @ApiModelProperty("执行人名称")
    private String userName;

    /**
     * 发起人名称
     */
    @ApiModelProperty("发起人名称")
    private String createName;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String phoneNumber;

    /**
     * 执行人所属部门名称
     */
    @ApiModelProperty("执行人所属部门名称")
    private String deptName;

    /**
     * 动态字段Key
     */
    @ApiModelProperty("动态字段Key")
    private String fieldKey;

    /**
     * 动态数据
     */
    @ApiModelProperty("动态数据")
    private String fieldJson;


    // 自定义字段
    /**
     * 任务类型名称
     */
    @ApiModelProperty("任务类型名称")
    private String taskTypeName;

    /**
     * 已逾期
     */
    @ApiModelProperty("已逾期")
    private String expireText;

    /**
     * 数据来源类型：0手动添加，1系统定时生成
     */
    private Integer sourceType;

    /**
     * 任务文件列表
     */
    private List<TaskFile> taskFileList;

    /**
     * 任务进度列表
     */
    private List<TaskSchedule> taskScheduleList;

    /**
     * 类型
     */
    private Integer type;
    /**
     * 开始时间
     */
    private String startDatetime;

    /**
     * 结束时间
     */
    private String endDatetime;

    /**
     * 年份
     */
    private String year;

    /**
     * 任务id__status__executor字符串拼接
     */
    private String taskInfoExecutors;

    /**
     * 任务文件id字符串拼接
     */
    private String taskFileStrIds;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTaskTypeId(Long taskTypeId) {
        this.taskTypeId = taskTypeId;
    }

    public Long getTaskTypeId() {
        return taskTypeId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setTaskNumber(String taskNumber) {
        this.taskNumber = taskNumber;
    }

    public String getTaskNumber() {
        return taskNumber;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLocation() {
        return location;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public Date getDeadline() {
        return deadline;
    }

    public Date getCompletionTime() {
        return completionTime;
    }

    public void setCompletionTime(Date completionTime) {
        this.completionTime = completionTime;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    public String getExecutor() {
        return executor;
    }

    public void setUrgencyLevel(Integer urgencyLevel) {
        this.urgencyLevel = urgencyLevel;
    }

    public Integer getUrgencyLevel() {
        return urgencyLevel;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public List<TaskFile> getTaskFileList() {
        return taskFileList;
    }

    public void setTaskFileList(List<TaskFile> taskFileList) {
        this.taskFileList = taskFileList;
    }

    public String getTaskTypeName() {
        return taskTypeName;
    }

    public void setTaskTypeName(String taskTypeName) {
        this.taskTypeName = taskTypeName;
    }

    public String getExpireText() {
        return expireText;
    }

    public void setExpireText(String expireText) {
        this.expireText = expireText;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public List<TaskSchedule> getTaskScheduleList() {
        return taskScheduleList;
    }

    public void setTaskScheduleList(List<TaskSchedule> taskScheduleList) {
        this.taskScheduleList = taskScheduleList;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getStartDatetime() {
        return startDatetime;
    }

    public void setStartDatetime(String startDatetime) {
        this.startDatetime = startDatetime;
    }

    public String getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(String endDatetime) {
        this.endDatetime = endDatetime;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getTaskInfoExecutors() {
        return taskInfoExecutors;
    }

    public void setTaskInfoExecutors(String taskInfoExecutors) {
        this.taskInfoExecutors = taskInfoExecutors;
    }

    public String getTaskFileStrIds() {
        return taskFileStrIds;
    }

    public void setTaskFileStrIds(String taskFileStrIds) {
        this.taskFileStrIds = taskFileStrIds;
    }

    public String getFieldKey() {
        return fieldKey;
    }

    public void setFieldKey(String fieldKey) {
        this.fieldKey = fieldKey;
    }

    public String getFieldJson() {
        return fieldJson;
    }

    public void setFieldJson(String fieldJson) {
        this.fieldJson = fieldJson;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("taskTypeId", getTaskTypeId())
                .append("name", getName())
                .append("taskNumber", getTaskNumber())
                .append("description", getDescription())
                .append("location", getLocation())
                .append("deadline", getDeadline())
                .append("completionTime", getCompletionTime())
                .append("executor", getExecutor())
                .append("urgencyLevel", getUrgencyLevel())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
