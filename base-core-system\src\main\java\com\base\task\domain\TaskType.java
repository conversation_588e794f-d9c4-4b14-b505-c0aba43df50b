package com.base.task.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 任务类型对象 task_type
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@ApiModel("任务类型对象")
public class TaskType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 任务类型名称
     */
    @Excel(name = "任务类型名称")
    @ApiModelProperty("任务类型名称")
    private String name;

    /**
     * 是否定期生成：0否，1是
     */
    @Excel(name = "是否定期生成：0否，1是")
    @ApiModelProperty("是否定期生成：0否，1是")
    private Integer periodicStatus;

    /**
     * 执行人，多个使用英文逗号隔开
     */
    @Excel(name = "执行人，多个使用英文逗号隔开")
    @ApiModelProperty("执行人，多个使用英文逗号隔开")
    private String executor;

    /**
     * 紧急程度：0不紧急，1一般，2紧急，默认2紧急
     */
    @Excel(name = "紧急程度：0不紧急，1一般，2紧急，默认2紧急")
    @ApiModelProperty("紧急程度：0不紧急，1一般，2紧急，默认2紧急")
    private Integer urgencyLevel;

    /**
     * 任务周期
     */
    @Excel(name = "任务周期")
    @ApiModelProperty("任务周期")
    private Integer taskCycle;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("开始时间")
    private Date startTime;

    /**
     * 任务提前生成时间
     */
    @Excel(name = "任务提前生成时间")
    @ApiModelProperty("任务提前生成时间")
    private Integer advanceTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @ApiModelProperty(hidden = true)
    private Integer delFlag;

    /**
     * 当前是否已经生成：0未生成，1已生成
     */
    private Integer currentStatus;

    /**
     * 下一次执行时间
     */
    private Date nextStartTime;

    /**
     * 是否为菜单：0否（可删除），1是（不可删除）
     */
    @ApiModelProperty("是否为菜单：0否（可删除），1是（不可删除）")
    private Integer menuStatus;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String userName;

    /**
     * 是否需要复核：0否，1是，默认为0
     */
    @Excel(name = "是否需要复核：0否，1是，默认为0")
    @ApiModelProperty("是否需要复核：0否，1是，默认为0")
    private Integer reviewStatus;

    /**
     * 任务发布信息
     */
    @Excel(name = "任务发布信息")
    @ApiModelProperty("任务发布信息")
    private String releaseInfo;

    /**
     * 任务进度反馈信息
     */
    @Excel(name = "任务进度反馈信息")
    @ApiModelProperty("任务进度反馈信息")
    private String progressInfo;

    /**
     * 任务完成反馈信息
     */
    @Excel(name = "任务完成反馈信息")
    @ApiModelProperty("任务完成反馈信息")
    private String finishInfo;

    /**
     * 执行人名称
     */
    private String executorName;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setPeriodicStatus(Integer periodicStatus) {
        this.periodicStatus = periodicStatus;
    }

    public Integer getPeriodicStatus() {
        return periodicStatus;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    public String getExecutor() {
        return executor;
    }

    public void setUrgencyLevel(Integer urgencyLevel) {
        this.urgencyLevel = urgencyLevel;
    }

    public Integer getUrgencyLevel() {
        return urgencyLevel;
    }

    public void setTaskCycle(Integer taskCycle) {
        this.taskCycle = taskCycle;
    }

    public Integer getTaskCycle() {
        return taskCycle;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setAdvanceTime(Integer advanceTime) {
        this.advanceTime = advanceTime;
    }

    public Integer getAdvanceTime() {
        return advanceTime;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public Integer getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(Integer currentStatus) {
        this.currentStatus = currentStatus;
    }

    public Date getNextStartTime() {
        return nextStartTime;
    }

    public void setNextStartTime(Date nextStartTime) {
        this.nextStartTime = nextStartTime;
    }

    public Integer getMenuStatus() {
        return menuStatus;
    }

    public void setMenuStatus(Integer menuStatus) {
        this.menuStatus = menuStatus;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getReleaseInfo() {
        return releaseInfo;
    }

    public void setReleaseInfo(String releaseInfo) {
        this.releaseInfo = releaseInfo;
    }

    public String getProgressInfo() {
        return progressInfo;
    }

    public void setProgressInfo(String progressInfo) {
        this.progressInfo = progressInfo;
    }

    public String getFinishInfo() {
        return finishInfo;
    }

    public void setFinishInfo(String finishInfo) {
        this.finishInfo = finishInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("periodicStatus", getPeriodicStatus())
                .append("executor", getExecutor())
                .append("urgencyLevel", getUrgencyLevel())
                .append("taskCycle", getTaskCycle())
                .append("startTime", getStartTime())
                .append("advanceTime", getAdvanceTime())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
