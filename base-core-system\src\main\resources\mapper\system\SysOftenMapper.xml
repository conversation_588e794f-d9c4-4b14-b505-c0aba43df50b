<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.SysOftenMapper">

    <resultMap type="SysOften" id="SysOftenResult">
        <result property="oftenId"    column="often_id"    />
        <result property="oftenType"    column="often_type"    />
        <result property="externalId"    column="external_id"    />
        <result property="oftenPath"    column="often_path"    />
        <result property="oftenName"    column="often_name"    />
        <result property="oftenIcon"    column="often_icon"    />
        <result property="oftenImage"    column="often_image"    />
        <result property="oftenTarget"    column="often_target"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="sort"    column="sort"    />
    </resultMap>

    <sql id="selectSysOftenVo">
        select often_id, often_type, external_id, often_path, often_name, often_icon, often_image, often_target, create_by, create_time, sort from sys_often
    </sql>

    <select id="selectSysOftenList" parameterType="SysOften" resultMap="SysOftenResult">
        <include refid="selectSysOftenVo"/>
        <where>
            <if test="oftenType != null  and oftenType != ''"> and often_type = #{oftenType}</if>
            <if test="externalId != null "> and external_id = #{externalId}</if>
            <if test="oftenPath != null  and oftenPath != ''"> and often_path = #{oftenPath}</if>
            <if test="oftenName != null  and oftenName != ''"> and often_name like concat('%', #{oftenName}, '%')</if>
            <if test="oftenIcon != null  and oftenIcon != ''"> and often_icon = #{oftenIcon}</if>
            <if test="oftenImage != null  and oftenImage != ''"> and often_image = #{oftenImage}</if>
            <if test="oftenTarget != null  and oftenTarget != ''"> and often_target = #{oftenTarget}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
        </where>
        ORDER BY sort ASC
    </select>

    <select id="selectSysOftenByOftenId" parameterType="Long" resultMap="SysOftenResult">
    </select>

    <insert id="insertSysOften" parameterType="SysOften" keyProperty="oftenId">
        insert into sys_often
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oftenType != null">often_type,</if>
            <if test="externalId != null">external_id,</if>
            <if test="oftenPath != null">often_path,</if>
            <if test="oftenName != null">often_name,</if>
            <if test="oftenIcon != null">often_icon,</if>
            <if test="oftenImage != null">often_image,</if>
            <if test="oftenTarget != null">often_target,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="sort != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oftenType != null">#{oftenType},</if>
            <if test="externalId != null">#{externalId},</if>
            <if test="oftenPath != null">#{oftenPath},</if>
            <if test="oftenName != null">#{oftenName},</if>
            <if test="oftenIcon != null">#{oftenIcon},</if>
            <if test="oftenImage != null">#{oftenImage},</if>
            <if test="oftenTarget != null">#{oftenTarget},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="sort != null">#{sort},</if>
        </trim>
        ON CONFLICT (create_by, often_type, external_id)
        DO UPDATE SET
        <trim suffixOverrides=",">
            <if test="oftenPath != null">often_path = EXCLUDED.often_path,</if>
            <if test="oftenName != null">often_name = EXCLUDED.often_name,</if>
            <if test="oftenIcon != null">often_icon = EXCLUDED.often_icon,</if>
            <if test="oftenImage != null">often_image = EXCLUDED.often_image,</if>
            <if test="oftenTarget != null">often_target = EXCLUDED.often_target,</if>
            <if test="createTime != null">create_time = EXCLUDED.create_time,</if>
            <if test="sort != null">sort = EXCLUDED.sort,</if>
        </trim>
    </insert>


    <update id="updateSysOften" parameterType="SysOften">
        update sys_often
        <trim prefix="SET" suffixOverrides=",">
            <if test="oftenType != null">often_type = #{oftenType},</if>
            <if test="externalId != null">external_id = #{externalId},</if>
            <if test="oftenPath != null">often_path = #{oftenPath},</if>
            <if test="oftenName != null">often_name = #{oftenName},</if>
            <if test="oftenIcon != null">often_icon = #{oftenIcon},</if>
            <if test="oftenImage != null">often_image = #{oftenImage},</if>
            <if test="oftenTarget != null">often_target = #{oftenTarget},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where often_id = #{oftenId}
    </update>

    <delete id="deleteSysOftenByOftenId" parameterType="Long">
        delete from sys_often where often_id = #{oftenId}
    </delete>

    <delete id="deleteSysOftenByOftenIds" parameterType="String">
        delete from sys_often where often_id in
        <foreach item="oftenId" collection="array" open="(" separator="," close=")">
            #{oftenId}
        </foreach>
    </delete>
</mapper>
