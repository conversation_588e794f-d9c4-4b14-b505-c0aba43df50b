package com.base.common.config;

import com.base.common.utils.StringUtils;
import com.base.common.utils.sign.RsaUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Setter
@Component
public class ProductConfig {

    private volatile String produceConfig = null;

    @PostConstruct
    public void init() {
        try {
            produceConfig = RsaUtils.getConfigData();
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public String getProduceConfig() {
        // 产品时间校验 有需要再启用
//        Integer expireTime = JSONObject.parseObject(produceConfig).getInteger("expireTime");
//        if (expireTime != null) {
//            if (expireTime < new Date().getTime()) {
//                throw new RuntimeException("平台已过试用期, 请联系我司人员进行购买正式版");
//            }
//        }
        return produceConfig;
    }

    public boolean isValid() {
        // 校验签名是否合法
        return StringUtils.isEmpty(produceConfig);
    }
}
