package com.base.web.controller.system;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.poi.ExcelUtil;
import com.base.system.domain.SysMenuField;
import com.base.system.domain.dto.SysMenuFieldAddAllDTO;
import com.base.system.domain.dto.SysMenuFieldSortDTO;
import com.base.system.service.ISysMenuFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单-动态字段 关联关系Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/sys/menu_field")
public class SysMenuFieldController extends BaseController
{
    @Autowired
    private ISysMenuFieldService sysMenuFieldService;

    /**
     * 分页查询菜单-动态字段 关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:field:list')")
    @RequestMapping("/page")
    public TableDataInfo page(@RequestBody SysMenuField sysMenuField)
    {
        startPage();
        List<SysMenuField> list = sysMenuFieldService.selectSysMenuFieldList(sysMenuField);
        return getDataTable(list);
    }

    /**
     * 查询菜单-动态字段 关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:field:list')")
    @RequestMapping("/list")
    public AjaxResultSnake list(@RequestBody SysMenuField sysMenuField)
    {
        List<SysMenuField> list = sysMenuFieldService.selectSysMenuFieldList(sysMenuField);
        return AjaxResultSnake.success(list);
    }

    /**
     * 导出菜单-动态字段 关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:field:export')")
    @Log(title = "菜单-动态字段 关联关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysMenuField sysMenuField)
    {
        List<SysMenuField> list = sysMenuFieldService.selectSysMenuFieldList(sysMenuField);
        ExcelUtil<SysMenuField> util = new ExcelUtil<SysMenuField>(SysMenuField.class);
        util.exportExcel(response, list, "菜单-动态字段 关联关系数据");
    }

    /**
     * 获取菜单-动态字段 关联关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:field:query')")
    @GetMapping(value = "get")
    public AjaxResultSnake getInfo(@RequestParam("joinId") Long joinId)
    {
        return AjaxResultSnake.success(sysMenuFieldService.selectSysMenuFieldByJoinId(joinId));
    }

    /**
     * 新增菜单-动态字段 关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:field:add')")
    @Log(title = "菜单-动态字段 关联关系", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResultSnake add(@RequestBody  SysMenuField sysMenuField)
    {
        return AjaxResultSnake.success(sysMenuFieldService.insertSysMenuField(sysMenuField));
    }

    /**
     * 批量新增
     * @param sysMenuFieldAddAllDTO
     * @return
     */
    @PreAuthorize("@ss.hasPermi('system:field:add')")
    @Log(title = "菜单-动态字段 关联关系", businessType = BusinessType.INSERT)
    @PostMapping("add_all")
    public AjaxResultSnake add(@RequestBody @Validated(SysMenuField.SysMenuFieldAdd.class) SysMenuFieldAddAllDTO sysMenuFieldAddAllDTO)
    {
        return AjaxResultSnake.success(sysMenuFieldService.batchSysMenuField(sysMenuFieldAddAllDTO.getMenuFieldArray()));
    }

    /**
     * 修改菜单-动态字段 关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:field:edit')")
    @Log(title = "菜单-动态字段 关联关系", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResultSnake edit(@RequestBody SysMenuField sysMenuField)
    {
        return AjaxResultSnake.success(sysMenuFieldService.updateSysMenuField(sysMenuField));
    }

    /**
     * 删除菜单-动态字段 关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:field:remove')")
    @Log(title = "菜单-动态字段 关联关系", businessType = BusinessType.DELETE)
    @GetMapping("delete")
    public AjaxResultSnake remove(@RequestParam("joinId") Long joinId)
    {
        return AjaxResultSnake.success(sysMenuFieldService.deleteSysMenuFieldByJoinId(joinId));
    }


    /**
     * 页面动态字段排序更新
     */
    @PreAuthorize("@ss.hasPermi('system:field:edit')")
    @Log(title = "页面动态字段排序", businessType = BusinessType.UPDATE)
    @PostMapping("/sort")
    public AjaxResultSnake updateSort(@RequestBody @Validated(SysMenuField.SysMenuFieldSort.class)SysMenuFieldSortDTO sysMenuFieldSortDTO) {
        List<SysMenuField> joinList = sysMenuFieldSortDTO.getMenuFieldArray();
        if (joinList == null || joinList.isEmpty()) {
            return AjaxResultSnake.success();
        }

        // 提取并排序 sort 值
        List<Long> sortList = joinList.stream()
                .map(SysMenuField::getSort)
                .sorted()
                .collect(Collectors.toList());

        int i = 0;
        SysMenuField firstField = new SysMenuField();
        SysMenuField temp = sysMenuFieldService.selectSysMenuFieldByJoinId(joinList.get(0).getJoinId());
        if (temp != null) {
            firstField = temp;
        }

        for (SysMenuField item : joinList) {
            SysMenuField sysMenuField = new SysMenuField();
            sysMenuField.setJoinId(item.getJoinId());
            sysMenuField.setSort(sortList.get(i++));
            sysMenuFieldService.updateSysMenuField(sysMenuField);
        }

        // 清除缓存
        sysMenuFieldService.clear(firstField.getMenuId());

        return AjaxResultSnake.success();
    }
}
