package com.base.system.mapper;

import com.base.system.domain.SysProcess;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 区域信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Repository
public interface SysProcessMapper
{
    /**
     * 查询区域信息
     *
     * @param processId 区域信息主键
     * @return 区域信息
     */
    public SysProcess selectSysProcessByProcessId(Long processId);

    /**
     * 查询区域信息列表
     *
     * @param sysProcess 区域信息
     * @return 区域信息集合
     */
    public List<SysProcess> selectSysProcessList(SysProcess sysProcess);

    /**
     * 新增区域信息
     *
     * @param sysProcess 区域信息
     * @return 结果
     */
    public int insertSysProcess(SysProcess sysProcess);

    /**
     * 修改区域信息
     *
     * @param sysProcess 区域信息
     * @return 结果
     */
    public int updateSysProcess(SysProcess sysProcess);

    /**
     * 删除区域信息
     *
     * @param processId 区域信息主键
     * @return 结果
     */
    public int deleteSysProcessByProcessId(Long processId);

    /**
     * 批量删除区域信息
     *
     * @param processIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysProcessByProcessIds(Long[] processIds);

    /**
     * 批量新增区域信息
     *
     * @param sysProcessList 区域信息列表
     * @return 结果
     */
    public int batchSysProcess(List<SysProcess> sysProcessList);

    List<SysProcess> selectSysProcessByIdIn(List<Long> processList);

    void clear();

    List<SysProcess> selectChildren(Long ancestorId);
}
