package com.base.system.service;

import com.base.system.domain.SysOften;

import java.util.List;

/**
 * 用户常用功能Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface ISysOftenService
{
    /**
     * 查询用户常用功能
     *
     * @param oftenId 用户常用功能主键
     * @return 用户常用功能
     */
    public SysOften selectSysOftenByOftenId(Long oftenId);

    public List<SysOften> selectList(SysOften sysOften);

    /**
     * 查询用户常用功能列表
     *
     * @param sysOften 用户常用功能
     * @return 用户常用功能集合
     */
    public List<SysOften> selectSysOftenList(SysOften sysOften);

    /**
     * 新增用户常用功能
     *
     * @param sysOften 用户常用功能
     * @return 结果
     */
    public int insertSysOften(SysOften sysOften);

    /**
     * 修改用户常用功能
     *
     * @param sysOften 用户常用功能
     * @return 结果
     */
    public int updateSysOften(SysOften sysOften);

    /**
     * 批量删除用户常用功能
     *
     * @param oftenIds 需要删除的用户常用功能主键集合
     * @return 结果
     */
    public int deleteSysOftenByOftenIds(Long[] oftenIds);

    /**
     * 删除用户常用功能信息
     *
     * @param oftenId 用户常用功能主键
     * @return 结果
     */
    public int deleteSysOftenByOftenId(Long oftenId);
}
