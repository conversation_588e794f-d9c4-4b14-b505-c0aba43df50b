package com.base.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum RunState {
    RUN("1", "运行"),
    STOP("0", "待机"),
    OFFLINE("-1", "离线"),
    ERROR("2", "故障"),
    ABNORMAL("3", "超标"),
    NOT_CONNECT("4", "暂未联网"),
    NORMAL("5", "数据正常");

    private final String value;
    private final String description;

    RunState(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    // 通过值获取枚举实例
    public static RunState fromValue(String value) {
        for (RunState state : RunState.values()) {
            if (state.getValue().equals(value)) {
                return state;
            }
        }
        throw new IllegalArgumentException("未知的运行状态值: " + value);
    }

    // 判断字符串是否是有效的 RunState 值
    public static boolean contains(String value) {
        for (RunState state : RunState.values()) {
            if (state.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }

    // 获取所有值对应的 Map，便于校验和转换
    private static final Map<String, RunState> VALUE_MAP = new HashMap<>();

    static {
        for (RunState state : values()) {
            VALUE_MAP.put(state.getValue(), state);
        }
    }

    public static RunState getEnumByValue(String value) {
        return VALUE_MAP.get(value);
    }
}
