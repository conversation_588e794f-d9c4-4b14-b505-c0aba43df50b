package com.base.system.task;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.config.CasConfig;
import com.base.common.core.domain.entity.SysDept;
import com.base.common.utils.DesUtils;
import com.base.system.domain.vo.CasDeptVo;
import com.base.system.service.ISysDeptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("deptTask")
public class SyncCasDeptTask {
    private static final Logger log = LoggerFactory.getLogger(SyncCasUserTask.class);

    @Autowired
    private ISysDeptService deptService;

    public void syncCasUserTask(){
        List<CasDeptVo> casDeptVos = readDept();
        List<SysDept> deptList = new ArrayList<SysDept>();
        casDeptVos.forEach(casDept -> {
            SysDept sysDept = new SysDept();
            sysDept.setDeptId((long) casDept.getDeptId());
            sysDept.setParentId(casDept.getParentId());
            sysDept.setAncestors(casDept.getAncestors());
            sysDept.setDeptName(casDept.getDeptName());
            sysDept.setOrderNum(casDept.getOrderNum());
            sysDept.setLeader(casDept.getLeader());
            sysDept.setPhone(casDept.getPhone());
            sysDept.setEmail(casDept.getEmail());
            sysDept.setStatus(casDept.getStatus());
            sysDept.setDelFlag(casDept.getDelFlag());
            deptList.add(sysDept);
        });
        deptService.importDept(deptList, true, "casTask");
    }

    public List<CasDeptVo> readDept(){
        /*用户信息同步接口地址*/
        String url = "http://*************:8080/openapi/sync/deptDetailList";
        /*初始化请求参数对象*/
        Map<String, Object> params = new HashMap<>();
        /*时间戳*/
        Long timeStamp = System.currentTimeMillis();
        /*appCode*/
        String appCode = "env";
        /*业务参数 - 根据接口要求拼装参数*/
        Map<String, Object> busiParam = new HashMap<>();
        String busiParamString = JSON.toJSONString(busiParam);
        /*签名值  sign=DesUtil.encryptDES(appCode+busiParamString+timeStamp,"密钥")*/
        String sign = DesUtils.encryptDES(appCode + busiParamString + timeStamp, CasConfig.getEncryptKey());
        params.put("timeStamp", timeStamp);
        params.put("appCode", appCode);
        params.put("busiParam", busiParamString);
        params.put("sign", sign);
        params.put("pageNum", 1);
        params.put("pageSize", 10);
        /*调用组织机构信息同步接口*/
        String result = HttpUtil.post(url, JSON.toJSONString(params));
        /*输出结果信息*/
        /** {
         "code": 200,
         "msg": "操作成功",
         "data": {
         "records": [{}],//返回数据结构
         "total": 31,
         "size": 10,
         "current": 1,
         "orders": [],
         "optimizeCountSql": true,
         "searchCount": true,
         "countId": null,
         "maxLimit": null,
         "pages": 4
         },
         "ok": true
         } **/
        log.info(result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (!jsonObject.get("code").toString().equals("200")){
            log.error("同步部门信息失败, {}", result);
            throw new RuntimeException("同步部门信息失败");
        }
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray records = data.getJSONArray("records");
        return JSONArray.parseArray(records.toJSONString(), CasDeptVo.class);
    }

}
