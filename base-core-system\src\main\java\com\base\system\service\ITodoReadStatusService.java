package com.base.system.service;

import com.base.system.domain.TodoReadStatus;

import java.util.List;

/**
 * 待办事项已读状态 服务层接口
 */
public interface ITodoReadStatusService {
    /**
     * 查询待办事项已读状态
     *
     * @param readStatusId 已读状态ID
     * @return 待办事项已读状态
     */
    public TodoReadStatus selectTodoReadStatusById(Long readStatusId);

    /**
     * 查询待办事项已读状态列表
     *
     * @param todoReadStatus 待办事项已读状态
     * @return 待办事项已读状态集合
     */
    public List<TodoReadStatus> selectTodoReadStatusList(TodoReadStatus todoReadStatus);

    /**
     * 新增待办事项已读状态
     *
     * @param todoReadStatus 待办事项已读状态
     * @return 结果
     */
    public int insertTodoReadStatus(TodoReadStatus todoReadStatus);

    /**
     * 修改待办事项已读状态
     *
     * @param todoReadStatus 待办事项已读状态
     * @return 结果
     */
    public int updateTodoReadStatus(TodoReadStatus todoReadStatus);

    /**
     * 删除待办事项已读状态
     *
     * @param readStatusId 已读状态ID
     * @return 结果
     */
    public int deleteTodoReadStatusById(Long readStatusId);

    /**
     * 批量删除待办事项已读状态
     *
     * @param readStatusIds 需要删除的已读状态ID集合
     * @return 结果
     */
    public int deleteTodoReadStatusByIds(Long[] readStatusIds);
}
