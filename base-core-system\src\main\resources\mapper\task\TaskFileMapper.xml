<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.task.mapper.TaskFileMapper">

    <resultMap type="TaskFile" id="TaskFileResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="fileSuffix" column="file_suffix"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectTaskFileVo">
        select id, task_id, file_name, file_path, file_suffix, create_by, create_time, del_flag from task_file
    </sql>

    <select id="selectTaskFileList" parameterType="TaskFile" resultMap="TaskFileResult">
        <include refid="selectTaskFileVo"/>
        <where>
            <if test="taskId != null ">and task_id = #{taskId}</if>
            <if test="fileName != null  and fileName != ''">and file_name like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''">and file_path = #{filePath}</if>
            <if test="fileSuffix != null  and fileSuffix != ''">and file_suffix = #{fileSuffix}</if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectTaskFileById" parameterType="Long" resultMap="TaskFileResult">
        <include refid="selectTaskFileVo"/>
        where id = #{id}
    </select>

    <insert id="insertTaskFile" parameterType="TaskFile" useGeneratedKeys="true" keyProperty="id">
        insert into task_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileSuffix != null">file_suffix,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileSuffix != null">#{fileSuffix},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateTaskFile" parameterType="TaskFile">
        update task_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileSuffix != null">file_suffix = #{fileSuffix},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskFileById" parameterType="Long">
        delete from task_file where id = #{id}
    </delete>

    <delete id="deleteTaskFileByIds" parameterType="String">
        delete from task_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>