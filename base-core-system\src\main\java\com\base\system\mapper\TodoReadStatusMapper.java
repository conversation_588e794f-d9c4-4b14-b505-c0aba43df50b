package com.base.system.mapper;

import com.base.system.domain.TodoReadStatus;

import java.util.List;

/**
 * 待办事项已读状态 数据层
 */
public interface TodoReadStatusMapper {
    /**
     * 查询待办事项已读状态
     */
    public TodoReadStatus selectTodoReadStatusById(Long readStatusId);

    /**
     * 查询待办事项已读状态列表
     */
    public List<TodoReadStatus> selectTodoReadStatusList(TodoReadStatus todoReadStatus);

    /**
     * 新增待办事项已读状态
     */
    public int insertTodoReadStatus(TodoReadStatus todoReadStatus);

    /**
     * 修改待办事项已读状态
     */
    public int updateTodoReadStatus(TodoReadStatus todoReadStatus);

    /**
     * 删除待办事项已读状态
     */
    public int deleteTodoReadStatusById(Long readStatusId);

    /**
     * 批量删除待办事项已读状态
     */
    public int deleteTodoReadStatusByIds(Long[] readStatusIds);
}
