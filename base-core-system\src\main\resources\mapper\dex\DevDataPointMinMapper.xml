<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.dex.mapper.DevDataPointMinMapper">

    <resultMap type="DevDataPointMin" id="DevDataPointMinResult">
        <result property="pointId" column="point_id"/>
        <result property="sourceValue" column="source_value"/>
        <result property="value" column="value"/>
        <result property="monitorTime" column="monitor_time"/>
        <result property="createTime" column="create_time"/>
        <result property="validFlag" column="valid_flag"/>
        <result property="pointFlag" column="point_flag"/>
        <result property="id" column="id"/>
    </resultMap>

    <sql id="selectDevDataPointMinVo">
        select point_id, source_value, value, monitor_time, create_time, valid_flag, point_flag, id from dev_data_point_min
    </sql>

    <select id="selectDevDataPointMinList" parameterType="DevDataPointMin" resultMap="DevDataPointMinResult">
        <include refid="selectDevDataPointMinVo"/>
        <where>
            <if test="sourceValue != null "> and source_value = #{sourceValue}</if>
            <if test="value != null "> and value = #{value}</if>
            <if test="validFlag != null  and validFlag != ''"> and valid_flag = #{validFlag}</if>
            <if test="pointFlag != null  and pointFlag != ''"> and point_flag = #{pointFlag}</if>
        </where>
    </select>

    <select id="selectDevDataPointMinByPointId" parameterType="String" resultMap="DevDataPointMinResult">
        <include refid="selectDevDataPointMinVo"/>
        where point_id = #{pointId}
    </select>

    <select id="getDevDataPointMinList" parameterType="DevDataPointMin" resultType="DevDataPointMin">
        select * from (
            (select bp.point_code_cn factor, round(dm."value") value, dm.monitor_time monitorTime, d.device_id deviceId
            from dex.dev_data_point_min dm
            join dex.dev_base_point bp on bp.point_id=dm.point_id
            join device.env_device_point dp on bp.point_id = dp.point_id
            join device.env_device d on dp.device_id=d.device_id
            where d.device_id = #{deviceId} and dm.point_id = #{pointId}
            and dm.monitor_time &lt;= #{monitorTime}
            order by monitor_time desc
            limit 10)

            UNION

            (select bp.point_code_cn factor, round(dm."value") value, dm.monitor_time monitorTime, d.device_id deviceId
            from dex.dev_data_point_min dm
            join dex.dev_base_point bp on bp.point_id=dm.point_id
            join device.env_device_point dp on bp.point_id = dp.point_id
            join device.env_device d on dp.device_id=d.device_id
            where d.device_id = #{deviceId} and dm.point_id = #{pointId}
            and dm.monitor_time >= #{monitorTime}
            order by monitor_time asc
            limit 10)
        ) t ORDER BY monitorTime asc
    </select>

    <insert id="insertDevDataPointMin" parameterType="DevDataPointMin">
        insert into dev_data_point_min
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointId != null">point_id,</if>
            <if test="sourceValue != null">source_value,</if>
            <if test="value != null">value,</if>
            <if test="monitorTime != null">monitor_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="pointFlag != null">point_flag,</if>
            <if test="id != null">id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointId != null">#{pointId},</if>
            <if test="sourceValue != null">#{sourceValue},</if>
            <if test="value != null">#{value},</if>
            <if test="monitorTime != null">#{monitorTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="pointFlag != null">#{pointFlag},</if>
            <if test="id != null">#{id},</if>
        </trim>
    </insert>

    <update id="updateDevDataPointMin" parameterType="DevDataPointMin">
        update dev_data_point_min
        <trim prefix="SET" suffixOverrides=",">
            <if test="sourceValue != null">source_value = #{sourceValue},</if>
            <if test="value != null">value = #{value},</if>
            <if test="monitorTime != null">monitor_time = #{monitorTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="pointFlag != null">point_flag = #{pointFlag},</if>
            <if test="id != null">id = #{id},</if>
        </trim>
        where point_id = #{pointId}
    </update>

    <delete id="deleteDevDataPointMinByPointId" parameterType="String">
        delete from dev_data_point_min where point_id = #{pointId}
    </delete>

    <delete id="deleteDevDataPointMinByPointIds" parameterType="String">
        delete from dev_data_point_min where point_id in
        <foreach item="pointId" collection="array" open="(" separator="," close=")">
            #{pointId}
        </foreach>
    </delete>
</mapper>