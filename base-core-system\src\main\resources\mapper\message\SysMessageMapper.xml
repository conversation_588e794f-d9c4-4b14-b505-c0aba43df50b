<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.message.mapper.SysMessageMapper">
    
    <resultMap type="SysMessage" id="SysMessageResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="messageConfigId"    column="message_config_id"    />
        <result property="code"    column="code"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="param"    column="param"    />
        <result property="dialogQuery"    column="dialog_query"    />
        <result property="query"    column="query"    />
        <result property="menuId"    column="menu_id"    />
    </resultMap>

    <sql id="selectSysMessageVo">
        select a.id, a.type, a.message_config_id, a.code, a.title, a.content, a.status, a.user_id, a.del_flag, a.create_by, a.create_time, a.update_by, a.update_time, a.remark, a.param
             , b.dialog_query, b.query, b.menu_id
        from sys_message a
        left join sys_message_config b on b.id = a.message_config_id
    </sql>

    <select id="selectSysMessageList" parameterType="SysMessage" resultMap="SysMessageResult">
        <include refid="selectSysMessageVo"/>
        <where>
            a.del_flag = 0
            <if test="type != null  and type != ''"> and a.type = #{type}</if>
            <if test="messageConfigId != null"> and a.message_config_id = #{messageConfigId}</if>
            <if test="code != null  and code != ''"> and a.code = #{code}</if>
            <if test="title != null  and title != ''"> and a.title like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''"> and a.content like concat('%', #{content}, '%')</if>
            <if test="status != null "> and a.status = #{status}</if>
            <if test="userId != null  and userId != ''"> and a.user_id = #{userId}</if>
        </where>
        order by a.status, a.create_time desc
    </select>
    
    <select id="selectSysMessageById" parameterType="Long" resultMap="SysMessageResult">
        <include refid="selectSysMessageVo"/>
        where a.id = #{id}
    </select>
        
    <insert id="insertSysMessage" parameterType="SysMessage" useGeneratedKeys="true" keyProperty="id">
        insert into sys_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="messageConfigId != null">message_config_id,</if>
            <if test="code != null">code,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="param != null">param,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="messageConfigId != null">#{messageConfigId},</if>
            <if test="code != null">#{code},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="param != null">#{param},</if>
         </trim>
    </insert>

    <update id="updateSysMessage" parameterType="SysMessage">
        update sys_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="messageConfigId != null">message_config_id = #{messageConfigId},</if>
            <if test="code != null">code = #{code},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="param != null">param = #{param},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateSysMessageByCondition" parameterType="SysMessage">
        update sys_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        <where>
            status = 0 and user_id = #{userId}
            <if test="type != null and type != ''"> and type = #{type}</if>
            <if test="messageConfigId != null"> and message_config_id = #{messageConfigId}</if>
        </where>
    </update>

    <delete id="deleteSysMessageById" parameterType="Long">
        delete from sys_message where id = #{id}
    </delete>

    <delete id="deleteSysMessageByIds" parameterType="String">
        delete from sys_message where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>