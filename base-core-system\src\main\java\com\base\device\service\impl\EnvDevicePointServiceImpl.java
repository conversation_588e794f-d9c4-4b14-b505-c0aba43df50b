package com.base.device.service.impl;

import com.base.common.utils.StringUtils;
import com.base.device.domain.EnvDevicePoint;
import com.base.device.mapper.EnvDevicePointMapper;
import com.base.device.service.IEnvDevicePointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备-点位 关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class EnvDevicePointServiceImpl implements IEnvDevicePointService
{
    @Autowired
    private EnvDevicePointMapper envDevicePointMapper;

    /**
     * 查询设备-点位 关联关系
     *
     * @param joinId 设备-点位 关联关系主键
     * @return 设备-点位 关联关系
     */
    @Override
    public EnvDevicePoint selectEnvDevicePointByJoinId(Long joinId)
    {
        return envDevicePointMapper.selectEnvDevicePointByJoinId(joinId);
    }

    /**
     * 查询设备-点位 关联关系列表
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 设备-点位 关联关系
     */
    @Override
    public List<EnvDevicePoint> selectEnvDevicePointList(EnvDevicePoint envDevicePoint)
    {
        return envDevicePointMapper.selectEnvDevicePointList(envDevicePoint);
    }

    @Override
    public List<EnvDevicePoint> selectByDeviceId(Long deviceId){
        return this.selectEnvDevicePointList(new EnvDevicePoint().setDeviceId(deviceId));
    }
    @Override
    public List<EnvDevicePoint> selectByDeviceId(String pointId){
        return this.selectEnvDevicePointList(new EnvDevicePoint().setPointId(pointId));
    }

    /**
     * 新增设备-点位 关联关系
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 结果
     */
    @Override
    public int insertEnvDevicePoint(EnvDevicePoint envDevicePoint)
    {
        return envDevicePointMapper.insertEnvDevicePoint(envDevicePoint);
    }

    @Override
    public int batchEnvDevicePoint(List<EnvDevicePoint> envDevicePointList)
    {
        if (StringUtils.isNotEmpty(envDevicePointList)){
            return envDevicePointMapper.batchEnvDevicePoint(envDevicePointList);
        }
        return 0;
    }

    /**
     * 修改设备-点位 关联关系
     *
     * @param envDevicePoint 设备-点位 关联关系
     * @return 结果
     */
    @Override
    public int updateEnvDevicePoint(EnvDevicePoint envDevicePoint)
    {
        return envDevicePointMapper.updateEnvDevicePoint(envDevicePoint);
    }

    /**
     * 批量删除设备-点位 关联关系
     *
     * @param joinIds 需要删除的设备-点位 关联关系主键
     * @return 结果
     */
    @Override
    public int deleteEnvDevicePointByJoinIds(Long[] joinIds)
    {
        return envDevicePointMapper.deleteEnvDevicePointByJoinIds(joinIds);
    }

    /**
     * 删除设备-点位 关联关系信息
     *
     * @param joinId 设备-点位 关联关系主键
     * @return 结果
     */
    @Override
    public int deleteEnvDevicePointByJoinId(Long joinId)
    {
        return envDevicePointMapper.deleteEnvDevicePointByJoinId(joinId);
    }
}
