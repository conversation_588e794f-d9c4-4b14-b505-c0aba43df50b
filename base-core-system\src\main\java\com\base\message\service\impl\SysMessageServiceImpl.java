package com.base.message.service.impl;

import com.alibaba.fastjson2.JSON;
import com.base.common.core.domain.entity.SysDictData;
import com.base.common.utils.DateUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.mqtt.MQTTUtils;
import com.base.message.domain.SysMessage;
import com.base.message.mapper.SysMessageMapper;
import com.base.message.service.ISysMessageService;
import com.base.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息通知Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
@Service
public class SysMessageServiceImpl implements ISysMessageService {
    @Autowired
    private SysMessageMapper sysMessageMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private MQTTUtils mqttUtils;

    /**
     * 查询消息通知
     *
     * @param id 消息通知主键
     * @return 消息通知
     */
    @Override
    public SysMessage selectSysMessageById(Long id) {
        return sysMessageMapper.selectSysMessageById(id);
    }

    /**
     * 查询消息通知列表
     *
     * @param sysMessage 消息通知
     * @return 消息通知
     */
    @Override
    public List<SysMessage> selectSysMessageList(SysMessage sysMessage) {
        return sysMessageMapper.selectSysMessageList(sysMessage);
    }

    /**
     * 新增消息通知
     *
     * @param sysMessage 消息通知
     * @return 结果
     */
    @Override
    public int insertSysMessage(SysMessage sysMessage) {
        int result = 0;
        // 添加消息之前判断配置是否启用
        SysDictData dictData = new SysDictData();
        dictData.setDictType("sys_message_type");
        dictData.setDictValue(sysMessage.getType());
        dictData.setStatus("0");
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        if (StringUtils.isNotEmpty(list)) {
            sysMessage.setCreateTime(DateUtils.getNowDate());
            result = sysMessageMapper.insertSysMessage(sysMessage);
            if (StringUtils.isNotEmpty(sysMessage.getUserId())) {
                mqttUtils.publish("sys_message_admin", 0, JSON.toJSONString(sysMessage));
            }
        }
        return result;
    }

    /**
     * 修改消息通知
     *
     * @param sysMessage 消息通知
     * @return 结果
     */
    @Override
    public int updateSysMessage(SysMessage sysMessage) {
        sysMessage.setUpdateTime(DateUtils.getNowDate());
        return sysMessageMapper.updateSysMessage(sysMessage);
    }

    /**
     * 根据条件修改消息通知
     *
     * @param sysMessage 消息通知
     * @return
     */
    @Override
    public int updateSysMessageByCondition(SysMessage sysMessage) {
        sysMessage.setUpdateTime(DateUtils.getNowDate());
        return sysMessageMapper.updateSysMessageByCondition(sysMessage);
    }

    /**
     * 修改消息通知状态
     *
     * @param sysMessage 消息通知
     * @return 结果
     */
    @Override
    public int updateSysMessageStatus(SysMessage sysMessage) {
        int result = 0;
        // 状态：0未读，1已读，默认0未读
        sysMessage.setStatus(0);
        List<SysMessage> sysMessageList = selectSysMessageList(sysMessage);
        if (StringUtils.isNotEmpty(sysMessageList)) {
            SysMessage sysMessageEntity = sysMessageList.get(0);
            sysMessageEntity.setStatus(1);
            sysMessageEntity.setUpdateBy(sysMessage.getUserId());
            result = updateSysMessageByCondition(sysMessageEntity);
        }
        return result;
    }

    /**
     * 批量删除消息通知
     *
     * @param ids 需要删除的消息通知主键
     * @return 结果
     */
    @Override
    public int deleteSysMessageByIds(Long[] ids) {
        return sysMessageMapper.deleteSysMessageByIds(ids);
    }

    /**
     * 删除消息通知信息
     *
     * @param id 消息通知主键
     * @return 结果
     */
    @Override
    public int deleteSysMessageById(Long id) {
        return sysMessageMapper.deleteSysMessageById(id);
    }
}
