
package com.base.system.service;

import com.base.system.domain.SysStation;
import lombok.SneakyThrows;

import java.util.List;

/**
 * 国/省控站Service接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ISysStationService
{
    /**
     * 查询国/省控站
     *
     * @param stationId 国/省控站主键
     * @return 国/省控站
     */
    public SysStation selectSysStationByStationId(Long stationId);

    /**
     * 查询国/省控站列表
     *
     * @param sysStation 国/省控站
     * @return 国/省控站集合
     */
    public List<SysStation> selectSysStationList(SysStation sysStation);

    /**
     * 新增国/省控站
     *
     * @param sysStation 国/省控站
     * @return 结果
     */
    public int insertSysStation(SysStation sysStation);

    /**
     * 修改国/省控站
     *
     * @param sysStation 国/省控站
     * @return 结果
     */
    public int updateSysStation(SysStation sysStation);

    /**
     * 批量删除国/省控站
     *
     * @param stationIds 需要删除的国/省控站主键集合
     * @return 结果
     */
    public int deleteSysStationByStationIds(Long[] stationIds);

    /**
     * 删除国/省控站信息
     *
     * @param stationId 国/省控站主键
     * @return 结果
     */
    public int deleteSysStationByStationId(Long stationId);

    /*
     * 当企业位置发生改变, 更新国省控站点
     * @param sysConfig 系统配置参数
     * @return 操作结果
     */
    @SneakyThrows
    void updateStationsWhenEnterpriseLocationChanges(String configValueStr);
}
