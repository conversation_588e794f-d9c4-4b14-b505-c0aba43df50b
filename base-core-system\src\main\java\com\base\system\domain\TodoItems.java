package com.base.system.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 待办事项对象 todo_items
 *
 * <AUTHOR>
 * @date 2023-10-01
 */
@Data
public class TodoItems extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 待办事项ID
     */
    @Excel(name = "待办事项ID")
    private Long todoId;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 状态（0：未完成，1：已完成）
     */
    @Excel(name = "状态", readConverterExp = "0=未完成,1=已完成")
    private String status;

    /**
     * 待办内容
     */
    @Excel(name = "待办内容")
    private String content;

    /**
     * 类型标签
     */
    @Excel(name = "类型标签")
    private String tag;

    /**
     * 发送人ID，关联sys_user表
     */
    @Excel(name = "发送人ID")
    private Long senderId;

    /**
     * 接收人ID，关联sys_user表
     */
    @Excel(name = "接收人ID")
    private Long receiverId;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 业务场景类型（例如：个人待办/团队待办/系统待办...）
     */
    @Excel(name = "业务场景类型")
    private String sceneType;

    /**
     * 扩展id 结合sceneType使用
     */
    private String extendId;

    /**
     * 关联产品id
     */
    @Excel(name = "关联产品id")
    private Long productId;

    /**
     * 跳转的菜单ID（sys_menu）
     */
    @Excel(name = "跳转的菜单ID")
    private Long menuId;

    /**
     * 跳转链接参数（如JSON参数）
     */
    @Excel(name = "跳转链接参数")
    private String urlParams;
}
