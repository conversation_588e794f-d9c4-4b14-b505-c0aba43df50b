package com.base.common.utils;

import com.base.common.utils.sign.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class DesUtils {
    private static final Logger log = LoggerFactory.getLogger(DesUtils.class);

    /**
     * @description: 加密算法
     * <AUTHOR>
     * @date 2021/6/2 0002 16:39
     * @version 1.0
     */
    public static String encryptDES(String encryptString, String encryptKey) {
        String afterEncryptString = "";

        log.info("加密原字符串：" + encryptString);
        log.info("秘钥：" + encryptKey);

        try {
            SecretKeySpec key = new SecretKeySpec(encryptKey.getBytes(), "DES");
            Cipher cipher = Cipher.getInstance("DES");
            cipher.init(1, key);
            byte[] encryptedData = cipher.doFinal(encryptString.getBytes("UTF-8"));
            afterEncryptString = Base64.encode(encryptedData);
        } catch (Exception e) {
            //  e.printStackTrace();
        }
        log.info("加密后：" + afterEncryptString);
        return afterEncryptString;
    }

    /**
     * @description: 解密算法
     * <AUTHOR>
     * @date 2021/6/2 0002 16:40
     * @version 1.0
     */
    public static String decryptDES(String decryptString, String decryptKey) throws Exception {
        String afterDecryptString = null;
        byte[] byteMi = Base64.decode(decryptString);
        SecretKeySpec key = new SecretKeySpec(decryptKey.getBytes(), "DES");
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(2, key);
        byte[] decryptData = cipher.doFinal(byteMi);
        return new String(decryptData, "UTF-8");

    }

    public static class ThirdLoginVO {
        /*账号*/
        String account;
        /*时间戳*/
        Long timestamp;

        public String getAccount() {
            return account;
        }

        public void setAccount(String account) {
            this.account = account;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }
    }

    /**
     * sign 签名值示例代码
     **/
    public static void main(String[] args) {
        String appCode = "BONC";
        String busiParam = "{\"userName\":\"shangshuai\"}";
        Long timestamp = 1678872464000L;
        StringBuffer buffer = new StringBuffer().append(appCode).append(busiParam).append(timestamp);
        System.out.println(DesUtils.encryptDES(buffer.toString(), "WI45#tr$"));
    }
}
