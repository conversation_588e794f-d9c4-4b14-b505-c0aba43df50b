{"expireTime": 0, "productList": [{"product_id": 1, "name": "环保管控", "icon": "ai-round", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": null, "introduction": "围绕超低排放的核心指标，以政府和专家的视角，打造环保超低排放管控治系统，以数据为支撑，为企业“自证合规”，为成功通过验收和体现同行竞争性加分。同时以企业环保日常工作为基础，构建信息化智能化管理工具，进一步提升工作效率，让环保管理越来越省心、省事、省钱。", "update_logs": [{"version": "6.0.0", "log": "新增了清洁运输模块的功能，并优化了无组织排放的监控算法。", "update_time": "2025-04-15"}, {"version": "5.0.0", "log": "初始版本，包含了有组织排放和无组织排放的基础功能。", "update_time": "2024-12-01"}], "tags": ["门户", "数据报表", "表单"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0, "children": [{"product_id": 101, "name": "有组织排放", "icon": "border-cloud", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": "无特定二维码", "introduction": "监控和管理来自固定排放源（如烟囱、排气筒）的污染物排放情况。", "update_logs": [{"version": "6.0.0", "log": "优化了数据采集频率，提升了监控精度。", "update_time": "2025-04-15"}], "tags": ["数据报表", "实时监控"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 102, "name": "无组织排放", "icon": "clouds", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": "无特定二维码", "introduction": "监测和控制非固定点源（如物料堆场、生产过程逸散）的排放。", "update_logs": [{"version": "6.0.0", "log": "新增了实时监测功能，提高了数据采集的准确性。", "update_time": "2025-04-15"}, {"version": "5.0.0", "log": "初始版本，提供了基本的无组织排放监控功能。", "update_time": "2025-04-15"}], "tags": ["实时监控", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 103, "name": "清洁运输", "icon": "clean-car", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/无组织监管_20250422172522A002.jpg", "qr_code": "无特定二维码", "introduction": "管理和优化运输过程中的环保措施，减少运输环节的污染。", "update_logs": [{"version": "6.0.0", "log": "优化了运输路线规划算法，减少了污染物排放。", "update_time": "2025-04-15"}, {"version": "5.0.0", "log": "初始版本，提供了基本的清洁运输管理功能。", "update_time": "2025-04-15"}], "tags": ["优化算法", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 104, "name": "日常环保", "icon": "cycle", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/无组织监管_20250422172522A002.jpg", "qr_code": "无特定二维码", "introduction": "处理和记录企业日常运营中的各项环保管理工作。", "update_logs": [{"version": "6.0.0", "log": "新增了日常环保任务管理功能。", "update_time": "2025-04-15"}], "tags": ["任务管理", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 105, "name": "系统设置", "icon": "big-screen", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/无组织监管_20250422172522A002.jpg", "qr_code": "无特定二维码", "introduction": "完成系统的基础设置", "update_logs": [], "tags": ["用户管理", "角色管理"], "features": [], "readStatus": 0}]}, {"product_id": 2, "name": "节能降本", "icon": "ai-round", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": null, "introduction": "对重点用能设备的监控和优化，智能分析瓶颈并提供优化运行策略，实现能源消耗的降低和运营成本的节约。预测性维护预警设备故障，减少非计划停机，延长设备使用寿命降低维护费用；数据驱动精细化能源管理，助力企业实现显著的节能降本目标，提升盈利能力", "update_logs": [{"version": "6.0.0", "log": "新增了XX节能模块，并修复了空压机节能模块的若干问题。", "update_time": "2025-04-15"}, {"version": "5.0.0", "log": "初始版本，包含了空压机节能和除尘器AI节能的基础功能。", "update_time": "2025-04-15"}], "tags": ["节能优化", "数据报表", "AI算法"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0, "children": [{"product_id": 201, "name": "空压机节能", "icon": "app-ico-4", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/无组织监管_20250422172522A002.jpg", "qr_code": "无特定二维码", "introduction": "优化空压机系统的运行策略，降低能耗，节省电费开支。", "update_logs": [{"version": "6.0.0", "log": "修复了能耗计算中的一个关键问题，提升了准确性。", "update_time": "2025-04-15"}], "tags": ["能耗计算", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 202, "name": "除尘器AI节能", "icon": "app-ico-2", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": "无特定二维码", "introduction": "利用人工智能算法智能调节除尘设备运行，在保证除尘效果的同时最大化节能。", "update_logs": [{"version": "6.0.0", "log": "优化了AI算法，进一步提升了节能效果。", "update_time": "2025-04-15"}, {"version": "5.0.0", "log": "初始版本，实现了基本的AI节能功能。", "update_time": "2025-04-15"}], "tags": ["AI算法", "节能优化"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 203, "name": "XX节能", "icon": "app-ico-9", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/无组织监管_20250422172522A002.jpg", "qr_code": "无特定二维码", "introduction": "代表某一特定设备或流程的节能管理模块（具体名称待定）。", "update_logs": [{"version": "6.0.0", "log": "新增了XX节能模块的基础功能。", "update_time": "2025-04-15"}], "tags": ["节能管理", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 204, "name": "...", "icon": "app-ico-5", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": "无特定二维码", "introduction": "更多节能降本相关的功能模块或配置选项，当前状态提示异常。", "update_logs": [{"version": "6.0.0", "log": "修复了异常状态的问题，恢复了正常运行。", "update_time": "2025-04-15"}], "tags": ["异常处理", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}]}, {"product_id": 3, "name": "低碳减排", "icon": "ai-round", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": null, "introduction": "助力重污染企业低成本履约、降低经营风险以及碳资产保值增值，成为标杆企业。助力设定科学减排目标并实时跟踪进展，有效降低碳排放强度。规范化管理碳排放配额等碳资产，支持碳交易决策，发掘潜在经济价值；满足国内外碳报告与披露要求，提升企业可持续发展形象，助力构建低碳未来。", "update_logs": [{"version": "6.0.0", "log": "新增了碳减排模块，并优化了能耗管理的数据分析能力。", "update_time": "2025-04-15"}, {"version": "5.0.0", "log": "初始版本，包含了碳排放和能耗管理的基础功能。", "update_time": "2025-04-15"}], "tags": ["低碳减排", "数据报表", "能耗管理"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0, "children": [{"product_id": 301, "name": "碳排放", "icon": "app-ico-7", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": "无特定二维码", "introduction": "核算和监控企业的温室气体排放总量及来源。", "update_logs": [{"version": "6.0.0", "log": "增加了对多种温室气体的支持，并优化了核算算法。", "update_time": "2025-04-15"}], "tags": ["温室气体", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 302, "name": "能耗管理", "icon": "app-ico-3", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": "无特定二维码", "introduction": "管理和分析企业各类能源消耗数据，识别节能潜力。", "update_logs": [{"version": "6.0.0", "log": "新增了能耗数据分析功能，帮助用户更好地理解能源消耗情况。", "update_time": "2025-04-15"}, {"version": "5.0.0", "log": "初始版本，提供了基本的能耗管理功能。", "update_time": "2025-04-15"}], "tags": ["能耗分析", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 303, "name": "碳减排", "icon": "app-ico-6", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": "无特定二维码", "introduction": "实施和跟踪各项碳减排项目的效果与进展。", "update_logs": [{"version": "6.0.0", "log": "新增了碳减排项目管理功能。", "update_time": "2025-04-15"}], "tags": ["碳减排项目", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}, {"product_id": 304, "name": "...", "icon": "app-ico-5", "version": "6.0.0", "status": "", "is_enabled": "", "app_image": "/profile/upload/2025/04/22/有组织监管_20250422172438A001.jpg", "qr_code": "无特定二维码", "introduction": "提供更多与低碳减排相关的分析工具或管理功能。", "update_logs": [{"version": "6.0.0", "log": "新增了更多低碳减排相关的分析工具。", "update_time": "2025-04-15"}], "tags": ["低碳分析", "数据报表"], "features": ["环境监测", "数据报表", "功能展示"], "readStatus": 0}]}]}