<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.message.mapper.SysMessageConfigMapper">
    
    <resultMap type="SysMessageConfig" id="SysMessageConfigResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="menuId"    column="menu_id"    />
        <result property="dialogQuery"    column="dialog_query"    />
        <result property="query"    column="query"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysMessageConfigVo">
        select id, type, menu_id, dialog_query, query, del_flag, create_by, create_time, update_by, update_time, remark from sys_message_config
    </sql>

    <select id="selectSysMessageConfigList" parameterType="SysMessageConfig" resultMap="SysMessageConfigResult">
        <include refid="selectSysMessageConfigVo"/>
        <where>
            del_flag = 0
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="menuId != null "> and menu_id = #{menuId}</if>
            <if test="dialogQuery != null  and dialogQuery != ''"> and dialog_query = #{dialogQuery}</if>
            <if test="query != null  and query != ''"> and query = #{query}</if>
        </where>
    </select>
    
    <select id="selectSysMessageConfigById" parameterType="Long" resultMap="SysMessageConfigResult">
        <include refid="selectSysMessageConfigVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSysMessageConfig" parameterType="SysMessageConfig" useGeneratedKeys="true" keyProperty="id">
        insert into sys_message_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="menuId != null">menu_id,</if>
            <if test="dialogQuery != null">dialog_query,</if>
            <if test="query != null">query,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="menuId != null">#{menuId},</if>
            <if test="dialogQuery != null">#{dialogQuery},</if>
            <if test="query != null">#{query},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysMessageConfig" parameterType="SysMessageConfig">
        update sys_message_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="menuId != null">menu_id = #{menuId},</if>
            <if test="dialogQuery != null">dialog_query = #{dialogQuery},</if>
            <if test="query != null">query = #{query},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysMessageConfigById" parameterType="Long">
        update sys_message_config set del_flag = 2 where id = #{id}
    </delete>

    <delete id="deleteSysMessageConfigByIds" parameterType="String">
        update sys_message_config set del_flag = 2 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>