package com.base.framework.config;

import com.base.framework.config.properties.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

/**
 * redis配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class RedisConfig extends CachingConfigurerSupport
{
    @Bean
    @ConfigurationProperties("spring.redis.base")
    public RedisProperties baseRedisProperties() {
        return new RedisProperties();
    }

    @Bean
    @ConfigurationProperties("spring.redis.dex")
    public RedisProperties dexRedisProperties() {
        return new RedisProperties();
    }

    @Bean
    @ConfigurationProperties("spring.redis.atlanta")
    public RedisProperties atlantaRedisProperties() {
        return new RedisProperties();
    }

    // Base RedisTemplate
    @Bean("redisTemplateBase")
    public RedisTemplate<String, Object> redisTemplateBase() {
        return createRedisTemplate(baseRedisProperties());
    }

    // DEX RedisTemplate
    @Bean("redisTemplateDex")
    public RedisTemplate<String, Object> redisTemplateDex() {
        return createRedisTemplate(dexRedisProperties());
    }

    // Atlanta RedisTemplate
    @Bean("redisTemplateAtlanta")
    public RedisTemplate<String, Object> redisTemplateAtlanta() {
        return createRedisTemplate(atlantaRedisProperties());
    }

    private RedisTemplate<String, Object> createRedisTemplate(RedisProperties props) {
        // 构建客户端配置
        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                .commandTimeout(Duration.ofSeconds(10))
                .build();

        // 构建 RedisStandaloneConfiguration（包含 host、port、database、password）
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration(
                props.getHost(), props.getPort()
        );
        redisConfig.setDatabase(props.getDatabase());
        if (props.getPassword() != null && !props.getPassword().isEmpty()) {
            redisConfig.setPassword(RedisPassword.of(props.getPassword()));
        }

        // 创建连接工厂，使用 redisConfig 包含的配置（包括密码）
        LettuceConnectionFactory factory = new LettuceConnectionFactory(redisConfig, clientConfig);
        factory.afterPropertiesSet();

        // 创建 RedisTemplate
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        FastJson2JsonRedisSerializer serializer = new FastJson2JsonRedisSerializer(Object.class);

        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);
        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public DefaultRedisScript<Long> limitScript()
    {
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(limitScriptText());
        redisScript.setResultType(Long.class);
        return redisScript;
    }

    /**
     * 限流脚本
     */
    private String limitScriptText()
    {
        return "local key = KEYS[1]\n" +
                "local count = tonumber(ARGV[1])\n" +
                "local time = tonumber(ARGV[2])\n" +
                "local current = redis.call('get', key);\n" +
                "if current and tonumber(current) > count then\n" +
                "    return tonumber(current);\n" +
                "end\n" +
                "current = redis.call('incr', key)\n" +
                "if tonumber(current) == 1 then\n" +
                "    redis.call('expire', key, time)\n" +
                "end\n" +
                "return tonumber(current);";
    }
}
