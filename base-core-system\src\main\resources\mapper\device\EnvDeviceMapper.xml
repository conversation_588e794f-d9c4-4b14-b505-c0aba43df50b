<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.device.mapper.EnvDeviceMapper">

    <resultMap type="EnvDevice" id="EnvDeviceResult">
        <result property="deviceName"    column="device_name"    />
        <result property="e9Code"    column="e9_code"    />
        <result property="processId"    column="process_id"    />
        <result property="posX"    column="pos_x"    />
        <result property="posY"    column="pos_y"    />
        <result property="posZ"    column="pos_z"    />
        <result property="mainType"    column="main_type"    />
        <result property="subType"    column="sub_type"    />
        <result property="ip"    column="ip"    />
        <result property="port"    column="port"    />
        <result property="showFlag"    column="show_flag"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="sort"    column="sort"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="runState"    column="run_state"    />
        <result property="fieldKey"    column="field_key"    />
        <result property="deptId"    column="dept_id"    />
        <result property="fieldJson"    column="field_json"   jdbcType="OTHER" typeHandler="com.base.common.handler.JsonbToFastJSON2JSONObjectTypeHandler" />
        <result property="location"    column="location"    />
        <result property="deviceId"    column="device_id"    />
    </resultMap>

    <sql id="selectEnvDeviceVo">
        select device_name, e9_code, process_id, pos_x, pos_y, pos_z, main_type, sub_type, ip, port, show_flag, del_flag, sort,
               remark, create_time, create_by, update_time, update_by, run_state, field_key, dept_id, field_json, location, device_id
        from device.env_device
    </sql>

    <select id="selectEnvDeviceList" parameterType="EnvDevice" resultMap="EnvDeviceResult">
        <include refid="selectEnvDeviceVo"/>
        <where>
            <if test="deviceId != null and (deviceIdList == null or deviceIdList.size() == 0) ">and device_id =
                #{deviceId}
            </if>
            <if test="deviceIdList != null and deviceIdList.size() > 0">
                and device_id in
                <foreach collection="deviceIdList" item="dId" open="(" close=")" separator=",">
                    #{dId}
                </foreach>
            </if>
            <if test="deviceName != null  and deviceName != '' and (queryStr == null or queryStr == '') ">and
                device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="e9Code != null  and e9Code != '' and (queryStr == null or queryStr == '') ">and e9_code like
                concat('%', #{e9Code}, '%')
            </if>
            <if test="queryStr != null  and queryStr != ''">and (e9_code like concat('%', #{queryStr}, '%') or
                (device_name like concat('%', #{queryStr}, '%'))
            </if>
            <if test="processId != null and (processIdList == null or processIdList.size() == 0)">and process_id =
                #{processId}
            </if>
            <if test="processIdList != null and processIdList.size() > 0">
                and process_id in
                <foreach collection="processIdList" item="pId" open="(" close=")" separator=",">
                    #{pId}
                </foreach>
            </if>
            <if test="posX != null  and posX != ''">and pos_x = #{posX}</if>
            <if test="posY != null  and posY != ''">and pos_y = #{posY}</if>
            <if test="posZ != null  and posZ != ''">and pos_z = #{posZ}</if>
            <if test="mainType != null  and mainType != ''">and main_type = #{mainType}</if>
            <if test="subType != null  and subType != '' and (subTypeList == null or subTypeList.size() ==0) ">and
                sub_type = #{subType}
            </if>
            <if test="subTypeList != null and subTypeList.size() > 0">
                and sub_type in
                <foreach collection="subTypeList" item="sType" open="(" close=")" separator=",">
                    #{sType}
                </foreach>
            </if>
            <if test="ip != null  and ip != ''">and ip = #{ip}</if>
            <if test="port != null  and port != ''">and port = #{port}</if>
            <if test="showFlag != null ">and show_flag = #{showFlag}</if>
            <if test="sort != null ">and sort = #{sort}</if>
            <if test="runState != null  and runState != ''">and run_state = #{runState}</if>
            <if test="fieldKey != null  and fieldKey != ''">and field_key = #{fieldKey}</if>
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="location != null  and location != ''">and location = #{location}</if>
            <if test="delFlag == null and (delFlagList == null or delFlagList.size() == 0)">and del_flag = '0'</if>
            <if test="delFlag != null and (delFlagList == null or delFlagList.size() == 0)">and del_flag = #{delFlag}
            </if>
            <if test="delFlagList != null and delFlagList.size() > 0">
                and del_flag in
                <foreach collection="delFlagList" item="dFlag" open="(" close=")" separator=",">
                    #{dFlag}
                </foreach>
            </if>
            <if test="fieldJson != null and fieldJson.isEmpty() == false">
                and field_json @> #{fieldJson, typeHandler=com.base.common.handler.JsonToStringTypeHandler}::jsonb
            </if>

        </where>

        ${orderByField}
    </select>

    <select id="selectEnvDevicePointJoinList" parameterType="EnvDevice" resultMap="EnvDeviceResult">

        select a.*, count(b.device_id) as pointCount, string_agg(c.point_code_cn, '；') as pointName
        from device.env_device a left join device.env_device_point b ON a.device_id = b.device_id
        left join dex.dev_base_point c ON c.point_id = b.point_id
        <where>
            <if test="deviceId != null and (deviceIdList == null or deviceIdList.size() == 0) ">and a.device_id = #{deviceId}</if>
            <if test="deviceIdList != null and deviceIdList.size() > 0">
                and a.device_id in
                <foreach collection="deviceIdList" item="dId" open="(" close=")" separator=",">
                    #{dId}
                </foreach>
            </if>
            <if test="deviceName != null  and deviceName != '' and (queryStr == null or queryStr == '') "> and a.device_name like concat('%', #{deviceName}, '%')</if>
            <if test="e9Code != null  and e9Code != '' and (queryStr == null or queryStr == '') "> and a.e9_code like concat('%', #{e9Code}, '%')</if>
            <if test="queryStr != null  and queryStr != ''"> and (e9_code like concat('%', #{queryStr}, '%') or (a.device_name like concat('%', #{queryStr}, '%') or (c.point_code like concat('%', #{queryStr}, '%') or (c.point_code_cn like concat('%', #{queryStr}, '%'))</if>
            <if test="processId != null and (processIdList == null or processIdList.size() == 0)"> and a.process_id = #{processId}</if>
            <if test="processIdList != null and processIdList.size() > 0">
                and a.process_id in
                <foreach collection="processIdList" item="pId" open="(" close=")" separator=",">
                    #{pId}
                </foreach>
            </if>
            <if test="posX != null  and posX != ''"> and a.pos_x = #{posX}</if>
            <if test="posY != null  and posY != ''"> and a.pos_y = #{posY}</if>
            <if test="posZ != null  and posZ != ''"> and a.pos_z = #{posZ}</if>
            <if test="mainType != null  and mainType != ''"> and a.main_type = #{mainType}</if>
            <if test="subType != null  and subType != '' and (subTypeList == null or subTypeList.size() ==0) "> and a.sub_type = #{subType}</if>
            <if test="subTypeList != null and subTypeList.size() > 0">
                and a.sub_type in
                <foreach collection="subTypeList" item="sType" open="(" close=")" separator=",">
                    #{sType}
                </foreach>
            </if>
            <if test="ip != null  and ip != ''"> and a.ip = #{ip}</if>
            <if test="port != null  and port != ''"> and a.port = #{port}</if>
            <if test="showFlag != null "> and a.show_flag = #{showFlag}</if>
            <if test="sort != null "> and a.sort = #{sort}</if>
            <if test="runState != null  and runState != ''"> and a.run_state = #{runState}</if>
            <if test="fieldKey != null  and fieldKey != ''"> and a.field_key = #{fieldKey}</if>
            <if test="deptId != null "> and a.dept_id = #{deptId}</if>
            <if test="location != null  and location != ''"> and a.location = #{location}</if>
            <if test="delFlag == null and (delFlagList == null or delFlagList.size() == 0)">a.del_flag = '0'</if>
            <if test="delFlag != null and (delFlagList == null or delFlagList.size() == 0)">a.del_flag = #{delFlag}</if>
            <if test="delFlagList != null and delFlagList.size() > 0">
                and a.del_flag in
                <foreach collection="delFlagList" item="dFlag" open="(" close=")" separator=",">
                    #{dFlag}
                </foreach>
            </if>
            <if test="fieldJson != null and fieldJson.isEmpty() == false">
                and field_json @> #{fieldJson, typeHandler=com.base.common.handler.JsonToStringTypeHandler}::jsonb
            </if>
        </where>
        group by a.device_id
        <if test="pointCount != null and pointCount > 0 and pointCount lte 10"> having count(b.device_id) = #{pointCount}::bigint</if>
        <if test="pointCount != null and pointCount > 10"> having count(b.device_id) > 10</if>

        ${orderByField}
    </select>

    <select id="selectEnvDeviceByDeviceId" parameterType="Long" resultMap="EnvDeviceResult">
        <include refid="selectEnvDeviceVo"/>
        where device_id = #{deviceId} and del_flag = '0'
    </select>

    <select id="countByRunState" resultType="com.base.device.domain.dto.EnvDeviceRunStateCountDTO">
        select run_state as "runState", count(1) as "number" from device.env_device
        <where>
            sub_type in
            <foreach collection="list" item="sType" open="(" close=")" separator=",">
                #{sType}
            </foreach> and del_flag = '0'
        </where>
        group by run_state
    </select>

    <select id="queryMinTop" resultType="java.util.Map">
        SELECT COALESCE(MIN((field_json -> 'to_top')::numeric), 100)::integer AS minSort
        FROM device.env_device
        WHERE del_flag = '0' AND field_json ? 'to_top'
    </select>

    <insert id="insertEnvDevice" parameterType="EnvDevice" useGeneratedKeys="true" keyProperty="deviceId">
        insert into device.env_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">device_name,</if>
            <if test="e9Code != null">e9_code,</if>
            <if test="processId != null">process_id,</if>
            <if test="posX != null">pos_x,</if>
            <if test="posY != null">pos_y,</if>
            <if test="posZ != null">pos_z,</if>
            <if test="mainType != null">main_type,</if>
            <if test="subType != null">sub_type,</if>
            <if test="ip != null">ip,</if>
            <if test="port != null">port,</if>
            <if test="showFlag != null">show_flag,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="sort != null">sort,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="runState != null">run_state,</if>
            <if test="fieldKey != null">field_key,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="location != null">location,</if>
            field_json,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">#{deviceName},</if>
            <if test="e9Code != null">#{e9Code},</if>
            <if test="processId != null">#{processId},</if>
            <if test="posX != null">#{posX},</if>
            <if test="posY != null">#{posY},</if>
            <if test="posZ != null">#{posZ},</if>
            <if test="mainType != null">#{mainType},</if>
            <if test="subType != null">#{subType},</if>
            <if test="ip != null">#{ip},</if>
            <if test="port != null">#{port},</if>
            <if test="showFlag != null">#{showFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="sort != null">#{sort},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="runState != null">#{runState},</if>
            <if test="fieldKey != null">#{fieldKey},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="location != null">#{location},</if>
            <if test="fieldJson != null">#{fieldJson, typeHandler=com.base.common.handler.JsonToStringTypeHandler}::jsonb,</if>
            <if test="fieldJson == null">'{}'::jsonb</if>
         </trim>
    </insert>

    <update id="updateEnvDevice" parameterType="EnvDevice">
        update device.env_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="e9Code != null">e9_code = #{e9Code},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="posX != null">pos_x = #{posX},</if>
            <if test="posY != null">pos_y = #{posY},</if>
            <if test="posZ != null">pos_z = #{posZ},</if>
            <if test="mainType != null">main_type = #{mainType},</if>
            <if test="subType != null">sub_type = #{subType},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="port != null">port = #{port},</if>
            <if test="showFlag != null">show_flag = #{showFlag},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="runState != null">run_state = #{runState},</if>
            <if test="fieldKey != null">field_key = #{fieldKey},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="location != null">location = #{location},</if>
            <if test="fieldJson != null">
            field_json = field_json || #{fieldJson, typeHandler=com.base.common.handler.JsonToStringTypeHandler}::jsonb,
            </if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <update id="batchUpdateEnvDevice" parameterType="EnvDevice">
        update device.env_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="e9Code != null">e9_code = #{e9Code},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="posX != null">pos_x = #{posX},</if>
            <if test="posY != null">pos_y = #{posY},</if>
            <if test="posZ != null">pos_z = #{posZ},</if>
            <if test="mainType != null">main_type = #{mainType},</if>
            <if test="subType != null">sub_type = #{subType},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="port != null">port = #{port},</if>
            <if test="showFlag != null">show_flag = #{showFlag},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="runState != null">run_state = #{runState},</if>
            <if test="fieldKey != null">field_key = #{fieldKey},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="location != null">location = #{location},</if>
            <if test="fieldJson != null">
                field_json = field_json || #{fieldJson, typeHandler=com.base.common.handler.JsonToStringTypeHandler}::jsonb,
            </if>
        </trim>
        where
        device_id in
        <foreach collection="deviceIdList" item="dId" open="(" close=")" separator=",">
            #{dId}
        </foreach>
    </update>

    <update id="clearTop">
        update device.env_device set field_json = field_json - 'to_top', update_time = now() where device_id = #{deviceId}
    </update>

    <update id="deleteEnvDeviceByDeviceId" parameterType="Long">
        update device.env_device set del_flag = '2', update_time = now() where device_id = #{deviceId}
    </update>

    <update id="deleteEnvDeviceByDeviceIds" parameterType="String">
        update device.env_device set del_flag = '2' where device_id in
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </update>

    <insert id="batchEnvDevice">
        insert into device.env_device( device_name, e9_code, process_id, pos_x, pos_y, pos_z, main_type, sub_type, ip, port, show_flag, del_flag, sort, remark, create_time, create_by,
                                      update_time, update_by, run_state, field_key, dept_id, location, field_json) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.deviceName}, #{item.e9Code}, #{item.processId}, #{item.posX}, #{item.posY}, #{item.posZ}, #{item.mainType}, #{item.subType}, #{item.ip}, #{item.port}, #{item.showFlag},
            <if test="item.delFlag == null">'0'</if><if test="item.fieldJson != null">#{item.delFlag}</if>, #{item.sort}, #{item.remark}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy}, #{item.runState}, #{item.fieldKey}, #{item.deptId},
            #{item.location}, <if test="item.fieldJson == null">'{}'::jsonb</if><if test="item.fieldJson != null">#{item.fieldJson}</if>)
        </foreach>
    </insert>
</mapper>
