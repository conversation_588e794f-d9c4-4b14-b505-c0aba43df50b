package com.base.device.domain.dto;

import com.base.device.domain.EnvDevice;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

@Data
public class EnvDeviceScreenDTO {

    /**
     * 查询场景 env_device:设备  material:物料 in_transport:厂内车辆  out_transport:场外车辆
     */
    @JsonAlias("scene")
    private String scene;

    /**
     * 是否查询设备数量
     */
    @JsonAlias("query_num")
    private Boolean queryNum = false;

    /**
     * 是否查询设备
     */
    @JsonAlias("query_device")
    private Boolean queryDevice = false;

    /**
     * 是否查询主类
     */
    @JsonAlias("query_main")
    private Boolean queryMain = false;

    /**
     * 是否查询子类
     */
    @JsonAlias("query_sub")
    private Boolean querySub = false;

    /**
     * 设备信息, 用来筛选
     */
    @JsonAlias("device_item")
    private EnvDevice deviceItem;

    @JsonAlias("status")
    private Integer status;

}
