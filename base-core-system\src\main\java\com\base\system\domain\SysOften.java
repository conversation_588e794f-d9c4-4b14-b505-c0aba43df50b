package com.base.system.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.base.common.core.domain.entity.SysMenu;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
/**
 * 用户常用功能对象 sys_often
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public class SysOften extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long oftenId;

    /**
     * 类型  app:应用  menu:菜单  customize:自定义
     */
    @Excel(name = "类型  app:应用  menu:菜单  customize:自定义")
    private String oftenType;

    /**
     * 应用id
     */
    @Excel(name = "应用id")
    private String externalId;

    /**
     * 第三方地址
     */
    @Excel(name = "第三方地址")
    private String oftenPath;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String oftenName;

    /**
     * 图标
     */
    @Excel(name = "图标")
    private String oftenIcon;

    /**
     * 打开方式 _self:当前页面  _blank:新窗口
     */
    @Excel(name = "打开方式 _self:当前页面  _blank:新窗口")
    private String oftenTarget;

    private Long sort;

    /**
     * 图像
     */
    private String oftenImage;

    private SysMenu sysMenu;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("oftenId", getOftenId())
                .append("oftenType", getOftenType())
                .append("externalId", getExternalId())
                .append("oftenPath", getOftenPath())
                .append("oftenName", getOftenName())
                .append("oftenIcon", getOftenIcon())
                .append("oftenImage", getOftenImage())
                .append("oftenTarget", getOftenTarget())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .toString();
    }
}
