package com.base.web.controller.system;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.entity.SysMenu;
import com.base.common.enums.BusinessType;
import com.base.common.utils.StringUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.system.domain.Product;
import com.base.system.domain.SysOften;
import com.base.system.service.IProductInfoService;
import com.base.system.service.ISysMenuService;
import com.base.system.service.ISysOftenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户常用功能Controller
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RestController
@RequestMapping("/system/often")
public class SysOftenController extends BaseController {

    @Autowired
    private ISysOftenService sysOftenService;

    @Autowired
    private ISysMenuService sysMenuService;

    @Autowired
    private IProductInfoService productInfoService;

    /**
     * 查询用户常用功能列表
     */
    @PreAuthorize("@ss.hasPermi('system:often:list')")
    @RequestMapping("/list")
    public AjaxResult list(@RequestBody SysOften sysOften) {
        sysOften.setCreateBy(getUsername());
        List<SysOften> list = sysOftenService.selectSysOftenList(sysOften);
        return AjaxResult.success(list);
    }

    /**
     * 查询用户菜单列表
     *
     * @param sysMenu
     * @return
     */
    @PreAuthorize("@ss.hasPermi('system:often:list')")
    @RequestMapping("/menu/list")
    public AjaxResult menuList(@RequestBody SysMenu sysMenu) {
        // 查询出所有菜单
        List<SysMenu> sysMenuList = sysMenuService.selectMenuList(sysMenu.setStatus("0").setVisible("0"), getUserId());

        // 查询出用户收藏的菜单
        SysOften sysOften = new SysOften().setOftenType("menu");
        sysOften.setCreateBy(getUsername());
        List<SysOften> sysOftenList = sysOftenService.selectSysOftenList(sysOften);

        // 获取应用ID
        // 先把 externalId 提取成 Set<Integer>，方便快速匹配
        Map<Long, Long> externalIdMap = sysOftenList.stream()
                .filter(o -> o.getExternalId() != null)
                .collect(Collectors.toMap(
                        o -> Long.valueOf(o.getExternalId()),
                        SysOften::getOftenId
                ));

        for (SysMenu sysM : sysMenuList) {
            Long oftenId = externalIdMap.get(sysM.getMenuId());
            if (StringUtils.isNotNull(oftenId)){
                sysM.setSelect(true).setExternalId(oftenId.toString());
            }
        }
        return AjaxResult.success(sysMenuList);
    }

    /**
     * 查询用户应用列表
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('system:often:list')")
    @RequestMapping("/product/list")
    public AjaxResult productList() {
        // 查询出所有菜单
        List<SysMenu> sysMenus = sysMenuService.selectMenuList(new SysMenu().setStatus("0").setVisible("0"), getUserId());
        Set<Integer> productIds = sysMenus.stream()
                .map(SysMenu::getProductId)  // 提取 productId
                .filter(Objects::nonNull)    // 过滤掉 null（防止空指针异常）
                .map(Long::intValue)  // 提取 productId
                .collect(Collectors.toSet()); // 收集成 Set
        // 获取菜单所属应用
        List<Product> productList = productInfoService.getProductListByIds(productIds, "已启用");

        // 查询出用户收藏的应用
        SysOften appOften = new SysOften().setOftenType("app");
        appOften.setCreateBy(getUsername());
        List<SysOften> sysOftenList = sysOftenService.selectList(appOften);
        // 获取应用ID
        // 先把 externalId 提取成 Set<Integer>，方便快速匹配
        Map<Integer, Long> externalIdMap = sysOftenList.stream()
                .filter(o -> o.getExternalId() != null)
                .collect(Collectors.toMap(
                        o -> Integer.valueOf(o.getExternalId()),
                        SysOften::getOftenId
                ));

        // 递归处理, 标注哪些为用户收藏
        for (Product product : productList) {
            setSelectFlag(product, externalIdMap);
        }
        return AjaxResult.success(productList);
    }

    // 递归方法，处理树状结构
    private void setSelectFlag(Product product, Map<Integer, Long> externalIdMap) {
        if (product == null) {
            return;
        }
        // 判断当前节点
        Long externalId = externalIdMap.get(product.getProductId());
        if (StringUtils.isNotNull(externalId)) {
            product.setSelect(true);
            product.setExternalId(externalId.toString());
        }
        // 递归处理子节点
        if (product.getChildren() != null && !product.getChildren().isEmpty()) {
            for (Product child : product.getChildren()) {
                setSelectFlag(child, externalIdMap);
            }
        }
    }

    /**
     * 导出用户常用功能列表
     */
    @PreAuthorize("@ss.hasPermi('system:often:export')")
    @Log(title = "用户常用功能", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysOften sysOften) {
        List<SysOften> list = sysOftenService.selectSysOftenList(sysOften);
        ExcelUtil<SysOften> util = new ExcelUtil<SysOften>(SysOften.class);
        util.exportExcel(response, list, "用户常用功能数据");
    }

    /**
     * 获取用户常用功能详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:often:query')")
    @GetMapping(value = "/{oftenId}")
    public AjaxResult getInfo(@PathVariable("oftenId") Long oftenId) {
        return success(sysOftenService.selectSysOftenByOftenId(oftenId));
    }

    /**
     * 新增用户常用功能
     */
    @PreAuthorize("@ss.hasPermi('system:often:add')")
    @Log(title = "用户常用功能", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody SysOften sysOften) {
        return toAjax(sysOftenService.insertSysOften(sysOften));
    }

    /**
     * 修改用户常用功能
     */
    @PreAuthorize("@ss.hasPermi('system:often:edit')")
    @Log(title = "用户常用功能", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody SysOften sysOften) {
        return toAjax(sysOftenService.updateSysOften(sysOften));
    }

    /**
     * 删除用户常用功能
     */
    @PreAuthorize("@ss.hasPermi('system:often:remove')")
    @Log(title = "用户常用功能", businessType = BusinessType.DELETE)
    @GetMapping("/delete/{oftenIds}")
    public AjaxResult remove(@PathVariable Long[] oftenIds) {
        return toAjax(sysOftenService.deleteSysOftenByOftenIds(oftenIds));
    }
}
