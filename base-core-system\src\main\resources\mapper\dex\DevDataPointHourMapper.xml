<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.dex.mapper.DevDataPointHourMapper">

    <resultMap type="DevDataPointHour" id="DevDataPointHourResult">
        <result property="pointId" column="point_id"/>
        <result property="sourceValue" column="source_value"/>
        <result property="value" column="value"/>
        <result property="monitorTime" column="monitor_time"/>
        <result property="createTime" column="create_time"/>
        <result property="validFlag" column="valid_flag"/>
        <result property="pointFlag" column="point_flag"/>
        <result property="id" column="id"/>
    </resultMap>

    <sql id="selectDevDataPointHourVo">
        select point_id, source_value, value, monitor_time, create_time, valid_flag, point_flag, id from dev_data_point_hour
    </sql>

    <select id="selectDevDataPointHourList" parameterType="DevDataPointHour" resultMap="DevDataPointHourResult">
        <include refid="selectDevDataPointHourVo"/>
        <where>
            <if test="sourceValue != null ">and source_value = #{sourceValue}</if>
            <if test="value != null ">and value = #{value}</if>
            <if test="validFlag != null  and validFlag != ''">and valid_flag = #{validFlag}</if>
            <if test="pointFlag != null  and pointFlag != ''">and point_flag = #{pointFlag}</if>
        </where>
    </select>

    <select id="selectDevDataPointHourByPointId" parameterType="String" resultMap="DevDataPointHourResult">
        <include refid="selectDevDataPointHourVo"/>
        where point_id = #{pointId}
    </select>

    <select id="getDevDataPointHourList" parameterType="DevDataPointHour" resultType="DevDataPointHour">
        select d.device_id deviceId, d.device_name deviceName, d.pos_x posX, d.pos_y posY, dm."value"
        from dex.dev_data_point_hour dm
        join dex.dev_base_point bp on bp.point_id=dm.point_id
        join device.env_device_point dp on bp.point_id = dp.point_id
        join device.env_device d on dp.device_id=d.device_id
        where d.main_type='station' and bp.point_code = #{factor}
        and to_char(dm.monitor_time, 'YYYY-MM-DD HH24') = #{hour}
        and d.device_id in (select device_id from device.env_device where main_type='station')
    </select>

    <select id="getDevDataPointHourByAvg" parameterType="DevDataPointHour" resultType="DevDataPointHour">
        select dm.monitor_time monitorTime, ROUND(avg(dm.value)) value
        from dex.dev_data_point_hour dm
        join dex.dev_base_point bp on bp.point_id=dm.point_id
        join device.env_device_point dp on bp.point_id = dp.point_id
        join device.env_device d on dp.device_id=d.device_id
        where d.main_type='station' and dm.value > 0 and d.sub_type = #{subType}
        and bp.point_code = #{factor}
        group by dm.monitor_time
        order by dm.monitor_time desc
        limit 1
    </select>

    <insert id="insertDevDataPointHour" parameterType="DevDataPointHour">
        insert into dev_data_point_hour
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointId != null">point_id,</if>
            <if test="sourceValue != null">source_value,</if>
            <if test="value != null">value,</if>
            <if test="monitorTime != null">monitor_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="pointFlag != null">point_flag,</if>
            <if test="id != null">id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointId != null">#{pointId},</if>
            <if test="sourceValue != null">#{sourceValue},</if>
            <if test="value != null">#{value},</if>
            <if test="monitorTime != null">#{monitorTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="pointFlag != null">#{pointFlag},</if>
            <if test="id != null">#{id},</if>
        </trim>
    </insert>

    <update id="updateDevDataPointHour" parameterType="DevDataPointHour">
        update dev_data_point_hour
        <trim prefix="SET" suffixOverrides=",">
            <if test="sourceValue != null">source_value = #{sourceValue},</if>
            <if test="value != null">value = #{value},</if>
            <if test="monitorTime != null">monitor_time = #{monitorTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="pointFlag != null">point_flag = #{pointFlag},</if>
            <if test="id != null">id = #{id},</if>
        </trim>
        where point_id = #{pointId}
    </update>

    <delete id="deleteDevDataPointHourByPointId" parameterType="String">
        delete from dev_data_point_hour where point_id = #{pointId}
    </delete>

    <delete id="deleteDevDataPointHourByPointIds" parameterType="String">
        delete from dev_data_point_hour where point_id in
        <foreach item="pointId" collection="array" open="(" separator="," close=")">
            #{pointId}
        </foreach>
    </delete>
</mapper>