package com.base.web.controller.system;

import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.system.domain.SysBuild;
import com.base.system.service.ISysBuildService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统建筑物控制器
 * 处理与建筑物相关的HTTP请求
 */
@RestController
@RequestMapping("/system/build")
public class SysBuildController extends BaseController {

    @Autowired
    private ISysBuildService sysBuildService;

    /**
     * 获取单个建筑物信息
     *
     * @param buildId 建筑物ID
     * @return 包含建筑物信息的AjaxResult
     */
    @GetMapping("/get")
    public AjaxResult get(@RequestParam String buildId) {
        SysBuild sysBuild = sysBuildService.getById(buildId);
        return success(sysBuild);
    }

    /**
     * 获取建筑物列表
     *
     * @param sysBuild 查询条件
     * @return 包含建筑物列表的AjaxResult
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody SysBuild sysBuild) {
        List<SysBuild> sysBuildList = sysBuildService.selectList(sysBuild);
        return success(sysBuildList);
    }

    /**
     * 添加新建筑物
     *
     * @param sysBuild 新建筑物信息
     * @return 包含新建筑物信息的AjaxResult
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody SysBuild sysBuild) {
        SysBuild addedSysBuild = sysBuildService.add(sysBuild);
        return success(addedSysBuild);
    }

    /**
     * 编辑建筑物信息
     *
     * @param sysBuild 要编辑的建筑物信息
     * @return 包含编辑后建筑物信息的AjaxResult
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody SysBuild sysBuild) {
        SysBuild editedSysBuild = sysBuildService.edit(sysBuild);
        return success(editedSysBuild);
    }

    /**
     * 删除建筑物
     *
     * @param buildId 要删除的建筑物ID
     * @return 删除结果的AjaxResult
     */
    @RequestMapping(value = "/delete", method = {RequestMethod.GET, RequestMethod.DELETE, RequestMethod.POST})
    public AjaxResult delete(@RequestParam String buildId) {
        boolean isDeleted = sysBuildService.deleteById(buildId);
        return isDeleted ? success() : error("删除失败");
    }
}
