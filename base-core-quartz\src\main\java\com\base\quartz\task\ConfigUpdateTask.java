package com.base.quartz.task;

import com.base.common.config.ProductConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

/**
 * 配置文件更新任务
 */
@Slf4j
@Component("configUpdateTask")
public class ConfigUpdateTask {

    @Autowired
    private ProductConfig productConfig;

    /**
     * 远程配置文件的URL地址
     */
    private static final String CONFIG_URL = "http://192.168.1.41/profile/config.secure.json";

    /**
     * 本地配置文件存储路径（相对于应用根目录）
     */
    private static final String LOCAL_PATH = "config/config.secure.json";

    /**
     * 下载并更新配置文件
     */
    public void updateConfig() {
        try {
            RestTemplate restTemplate = new RestTemplate();
            File tempFile = File.createTempFile("config", ".tmp");

            // 使用RestTemplate执行GET请求下载文件
            // 通过ResponseExtractor接口将响应流直接写入临时文件
            restTemplate.execute(CONFIG_URL, HttpMethod.GET, null, clientHttpResponse -> {
                Files.copy(clientHttpResponse.getBody(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                return null;
            });

            // 验证下载的文件是否有效（非空检查）
            if (tempFile.length() > 0) {
                // 创建目标文件对象
                File targetFile = new File(LOCAL_PATH);

                // 创建备份目录
                File backupDir = new File("config/backup");
                if (!backupDir.exists()) {
                    backupDir.mkdirs();
                }

                // 如果目标文件存在，先备份
                if (targetFile.exists()) {
                    String timestamp = String.valueOf(System.currentTimeMillis());
                    File backupFile = new File(backupDir, "config.secure.json." + timestamp);
                    Files.copy(targetFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                }

                // 原子性操作：将临时文件移动到目标位置，替换现有文件
                Files.move(tempFile.toPath(), targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                log.info("配置文件更新成功");

                // 调用ProductConfig的init方法重新初始化配置
                productConfig.init();
            } else {
                // 下载的文件为空时抛出运行时异常
                throw new RuntimeException("下载的文件为空");
            }
        } catch (Exception e) {
            log.error("配置文件更新失败", e);
        }
    }

    public static void main(String[] args) {
        new ConfigUpdateTask().updateConfig();
    }
}
