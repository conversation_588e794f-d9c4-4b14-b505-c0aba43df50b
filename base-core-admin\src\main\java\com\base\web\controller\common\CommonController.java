package com.base.web.controller.common;

import com.base.common.config.BaseConfig;
import com.base.common.constant.Constants;
import com.base.common.core.domain.AjaxResult;
import com.base.common.utils.DateUtils;
import com.base.common.utils.LbsUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.file.FileUploadUtils;
import com.base.common.utils.file.FileUtils;
import com.base.common.utils.file.ImageUtils;
import com.base.framework.config.ServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    private static final String FILE_DELIMETER = ",";

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = BaseConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求（单个）
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = BaseConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求（多个）
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = BaseConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files)
            {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;
                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = BaseConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 上传图片添加水印
     */
    @PostMapping("/uploadFileAddWatermark")
    public AjaxResult uploadFileAddWatermark(MultipartFile file) {
        try {
            // 上传文件路径
            String filePath = BaseConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String substring = StringUtils.substring(BaseConfig.getProfile(), 0, BaseConfig.getProfile().indexOf(Constants.RESOURCE_PREFIX));
            String absoluteFile = substring + fileName;
            // 获取图片详细信息
            Map<String, String> tags = ImageUtils.getImageMetadata(absoluteFile);
            if (StringUtils.isEmpty(tags)) {
                return AjaxResult.error("未能解析出图片信息，请重新上传");
            }
            // 提取图片信息
            log.info("==========> 提取图片信息为：{}", tags);
            // 拍摄日期
            String datetime = tags.get("Date/Time Digitized");
            // GPS信息
            String gpsLongitudeRef = tags.get("GPS Longitude Ref");
            String gpsLongitude = tags.get("GPS Longitude");
            String gpsLatitudeRef = tags.get("GPS Latitude Ref");
            String gpsLatitude = tags.get("GPS Latitude");
            log.info("==========> 拍摄日期：{}，gpsLatitude：{}，gpsLatitudeRef：{}，gpsLongitude：{}，gpsLongitudeRef：{}",
                    datetime, gpsLatitude, gpsLatitudeRef, gpsLongitude, gpsLongitudeRef);
            if (StringUtils.isEmpty(gpsLongitude) || StringUtils.isEmpty(gpsLatitude)) {
                return AjaxResult.error("图片未包含位置坐标，请重新上传");
            }
            Double longitude = LbsUtils.transformPosition(gpsLongitude);
            Double latitude = LbsUtils.transformPosition(gpsLatitude);
            log.info("==========> 转换后的经度：{}，纬度：{}", longitude, latitude);
            // 根据经纬度坐标，通过高德接口获取行政地址
            String address = LbsUtils.getLocationInfo(longitude, latitude);
            log.info("==========> 定位地址：{}", address);
            // 拍摄日期格式：yyyy-MM-dd HH:mm
            if (StringUtils.isNotEmpty(datetime)) {
                Date parseDate = DateUtils.parseDate(datetime, "yyyy:MM:dd HH:mm:ss");
                datetime = DateUtils.formatDateTime(parseDate).substring(0, 16);
            }
            // 当前登录用户名称
            String userName = "";
            if (StringUtils.isNotNull(SecurityUtils.getLoginUser()) && StringUtils.isNotNull(SecurityUtils.getLoginUser().getUser())) {
                userName = SecurityUtils.getLoginUser().getUser().getNickName();
            }
            // 添加水印
            ImageUtils.addWatermark(absoluteFile, longitude, latitude, datetime, userName, address, absoluteFile, 100, 100);

            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        } catch (Exception e) {
            log.error("=====> 上传图片添加水印出错", e);
            return AjaxResult.error("上传图片添加水印出错");
        }
    }

}
