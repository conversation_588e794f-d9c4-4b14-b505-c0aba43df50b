package com.base.web.controller.message;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.message.domain.SysMessageConfig;
import com.base.message.service.ISysMessageConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息通知配置Controller
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Api(tags = "消息通知配置管理")
@RestController
@RequestMapping("/api/sys/message/config")
public class SysMessageConfigController extends BaseController {
    @Autowired
    private ISysMessageConfigService sysMessageConfigService;

    /**
     * 查询消息通知配置列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询消息通知配置列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "消息通知类型，字典配置sys_message_type，全部传空NULL", required = false, dataType = "String", paramType = "query")
    })
    public AjaxResult list(String type) {
        SysMessageConfig sysMessageConfig = new SysMessageConfig();
        sysMessageConfig.setType(type);
        List<SysMessageConfig> list = sysMessageConfigService.selectSysMessageConfigList(sysMessageConfig);
        return AjaxResult.success(list);
    }

    /**
     * 批量更新消息通知配置，添加、修改和删除
     */
    @Log(title = "消息通知配置", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSave")
    @ApiOperation(value = "批量更新消息通知配置，添加、修改和删除", httpMethod = "POST")
    public AjaxResult batchSave(@RequestBody List<SysMessageConfig> sysMessageConfigList) {
        return AjaxResult.success(sysMessageConfigService.batchSave(sysMessageConfigList));
    }


    /**
     * 获取消息通知配置详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取消息通知配置详细信息", httpMethod = "GET")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(sysMessageConfigService.selectSysMessageConfigById(id));
    }

    /**
     * 新增消息通知配置
     */
    @Log(title = "消息通知配置", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增消息通知配置", httpMethod = "POST")
    public AjaxResult add(@RequestBody SysMessageConfig sysMessageConfig) {
        return toAjax(sysMessageConfigService.insertSysMessageConfig(sysMessageConfig));
    }

    /**
     * 修改消息通知配置
     */
    @Log(title = "消息通知配置", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改消息通知配置", httpMethod = "PUT")
    public AjaxResult edit(@RequestBody SysMessageConfig sysMessageConfig) {
        return toAjax(sysMessageConfigService.updateSysMessageConfig(sysMessageConfig));
    }

    /**
     * 删除消息通知配置
     */
    @Log(title = "消息通知配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除消息通知配置", httpMethod = "DELETE")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysMessageConfigService.deleteSysMessageConfigByIds(ids));
    }
}
