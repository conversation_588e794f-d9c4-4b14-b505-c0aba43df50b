package com.base.task.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 任务文件对象 task_file
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@ApiModel("任务文件对象")
public class TaskFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 任务id
     */
    @Excel(name = "任务id")
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 文件名
     */
    @Excel(name = "文件名")
    @ApiModelProperty("文件名")
    private String fileName;

    /**
     * 文件路径
     */
    @Excel(name = "文件路径")
    @ApiModelProperty("文件路径")
    private String filePath;

    /**
     * 文件后缀：jpg png docx mp4
     */
    @Excel(name = "文件后缀：jpg png docx mp4")
    @ApiModelProperty("文件后缀：jpg png docx mp4")
    private String fileSuffix;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @ApiModelProperty(hidden = true)
    private Integer delFlag;

    /**
     * id列表
     */
    private List<Long> ids;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFileSuffix(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    public String getFileSuffix() {
        return fileSuffix;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("taskId", getTaskId())
                .append("fileName", getFileName())
                .append("filePath", getFilePath())
                .append("fileSuffix", getFileSuffix())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
