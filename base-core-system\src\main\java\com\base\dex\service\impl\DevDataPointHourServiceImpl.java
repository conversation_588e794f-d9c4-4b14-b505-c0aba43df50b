package com.base.dex.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.config.BaseConfig;
import com.base.common.constant.Constants;
import com.base.common.core.redis.RedisCache;
import com.base.common.heatmap.HeatPoint;
import com.base.common.heatmap.HeatPointData;
import com.base.common.heatmap.HeatmapIdwUtil;
import com.base.common.utils.DateUtils;
import com.base.common.utils.StringUtils;
import com.base.dex.domain.DevDataPointHour;
import com.base.env.domain.EnvDataPoint;
import com.base.dex.mapper.DevDataPointHourMapper;
import com.base.dex.service.IDevDataPointHourService;
import com.base.env.service.IEnvDataPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 点位小时数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class DevDataPointHourServiceImpl implements IDevDataPointHourService {
    @Autowired
    private DevDataPointHourMapper devDataPointHourMapper;

    @Autowired
    private IEnvDataPointService envDataPointService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询点位小时数据
     *
     * @param pointId 点位小时数据主键
     * @return 点位小时数据
     */
    @Override
    public DevDataPointHour selectDevDataPointHourByPointId(String pointId) {
        return devDataPointHourMapper.selectDevDataPointHourByPointId(pointId);
    }

    /**
     * 查询点位小时数据列表
     *
     * @param devDataPointHour 点位小时数据
     * @return 点位小时数据
     */
    @Override
    public List<DevDataPointHour> selectDevDataPointHourList(DevDataPointHour devDataPointHour) {
        return devDataPointHourMapper.selectDevDataPointHourList(devDataPointHour);
    }

    /**
     * 新增点位小时数据
     *
     * @param devDataPointHour 点位小时数据
     * @return 结果
     */
    @Override
    public int insertDevDataPointHour(DevDataPointHour devDataPointHour) {
        devDataPointHour.setCreateTime(DateUtils.getNowDate());
        return devDataPointHourMapper.insertDevDataPointHour(devDataPointHour);
    }

    /**
     * 修改点位小时数据
     *
     * @param devDataPointHour 点位小时数据
     * @return 结果
     */
    @Override
    public int updateDevDataPointHour(DevDataPointHour devDataPointHour) {
        return devDataPointHourMapper.updateDevDataPointHour(devDataPointHour);
    }

    /**
     * 批量删除点位小时数据
     *
     * @param pointIds 需要删除的点位小时数据主键
     * @return 结果
     */
    @Override
    public int deleteDevDataPointHourByPointIds(String[] pointIds) {
        return devDataPointHourMapper.deleteDevDataPointHourByPointIds(pointIds);
    }

    /**
     * 删除点位小时数据信息
     *
     * @param pointId 点位小时数据主键
     * @return 结果
     */
    @Override
    public int deleteDevDataPointHourByPointId(String pointId) {
        return devDataPointHourMapper.deleteDevDataPointHourByPointId(pointId);
    }

    /**
     * 获取点位小时数据列表
     *
     * @param devDataPointHour 点位小时数据
     * @return 点位小时数据集合
     */
    @Override
    public List<DevDataPointHour> getDevDataPointHourList(DevDataPointHour devDataPointHour) {
        return devDataPointHourMapper.getDevDataPointHourList(devDataPointHour);
    }

    /**
     * 获取企业厂界范围坐标列表
     *
     * @return 厂界范围坐标列表
     */
    @Override
    public List<HeatPoint> getEnvDataPointList() {
        List<HeatPoint> pointList = redisCache.getCacheObject("env:data:point:list");
        if (pointList == null || pointList.size() <= 0) {
            pointList = new ArrayList<>();
            EnvDataPoint envDataPoint = new EnvDataPoint();
            // 类型：1本厂范围线
            envDataPoint.setType(1);
            List<EnvDataPoint> list = envDataPointService.selectEnvDataPointList(envDataPoint);
            for (EnvDataPoint vo : list) {
                // 过滤无坐标的数据
                if (StringUtils.isEmpty(vo.getPosX()) || "-".equals(vo.getPosX()) || StringUtils.isEmpty(vo.getPosY()) || "-".equals(vo.getPosY())) {
                    continue;
                }
                HeatPoint point = new HeatPoint();
                point.setX(Double.parseDouble(vo.getPosX()));
                point.setY(Double.parseDouble(vo.getPosY()));
                pointList.add(point);
            }
            // 放入redis缓存中，1小时过期
            redisCache.setCacheObject("env:data:point:list", pointList, 1, TimeUnit.HOURS);
        }
        return pointList;
    }

    /**
     * 生成热力图图片路径
     *
     * @param hourList   生成图片的小时数据
     * @param pixelWide  像素宽度
     * @param type       监测指标类型：pm2_5、pm10
     * @param date       日期，只能传一天
     * @param filePrefix 生成文件名的前缀，如:hour_，hour_20200712.png
     * @return
     */
    @Override
    public JSONObject getHeatMapImg(List<DevDataPointHour> hourList, int pixelWide, String type, Date date, String filePrefix) {
        if (StringUtils.isEmpty(hourList)) {
            return null;
        }
        JSONObject result = null;
        List<HeatPointData> lstAirData = new ArrayList<>();
        for (DevDataPointHour vo : hourList) {
            // 过滤坐标为空的
            if (StringUtils.isNull(vo.getPosX()) || StringUtils.isNull(vo.getPosY())) {
                continue;
            }
            HeatPointData data = new HeatPointData();
            data.setX(vo.getPosX());
            data.setY(vo.getPosY());
            data.setValue(vo.getValue());
            if (data.getValue() > 0) {
                lstAirData.add(data);
            }
        }
        if (lstAirData != null && lstAirData.size() > 0) {
            // /2020/08/08
            String datePath = File.separator + DateUtils.datePath(date);
            // 路径 /heatmap + / + pm2_5 + /2020/08/08
            String filePath = Constants.HEATMAP_PREFIX + File.separator + type + datePath;
            // 绝对路径：/home/<USER>/code/profile + /heatmap + / + pm2_5 + /2020/08/08
            File file = new File(BaseConfig.getProfile() + filePath);
            // 创建文件夹
            if (!file.exists()) {
                // 如果父路径不存在，会自动先创建路径所需的文件夹
                file.mkdirs();
            }
            String dateKey = DateUtil.format(date, "yyyyMMdd");
            // 路径+文件名：/heatmap\pm2_5\2024/08/21\2024-08-21 00_20200711.png
            String fileName = filePath + File.separator + filePrefix.replace(" ", "_") + dateKey + ".png";
            // redis KEY = heatmap:pm2_5:20200808:fileName
            String heatmapKey = Constants.HEATMAP + ":" + type + ":" + dateKey + ":" + fileName;
            if (redisCache.getCacheObject(heatmapKey) != null) {
                File file1 = new File(BaseConfig.getProfile() + fileName);
                // 如果热力图图片不存在，重新生成
                if (!file1.exists()) {
                    result = HeatmapIdwUtil.idwInterpolationImg(getEnvDataPointList(), lstAirData, pixelWide, type, fileName);
                    redisCache.setCacheObject(heatmapKey, result, 10, TimeUnit.DAYS);
                } else {
                    result = redisCache.getCacheObject(heatmapKey);
                }
            } else {
                result = HeatmapIdwUtil.idwInterpolationImg(getEnvDataPointList(), lstAirData, pixelWide, type, fileName);
                redisCache.setCacheObject(heatmapKey, result, 10, TimeUnit.DAYS);
            }
        }
        return result;
    }

    /**
     * 获取环境质量监测微站-小型空气质量监测站14和厂界监测微站21的最近平均值
     *
     * @param devDataPointHour 点位小时数据
     * @return 环境质量监测微站-小型空气质量监测站14和厂界监测微站21的最近平均值
     */
    @Override
    public DevDataPointHour getDevDataPointHourByAvg(DevDataPointHour devDataPointHour) {
        return devDataPointHourMapper.getDevDataPointHourByAvg(devDataPointHour);
    }
}
