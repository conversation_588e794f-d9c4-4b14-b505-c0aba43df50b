package com.base.system.mapper;

import com.base.system.domain.SysStation;

import java.util.List;

/**
 * 国/省控站Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface SysStationMapper
{
    /**
     * 查询国/省控站
     *
     * @param stationId 国/省控站主键
     * @return 国/省控站
     */
    public SysStation selectSysStationByStationId(Long stationId);

    /**
     * 查询国/省控站列表
     *
     * @param sysStation 国/省控站
     * @return 国/省控站集合
     */
    public List<SysStation> selectSysStationList(SysStation sysStation);

    /**
     * 新增国/省控站
     *
     * @param sysStation 国/省控站
     * @return 结果
     */
    public int insertSysStation(SysStation sysStation);

    /**
     * 修改国/省控站
     *
     * @param sysStation 国/省控站
     * @return 结果
     */
    public int updateSysStation(SysStation sysStation);

    /**
     * 删除国/省控站
     *
     * @param stationId 国/省控站主键
     * @return 结果
     */
    public int deleteSysStationByStationId(Long stationId);

    /**
     * 批量删除国/省控站
     *
     * @param stationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysStationByStationIds(Long[] stationIds);

    void clear();

    /**
     * 批量新增国/省控站
     *
     * @param sysStationList 国/省控站列表
     * @return 结果
     */
    public int batchSysStation(List<SysStation> sysStationList);
}
