package com.base.common.config;

import lombok.Data;
import org.apache.iotdb.isession.util.Version;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * iotDB连接池配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "spring.iot")
public class IotProperties {

    /**
     * 最大连接数
     */
    private Integer maxSize = 1000;

    /**
     * ip
     */
    private String host;

    /**
     * 端口号
     */
    private int port;

    /**
     * 用户名
     */
    private String user;

    /**
     * 密码
     */
    private String password;

    /**
     * 版本
     */
    private Version version = Version.V_1_0;

    /**
     * 超时时间 毫秒
     */
    private int connectionTimeoutInMs;


    /**
     * 数据库路径
     */
    private String dataenv;

}

