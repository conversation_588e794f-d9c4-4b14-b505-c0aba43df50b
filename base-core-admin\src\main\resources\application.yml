# 项目相关配置
base:
  # 名称
  name: base
  # 版本
  version: 6.0.0
  # 版权年份
  copyrightYear: 2023
  # 文件路径 示例（ Windows配置D:/env/uploadPath，Linux配置 /home/<USER>/uploadPath）
  #  profile: D:/env/uploadPath
  profile: profile
  configPath: config
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  # 单点登录类型 空则不使用 cas/oauth2
  SSO: oauth2
  # IP白名单
  whiteList:
    - 127.0.0.1
    - ************

schedule:
  enabled: false

# API服务配置
api-services:
  env-pollu-service: http://************/env-pollu-service
  base-atlanta-service: http://************/base-atlanta-service
  base-dex-service: http://************/base-dex-service

cas:
  host: http://*************:8888
  serverLoginUrl: ${cas.host}/cas/login
  serverValidate: ${cas.host}/cas/serviceValidate
  serviceUrl: http://*************/env-web/#/auth
  encryptKey: 0WqKg429

oauth2:
  host: http://sts.vamachina.com:8082
  serverLoginUrl: ${oauth2.host}/oAuth2/Connect?client_id=${oauth2.clientId}&state=a91c18&response_type=code&redirect_uri=
  serverTokenUrl: ${oauth2.host}/oAuth2/AccessToken?client_id=${oauth2.clientId}&client_secret=${oauth2.clientSecret}&grant_type=authorization_code&code=
  serverUserInfo: ${oauth2.host}/oAuth2/UserInfo?access_token=
  serviceUrl: http://************:30080/env-web/#/auth
  clientId: 9aeb8a92-91dd-1fcc-fbba-0f221149a784
  clientSecret: tjQ3ibjY

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 28080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.base: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 512MB
      # 设置总上传的文件大小
      max-request-size: 1024MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 4320

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.base.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: postgresql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /base-core-service

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

