package com.base.system.service;

import com.base.system.domain.SysBuild;

import java.util.List;

/**
 * 系统建筑物服务接口
 * 定义了建筑物相关的业务逻辑方法
 */
public interface ISysBuildService {
    /**
     * 根据ID获取建筑物信息
     *
     * @param buildId 建筑物ID
     * @return 建筑物信息
     */
    SysBuild getById(String buildId);

    /**
     * 根据条件查询建筑物列表
     *
     * @param sysBuild 查询条件
     * @return 建筑物列表
     */
    List<SysBuild> selectList(SysBuild sysBuild);

    /**
     * 添加新建筑物
     *
     * @param sysBuild 新建筑物信息
     * @return 添加后的建筑物信息
     */
    SysBuild add(SysBuild sysBuild);

    /**
     * 编辑建筑物信息
     *
     * @param sysBuild 要编辑的建筑物信息
     * @return 编辑后的建筑物信息
     */
    SysBuild edit(SysBuild sysBuild);

    /**
     * 根据ID删除建筑物
     *
     * @param buildId 要删除的建筑物ID
     * @return 是否删除成功
     */
    boolean deleteById(String buildId);
}