package com.base.dex.mapper;

import com.base.dex.domain.DevBasePoint;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备点Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Repository
public interface DevBasePointMapper
{
    /**
     * 查询设备点
     *
     * @param pointId 设备点主键
     * @return 设备点
     */
    public DevBasePoint selectDevBasePointByPointId(String pointId);

    /**
     * 查询设备点列表
     *
     * @param devBasePoint 设备点
     * @return 设备点集合
     */
    public List<DevBasePoint> selectDevBasePointList(DevBasePoint devBasePoint);

    /**
     * 新增设备点
     *
     * @param devBasePoint 设备点
     * @return 结果
     */
    public int insertDevBasePoint(DevBasePoint devBasePoint);

    /**
     * 修改设备点
     *
     * @param devBasePoint 设备点
     * @return 结果
     */
    public int updateDevBasePoint(DevBasePoint devBasePoint);

    /**
     * 删除设备点
     *
     * @param pointId 设备点主键
     * @return 结果
     */
    public int deleteDevBasePointByPointId(String pointId);

    /**
     * 批量删除设备点
     *
     * @param pointIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDevBasePointByPointIds(String[] pointIds);

    /**
     * 批量新增设备点
     *
     * @param devBasePointList 设备点列表
     * @return 结果
     */
    public int batchDevBasePoint(List<DevBasePoint> devBasePointList);
}
