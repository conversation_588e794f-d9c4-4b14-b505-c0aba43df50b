package com.base.system.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.utils.DateUtils;
import com.base.common.utils.http.HttpUtils;
import com.base.system.domain.SysStation;
import com.base.system.mapper.SysStationMapper;
import com.base.system.service.ISysStationService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 国/省控站Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class SysStationServiceImpl implements ISysStationService
{
    @Autowired
    private SysStationMapper sysStationMapper;

    /**
     * 查询国/省控站
     *
     * @param stationId 国/省控站主键
     * @return 国/省控站
     */
    @Override
    public SysStation selectSysStationByStationId(Long stationId)
    {
        return sysStationMapper.selectSysStationByStationId(stationId);
    }

    /**
     * 查询国/省控站列表
     *
     * @param sysStation 国/省控站
     * @return 国/省控站
     */
    @Override
    public List<SysStation> selectSysStationList(SysStation sysStation)
    {
        return sysStationMapper.selectSysStationList(sysStation);
    }

    /**
     * 新增国/省控站
     *
     * @param sysStation 国/省控站
     * @return 结果
     */
    @Override
    public int insertSysStation(SysStation sysStation)
    {
        sysStation.setCreateTime(DateUtils.getNowDate());
        return sysStationMapper.insertSysStation(sysStation);
    }

    /**
     * 修改国/省控站
     *
     * @param sysStation 国/省控站
     * @return 结果
     */
    @Override
    public int updateSysStation(SysStation sysStation)
    {
        sysStation.setUpdateTime(DateUtils.getNowDate());
        return sysStationMapper.updateSysStation(sysStation);
    }

    /**
     * 批量删除国/省控站
     *
     * @param stationIds 需要删除的国/省控站主键
     * @return 结果
     */
    @Override
    public int deleteSysStationByStationIds(Long[] stationIds)
    {
        return sysStationMapper.deleteSysStationByStationIds(stationIds);
    }

    /**
     * 删除国/省控站信息
     *
     * @param stationId 国/省控站主键
     * @return 结果
     */
    @Override
    public int deleteSysStationByStationId(Long stationId)
    {
        return sysStationMapper.deleteSysStationByStationId(stationId);
    }

    public void clearStation(){
        sysStationMapper.clear();
    }

    /*
     * 当企业位置发生改变, 更新国省控站点
     * @param sysConfig 系统配置参数
     * @return 操作结果
     */
    @SneakyThrows
    @Async("threadPoolTaskExecutor")
    @Override
    public void updateStationsWhenEnterpriseLocationChanges(String cityName) {
        // 构造请求 URL
        String encodedCityName = java.net.URLEncoder.encode(cityName, "UTF-8");
        String url = "http://wx.e9st.cn/airpoint/get/city?cityname=" + encodedCityName;

        // 发送 HTTP 请求
        String responseBody = HttpUtils.sendGet(url);
        // 解析响应数据
        List<JSONObject> airData = JSONArray.parseArray(responseBody, JSONObject.class);
        // 清空现有站点
        this.clearStation();

        // 构建站点列表
        List<SysStation> stationList = new ArrayList<>();
        for (JSONObject airItem : airData) {
            SysStation station = new SysStation();
            station.setStationName(airItem.getString("pointName"));
            station.setStationLocation(cityName);
            station.setStationType("国控站点");
            station.setStatus(0L);
            station.setDelFlag(0L);
            stationList.add(station);
        }

        // 批量插入站点数据
        if (!stationList.isEmpty()) {
            sysStationMapper.batchSysStation(stationList);
        }

    }
}
