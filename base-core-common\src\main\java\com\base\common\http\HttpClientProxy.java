package com.base.common.http;

import com.alibaba.fastjson2.JSON;
import com.base.common.annotation.EnableApiClient;
import com.base.common.annotation.HttpClient;
import com.base.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP客户端代理类，用于处理带有@HttpClient注解的接口方法调用
 * 该类实现了动态代理，可以拦截接口方法的调用并转换为HTTP请求
 *
 * <AUTHOR>
 * @date 2024/10/16
 */
public class HttpClientProxy implements InvocationHandler {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientProxy.class);

    private final String serviceName;
    private final RestTemplate restTemplate;
    private final Environment environment;

    /**
     * 构造函数
     *
     * @param interfaceType 接口类型
     * @param environment   Spring环境，用于获取配置
     */
    public HttpClientProxy(Class<?> interfaceType, Environment environment) {
        EnableApiClient annotation = interfaceType.getAnnotation(EnableApiClient.class);
        this.serviceName = (annotation != null) ? annotation.serviceName().getServiceName() : "";
        this.restTemplate = new RestTemplate();
        this.environment = environment;
        logger.info("初始化HTTP客户端代理，接口: {}，服务名: {}", interfaceType.getName(), this.serviceName);
    }

    /**
     * 代理方法调用的核心逻辑
     *
     * @param proxy  代理对象
     * @param method 被调用的方法
     * @param args   方法参数
     * @return 方法调用结果
     * @throws Throwable 可能抛出的异常
     */
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 处理 Object 类的方法
        if (method.getDeclaringClass() == Object.class) {
            return method.invoke(this, args);
        }

        long startTime = System.currentTimeMillis();

        // 检查方法是否有@HttpClient注解
        HttpClient httpClient = method.getAnnotation(HttpClient.class);
        if (httpClient == null) {
            logger.error("方法 {} 没有使用 @HttpClient 注解", method.getName());
            throw new UnsupportedOperationException("方法未使用 @HttpClient 注解");
        }

        // 获取baseUrl
        String baseUrl = environment.getProperty("api-services." + serviceName + ".base-url");
        if (baseUrl == null) {
            logger.error("未找到服务 {} 的baseUrl配置", serviceName);
            throw new IllegalStateException("未找到服务的baseUrl配置");
        }

        // 构建请求URL和方法
        String path = httpClient.path();
        String url = baseUrl + (baseUrl.endsWith("/") || path.startsWith("/") ? "" : "/") + path;
        String requestType = httpClient.method().name();

//        logger.info("调用方法: {}，URL: {}，HTTP方法: {}", method.getName(), url, requestType);

        Object requestBody = null;
        Map<String, Object> uriVariables = new HashMap<>();

        // 解析方法参数
        Parameter[] parameters = method.getParameters();

        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].isAnnotationPresent(RequestBody.class)) {
                requestBody = args[i];
                logger.debug("发现 @RequestBody 参数: {}", args[i]);
            } else if (parameters[i].isAnnotationPresent(RequestParam.class)) {
                // 添加空值检查
                if (args[i] != null) {
                    RequestParam requestParam = parameters[i].getAnnotation(RequestParam.class);
                    String paramName = requestParam.value().isEmpty() ? parameters[i].getName() : requestParam.value();
                    uriVariables.put(paramName, args[i]);
                }
//                logger.debug("添加 @RequestParam 变量: {}={}", paramName, args[i]);
            }
        }

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> entry : uriVariables.entrySet()) {
            builder.queryParam(entry.getKey(), entry.getValue());
        }
        String urlWithParams = builder.build().encode().toUriString();

        // 获取上游请求中的token
        String token = getTokenFromUpstreamRequest();

        // 准备请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        if (token != null && !token.isEmpty()) {
            headers.set("Authorization", token);
        }

        ResponseEntity<?> response;
        if ("GET".equalsIgnoreCase(requestType)) {
            // 处理GET请求
            logger.debug("发送GET请求到: {}", urlWithParams);
            if (method.getReturnType() == void.class && containsHttpServletResponse(method.getParameters())) {
                HttpEntity<?> entity = new HttpEntity<>(headers);
                // 处理文件下载
                ResponseEntity<Resource> resourceResponse = restTemplate.exchange(urlWithParams, HttpMethod.GET, entity, Resource.class);
                handleFileDownload(resourceResponse, getHttpServletResponse(args));
                return null;
            } else {
                // 使用HttpUtils发送GET请求
                String responseBody = HttpUtils.sendGet(urlWithParams, token);
                response = new ResponseEntity<>(responseBody, HttpStatus.OK);
            }
        } else if ("POST".equalsIgnoreCase(requestType)) {
            // 处理POST请求
            HttpEntity<?> entity;
            if (containsMultipartFile(method.getParameters())) {
                // 处理文件上传
                MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
                for (int i = 0; i < parameters.length; i++) {
                    if (parameters[i].getType() == MultipartFile.class) {
                        MultipartFile file = (MultipartFile) args[i];
                        body.add("file", new org.springframework.core.io.ByteArrayResource(file.getBytes()) {
                            @Override
                            public String getFilename() {
                                return file.getOriginalFilename();
                            }
                        });
                    } else {
                        body.add(parameters[i].getName(), args[i]);
                    }
                }
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                entity = new HttpEntity<>(body, headers);
                logger.debug("发送文件上传POST请求到: {}", urlWithParams);
            } else if (requestBody != null) {
                // 处理JSON请求体
                String jsonBody = JSON.toJSONString(requestBody);
                entity = new HttpEntity<>(jsonBody, headers);
                logger.debug("发送POST请求到: {}，JSON体: {}", urlWithParams, jsonBody);
            } else {
                // 处理表单数据
                MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
                map.setAll(uriVariables);
                entity = new HttpEntity<>(map, headers);
                logger.debug("发送POST请求到: {}，表单数据: {}", urlWithParams, map);
            }

            response = restTemplate.exchange(urlWithParams, HttpMethod.POST, entity, String.class);
        } else {
            logger.error("不支持的HTTP方法: {}", requestType);
            throw new UnsupportedOperationException("不支持的HTTP方法: " + requestType);
        }

        logger.info("收到响应，状态码: {}", response.getStatusCode());
//        logger.debug("响应体: {}", response.getBody());

        // 返回结果处理
        Type returnType = method.getGenericReturnType();
        Object result = JSON.parseObject((String) response.getBody(), returnType);
        logger.debug("解析响应为类型: {}", returnType.getTypeName());

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        logger.info("接口 {} 请求耗时: {} 毫秒", method.getName(), duration);

        return result;
    }

    /**
     * 从上游请求中获取token
     * 这个方法尝试从当前的HTTP请求中提取Authorization头
     *
     * @return token字符串，如果没有找到则返回null
     */
    private String getTokenFromUpstreamRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String token = request.getHeader("Authorization");
            logger.debug("api_token: {}", token);
            return token;
        }
        logger.warn("无法从上游请求中获取token");
        return null;
    }

    /**
     * 检查方法参数中是否包含HttpServletResponse
     *
     * @param parameters 方法参数数组
     * @return 如果包含HttpServletResponse返回true，否则返回false
     */
    private boolean containsHttpServletResponse(Parameter[] parameters) {
        for (Parameter parameter : parameters) {
            if (parameter.getType() == HttpServletResponse.class) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从方法参数中获取HttpServletResponse对象
     *
     * @param args 方法参数
     * @return HttpServletResponse对象，如果不存在则返回null
     */
    private HttpServletResponse getHttpServletResponse(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof HttpServletResponse) {
                return (HttpServletResponse) arg;
            }
        }
        return null;
    }

    /**
     * 处理文件下载
     *
     * @param response        包含文件资源的ResponseEntity
     * @param servletResponse 用于写入文件内容的HttpServletResponse
     * @throws Exception 如果下载过程中发生错误
     */
    private void handleFileDownload(ResponseEntity<Resource> response, HttpServletResponse servletResponse) throws Exception {
        if (response.getBody() == null) {
            throw new Exception("No file content received");
        }

        Resource resource = response.getBody();
        String filename = resource.getFilename();
        if (filename == null) {
            filename = "downloaded_file";
        }

        servletResponse.setContentType(response.getHeaders().getContentType().toString());
        servletResponse.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"");

        try (InputStream inputStream = resource.getInputStream();
             OutputStream outputStream = servletResponse.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    /**
     * 检查方法参数中是否包含MultipartFile
     *
     * @param parameters 方法参数数组
     * @return 如果包含MultipartFile返回true，否则返回false
     */
    private boolean containsMultipartFile(Parameter[] parameters) {
        for (Parameter parameter : parameters) {
            if (parameter.getType() == MultipartFile.class) {
                return true;
            }
        }
        return false;
    }
}
