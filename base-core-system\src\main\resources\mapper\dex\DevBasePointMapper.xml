<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.dex.mapper.DevBasePointMapper">

    <resultMap type="DevBasePoint" id="DevBasePointResult">
        <result property="pointId"    column="point_id"    />
        <result property="pointCode"    column="point_code"    />
        <result property="pointCodeCn"    column="point_code_cn"    />
        <result property="pointDataType"    column="point_data_type"    />
        <result property="pointRdType"    column="point_rd_type"    />
        <result property="pointAddress"    column="point_address"    />
        <result property="pointUnit"    column="point_unit"    />
        <result property="remark"    column="remark"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="deviceId"    column="device_id"    />
        <result property="pointFormula"    column="point_formula"    />
        <result property="isVirtual"    column="is_virtual"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="pointFormulaDemo"    column="point_formula_demo"    />
        <result property="readSequence"    column="read_sequence"    />
        <result property="readPosition"    column="read_position"    />
        <result property="viewFormula"    column="view_formula"    />
    </resultMap>

    <sql id="selectDevBasePointVo">
        select point_id, point_code, point_code_cn, point_data_type, point_rd_type, point_address, point_unit, remark, create_date, update_date, device_id, point_formula, is_virtual, del_flag, point_formula_demo, read_sequence, read_position, view_formula from dex.dev_base_point
    </sql>

    <select id="selectDevBasePointList" parameterType="DevBasePoint" resultMap="DevBasePointResult">
        <include refid="selectDevBasePointVo"/>
        <where>
            del_flag = '0'
            <if test="pointCode != null  and pointCode != ''"> and point_code = #{pointCode}</if>
            <if test="pointCodeCn != null  and pointCodeCn != ''"> and point_code_cn = #{pointCodeCn}</if>
            <if test="pointDataType != null  and pointDataType != ''"> and point_data_type = #{pointDataType}</if>
            <if test="pointRdType != null  and pointRdType != ''"> and point_rd_type = #{pointRdType}</if>
            <if test="pointAddress != null  and pointAddress != ''"> and point_address = #{pointAddress}</if>
            <if test="pointUnit != null  and pointUnit != ''"> and point_unit = #{pointUnit}</if>
            <if test="createDate != null  and createDate != ''"> and create_date = #{createDate}</if>
            <if test="updateDate != null  and updateDate != ''"> and update_date = #{updateDate}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="pointFormula != null  and pointFormula != ''"> and point_formula = #{pointFormula}</if>
            <if test="isVirtual != null  and isVirtual != ''"> and is_virtual = #{isVirtual}</if>
            <if test="pointFormulaDemo != null  and pointFormulaDemo != ''"> and point_formula_demo = #{pointFormulaDemo}</if>
            <if test="readSequence != null  and readSequence != ''"> and read_sequence = #{readSequence}</if>
            <if test="readPosition != null  and readPosition != ''"> and read_position = #{readPosition}</if>
            <if test="viewFormula != null  and viewFormula != ''"> and view_formula = #{viewFormula}</if>
        </where>
    </select>

    <select id="selectDevBasePointByPointId" parameterType="String" resultMap="DevBasePointResult">
        <include refid="selectDevBasePointVo"/>
        where point_id = #{pointId}
    </select>

    <insert id="insertDevBasePoint" parameterType="DevBasePoint">
        insert into dex.dev_base_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointId != null">point_id,</if>
            <if test="pointCode != null">point_code,</if>
            <if test="pointCodeCn != null">point_code_cn,</if>
            <if test="pointDataType != null">point_data_type,</if>
            <if test="pointRdType != null">point_rd_type,</if>
            <if test="pointAddress != null">point_address,</if>
            <if test="pointUnit != null">point_unit,</if>
            <if test="remark != null">remark,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="pointFormula != null">point_formula,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="pointFormulaDemo != null">point_formula_demo,</if>
            <if test="readSequence != null">read_sequence,</if>
            <if test="readPosition != null">read_position,</if>
            <if test="viewFormula != null">view_formula,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointId != null">#{pointId},</if>
            <if test="pointCode != null">#{pointCode},</if>
            <if test="pointCodeCn != null">#{pointCodeCn},</if>
            <if test="pointDataType != null">#{pointDataType},</if>
            <if test="pointRdType != null">#{pointRdType},</if>
            <if test="pointAddress != null">#{pointAddress},</if>
            <if test="pointUnit != null">#{pointUnit},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="pointFormula != null">#{pointFormula},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="pointFormulaDemo != null">#{pointFormulaDemo},</if>
            <if test="readSequence != null">#{readSequence},</if>
            <if test="readPosition != null">#{readPosition},</if>
            <if test="viewFormula != null">#{viewFormula},</if>
         </trim>
    </insert>

    <update id="updateDevBasePoint" parameterType="DevBasePoint">
        update dex.dev_base_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="pointCode != null">point_code = #{pointCode},</if>
            <if test="pointCodeCn != null">point_code_cn = #{pointCodeCn},</if>
            <if test="pointDataType != null">point_data_type = #{pointDataType},</if>
            <if test="pointRdType != null">point_rd_type = #{pointRdType},</if>
            <if test="pointAddress != null">point_address = #{pointAddress},</if>
            <if test="pointUnit != null">point_unit = #{pointUnit},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="pointFormula != null">point_formula = #{pointFormula},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="pointFormulaDemo != null">point_formula_demo = #{pointFormulaDemo},</if>
            <if test="readSequence != null">read_sequence = #{readSequence},</if>
            <if test="readPosition != null">read_position = #{readPosition},</if>
            <if test="viewFormula != null">view_formula = #{viewFormula},</if>
        </trim>
        where point_id = #{pointId}
    </update>

    <delete id="deleteDevBasePointByPointId" parameterType="String">
        delete from dex.dev_base_point where point_id = #{pointId}
    </delete>

    <delete id="deleteDevBasePointByPointIds" parameterType="String">
        delete from dex.dev_base_point where point_id in
        <foreach item="pointId" collection="array" open="(" separator="," close=")">
            #{pointId}
        </foreach>
    </delete>

    <insert id="batchDevBasePoint">
        insert into dex.dev_base_point( point_code, point_code_cn, point_data_type, point_rd_type, point_address, point_unit, remark, create_date, update_date, device_id, point_formula, is_virtual, del_flag, point_formula_demo, read_sequence, read_position, view_formula) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.pointCode}, #{item.pointCodeCn}, #{item.pointDataType}, #{item.pointRdType}, #{item.pointAddress}, #{item.pointUnit}, #{item.remark}, #{item.createDate}, #{item.updateDate}, #{item.deviceId}, #{item.pointFormula}, #{item.isVirtual}, #{item.delFlag}, #{item.pointFormulaDemo}, #{item.readSequence}, #{item.readPosition}, #{item.viewFormula})
        </foreach>
    </insert>
</mapper>
