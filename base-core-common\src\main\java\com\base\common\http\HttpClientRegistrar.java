package com.base.common.http;

import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/10/16
 */
public class HttpClientRegistrar implements ImportBeanDefinitionRegistrar {

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        InterfaceScanner scanner = new InterfaceScanner();

        // 扫描指定包路径下的接口类
        Set<Class<?>> interfaces = scanner.findCandidateInterfaces("com.base.remote");

        interfaces.forEach(clazz -> {
            // 打印找到的接口
//            System.out.println("Found interface: " + clazz.getName());

            // 创建代理类的 BeanDefinition
            BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(ProxyFactoryBean.class);
            beanDefinitionBuilder.addConstructorArgValue(clazz);

            // 注册 BeanDefinition
            registry.registerBeanDefinition(clazz.getSimpleName(), beanDefinitionBuilder.getBeanDefinition());
        });
    }


}
