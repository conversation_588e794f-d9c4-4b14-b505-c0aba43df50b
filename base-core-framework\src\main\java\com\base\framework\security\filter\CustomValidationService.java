package com.base.framework.security.filter;

import com.base.common.config.BaseConfig;
import com.base.common.config.ProductConfig;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.core.domain.model.LoginUser;
import com.base.common.exception.ServiceException;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.ServletUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.ip.IpUtils;
import com.base.framework.web.service.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CustomValidationService {

    @Resource
    TokenService tokenService;
    @Autowired
    private ProductConfig productConfig;

    /**
     * 校验是否为内部应用请求, 内部应用准许放行
     *
     * @return
     */
    public boolean validateInIp(HttpServletRequest request) {
        if (productConfig.isValid()) {
            throw new ServiceException("配置文件被篡改, 请联系管理员");
        }
        String errMsg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
//        request = ServletUtils.getRequest();
        LoginUser loginUser = tokenService.getLoginUser(request);
        Authentication authentication = SecurityUtils.getAuthentication();
        if (StringUtils.isNotNull(authentication) && ! StringUtils.equals("anonymousUser", authentication.getPrincipal().toString())){
            return true;
        }
        else if (StringUtils.isNotNull(loginUser)) {
            // 内部应用携带token则使用token
            tokenService.verifyToken(loginUser);
        } else {
            // 校验是否为内部IP
            if (BaseConfig.getWhiteList().contains(IpUtils.getIpAddr())) {
                // 如果是内部IP,则放行并授予最高权限
                loginUser = new LoginUser();
                loginUser.setUserId(1L);
                SysUser sysUser = new SysUser();
                sysUser.setUserId(1L);
                // 服务名称
                String source = ServletUtils.getHeaderParam("source");
                if (StringUtils.isBlank(source)) {
//                    ServletUtils.renderString(ServletUtils.getResponse(), JSON.toJSONString(AjaxResult.error(HttpStatus.UNAUTHORIZED, errMsg)));
                    return false;
                }
                sysUser.setUserName(source);
                loginUser.setUser(sysUser);
                loginUser.setPermissions(Stream.of("*:*:*").collect(Collectors.toSet()));
                tokenService.setUserAgent(loginUser);
            } else {
//                ServletUtils.renderString(ServletUtils.getResponse(), JSON.toJSONString(AjaxResult.error(HttpStatus.UNAUTHORIZED, errMsg)));
                return false;
            }
        }
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
        authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        return true;
    }
}
