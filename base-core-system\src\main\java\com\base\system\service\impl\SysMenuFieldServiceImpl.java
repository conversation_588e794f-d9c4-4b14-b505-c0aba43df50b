package com.base.system.service.impl;

import com.base.common.annotation.RedisCacheAnno;
import com.base.common.core.redis.RedisCache;
import com.base.common.utils.StringUtils;
import com.base.common.utils.bean.BeanUtils;
import com.base.system.domain.SysMenuField;
import com.base.system.mapper.SysMenuFieldMapper;
import com.base.system.service.ISysMenuFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单-动态字段 关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
public class SysMenuFieldServiceImpl implements ISysMenuFieldService
{
    @Autowired
    private SysMenuFieldMapper sysMenuFieldMapper;

    @Autowired
    RedisCache redisCache;

    public static final String SYS_MENU_FIELD_CACHE_KEY = "SysMenuFieldCache:";

    /**
     * 查询菜单-动态字段 关联关系
     *
     * @param joinId 菜单-动态字段 关联关系主键
     * @return 菜单-动态字段 关联关系
     */
    @Override
    public SysMenuField selectSysMenuFieldByJoinId(Long joinId)
    {
        return sysMenuFieldMapper.selectSysMenuFieldByJoinId(joinId);
    }

    /**
     * 查询菜单-动态字段 关联关系列表
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 菜单-动态字段 关联关系
     */
    @Override
    public List<SysMenuField> selectSysMenuFieldList(SysMenuField sysMenuField)
    {
        return sysMenuFieldMapper.selectSysMenuFieldList(sysMenuField);
    }

    /**
     * 新增菜单-动态字段 关联关系
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 结果
     */
    @Override
    public int insertSysMenuField(SysMenuField sysMenuField)
    {
        int res = sysMenuFieldMapper.insertSysMenuField(sysMenuField);
        this.clear(sysMenuField.getMenuId());
        return res;
    }

    @Override
    public int batchSysMenuField(List<SysMenuField> menuFieldList){
        if (menuFieldList == null || menuFieldList.isEmpty()) {
            return 0;
        }

        String fieldKey = menuFieldList.get(0).getFieldKey();
        Long menuId = menuFieldList.get(0).getMenuId();

        List<SysMenuField> dbList = new ArrayList<>();
        for (SysMenuField item : menuFieldList) {
            SysMenuField dbItem = new SysMenuField();
            BeanUtils.copyProperties(dbItem, item); // 浅拷贝，如需深拷贝可自定义复制逻辑
            dbItem.setJoinId(null);
            dbList.add(dbItem);
        }

        // 删除旧数据
        this.deleteByMenuIdAndFieldKey(menuId, fieldKey);

        // 插入新数据
        int res = sysMenuFieldMapper.batchSysMenuField(dbList);

        for (Long id : dbList.stream().map(SysMenuField::getMenuId).collect(Collectors.toSet())) {
            this.clear(id);
        }

        return res;
    }

    public int deleteByMenuIdAndFieldKey(Long menuId, String fieldKey){
        int res = sysMenuFieldMapper.deleteByMenuIdAndFieldKey(menuId, fieldKey);
        return res;
    }

    /**
     * 修改菜单-动态字段 关联关系
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 结果
     */
    @Override
    public int updateSysMenuField(SysMenuField sysMenuField)
    {
        int res = sysMenuFieldMapper.updateSysMenuField(sysMenuField);
        this.clear(sysMenuField.getMenuId());
        return res;
    }

    /**
     * 批量删除菜单-动态字段 关联关系
     *
     * @param joinIds 需要删除的菜单-动态字段 关联关系主键
     * @return 结果
     */
    @Override
    public int deleteSysMenuFieldByJoinIds(Long[] joinIds)
    {
        int res = sysMenuFieldMapper.deleteSysMenuFieldByJoinIds(joinIds);

        return res;
    }

    /**
     * 删除菜单-动态字段 关联关系信息
     *
     * @param joinId 菜单-动态字段 关联关系主键
     * @return 结果
     */
    @Override
    public int deleteSysMenuFieldByJoinId(Long joinId)
    {
        Long menuId = this.selectSysMenuFieldByJoinId(joinId).getMenuId();
        int res = sysMenuFieldMapper.deleteSysMenuFieldByJoinId(joinId);
        if (res > 0){
            this.clear(menuId);
        }
        return res;
    }

    @Override
    public List<SysMenuField> selectSysMenuFieldList(Long menuId, String fieldKey, boolean queryAll) {
        return this.selectSysMenuFieldList(menuId, Collections.singletonList(fieldKey), queryAll);
    }

    @Override
    public List<SysMenuField> selectSysMenuFieldList(Long menuId, List<String> fieldKey, boolean queryAll) {
        return Collections.emptyList();
    }

    @Override
    public List<SysMenuField> selectSysMenuFieldList(Long menuId, String fieldKey) {
        return this.selectSysMenuFieldList(menuId, Collections.singletonList(fieldKey), false);
    }

    @Override
    public List<SysMenuField> selectSysMenuFieldList(Long menuId, List<String> fieldKey) {
        return this.selectSysMenuFieldList(menuId, fieldKey, false);
    }

    @Override
    @RedisCacheAnno(key = SYS_MENU_FIELD_CACHE_KEY + "${menuId}", expire = 0)
    public List<SysMenuField> selectByMenuId(Long menuId) {
        return selectSysMenuFieldList(new SysMenuField().setMenuId(menuId));
    }


    @Override
    public void clear(Long menuId){
        if (StringUtils.isNotNull(menuId)){
            redisCache.deleteObject(SYS_MENU_FIELD_CACHE_KEY + menuId);
        }
    }
}
