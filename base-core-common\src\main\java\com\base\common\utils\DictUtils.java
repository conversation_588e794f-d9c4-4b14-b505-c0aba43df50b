package com.base.common.utils;

import com.alibaba.fastjson2.JSONArray;
import com.base.common.constant.CacheConstants;
import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.domain.entity.SysSceneDict;
import com.base.common.core.redis.RedisCache;
import com.base.common.utils.spring.SpringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 字典工具类
 *
 * <AUTHOR>
 */
public class DictUtils {
    /**
     * 分隔符
     */
    public static final String SEPARATOR = ",";

    /**
     * 设置字典缓存
     *
     * @param key       参数键
     * @param dictDatas 字典数据列表
     */
    public static void setDictCache(String key, List<SysDictData> dictDatas) {
        SpringUtils.getBean(RedisCache.class).setCacheObject(getCacheKey(key), dictDatas);
    }

    public static void setSceneDictCache(String scene, List<SysSceneDict> dictDatas) {
        SpringUtils.getBean(RedisCache.class).setCacheObject(getSceneCacheKey(scene), dictDatas);
    }

    /**
     * 获取字典缓存
     *
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static List<SysDictData> getDictCache(String key) {
        JSONArray arrayCache = SpringUtils.getBean(RedisCache.class).getCacheObject(getCacheKey(key));
        if (StringUtils.isNotNull(arrayCache)) {
            return arrayCache.toList(SysDictData.class);
        }
        return null;
    }

    public static Map<String, String> getDictLabelValueMap(String key) {
        List<SysDictData> dictCache = DictUtils.getDictCache(key);
        if (StringUtils.isNull(dictCache)) {
            return new HashMap<>();
        }
        return dictCache.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (t1, t2) -> t2));
    }

    public static Map<String, String> getDictValueLabelMap(String key) {
        List<SysDictData> dictCache = DictUtils.getDictCache(key);
        if (StringUtils.isNull(dictCache)) {
            return new HashMap<>();
        }
        return dictCache.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (t1, t2) -> t2));
    }

    public static List<SysSceneDict> getSceneDictCache(String scene) {
        JSONArray arrayCache = SpringUtils.getBean(RedisCache.class).getCacheObject(getSceneCacheKey(scene));
        if (StringUtils.isNotNull(arrayCache)) {
            return arrayCache.toList(SysSceneDict.class);
        }
        return null;
    }

    /**
     * 根据字典类型和字典值获取字典标签
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    public static String getDictLabel(String dictType, String dictValue) {
        return getDictLabel(dictType, dictValue, SEPARATOR);
    }

    /**
     * 根据字典类型和字典标签获取字典值
     *
     * @param dictType  字典类型
     * @param dictLabel 字典标签
     * @return 字典值
     */
    public static String getDictValue(String dictType, String dictLabel) {
        return getDictValue(dictType, dictLabel, SEPARATOR);
    }

    /**
     * 根据字典类型和字典值获取字典标签
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @param separator 分隔符
     * @return 字典标签
     */
    public static String getDictLabel(String dictType, String dictValue, String separator) {
        StringBuilder propertyString = new StringBuilder();
        List<SysDictData> datas = getDictCache(dictType);

        if (StringUtils.isNotNull(datas)) {
            if (StringUtils.containsAny(separator, dictValue)) {
                for (SysDictData dict : datas) {
                    for (String value : dictValue.split(separator)) {
                        if (value.equals(dict.getDictValue())) {
                            propertyString.append(dict.getDictLabel()).append(separator);
                            break;
                        }
                    }
                }
            } else {
                for (SysDictData dict : datas) {
                    if (dictValue.equals(dict.getDictValue())) {
                        return dict.getDictLabel();
                    }
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 根据字典类型和字典标签获取字典值
     *
     * @param dictType  字典类型
     * @param dictLabel 字典标签
     * @param separator 分隔符
     * @return 字典值
     */
    public static String getDictValue(String dictType, String dictLabel, String separator) {
        StringBuilder propertyString = new StringBuilder();
        List<SysDictData> datas = getDictCache(dictType);

        if (StringUtils.containsAny(separator, dictLabel) && StringUtils.isNotEmpty(datas)) {
            for (SysDictData dict : datas) {
                for (String label : dictLabel.split(separator)) {
                    if (label.equals(dict.getDictLabel())) {
                        propertyString.append(dict.getDictValue()).append(separator);
                        break;
                    }
                }
            }
        } else {
            for (SysDictData dict : datas) {
                if (dictLabel.equals(dict.getDictLabel())) {
                    return dict.getDictValue();
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 删除指定字典缓存
     *
     * @param key 字典键
     */
    public static void removeDictCache(String key) {
        SpringUtils.getBean(RedisCache.class).deleteObject(getCacheKey(key));
    }

    /**
     * 清空字典缓存
     */
    public static void clearDictCache() {
        Collection<String> keys = SpringUtils.getBean(RedisCache.class).keys(CacheConstants.SYS_DICT_KEY + "*");
        Collection<String> pyKeys = SpringUtils.getBean(RedisCache.class).keys("flask_cache:*");
        Collection<String> cacheKeys = SpringUtils.getBean(RedisCache.class).keys(CacheConstants.REDIS_CACHE_PREFIX + "SysSceneDict:*");
        keys.addAll(pyKeys);
        keys.addAll(cacheKeys);
        SpringUtils.getBean(RedisCache.class).deleteObject(keys);
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey) {
        return CacheConstants.SYS_DICT_KEY + configKey;
    }

    public static String getSceneCacheKey(String scene) {
        return CacheConstants.SYS_SCENE_DICT_KEY + scene;
    }

    public static List<SysDictData> selectDictData(String dictType, String scene, String status) {
        return selectDictData(Collections.singletonList(dictType), StringUtils.isNotBlank(scene) ? Collections.singletonList(scene) : new ArrayList<>(), status);
    }

    public static List<SysDictData> selectDictData(String dictType, String scene) {
        return selectDictData(Collections.singletonList(dictType), StringUtils.isNotBlank(scene) ? Collections.singletonList(scene) : new ArrayList<>(), "0");
    }

    public static List<SysDictData> selectDictData(List<String> dictType, String scene) {
        return selectDictData(dictType, StringUtils.isNotBlank(scene) ? Collections.singletonList(scene) : new ArrayList<>(), "0");
    }

    public static List<SysDictData> selectDictData(List<String> dictType, String scene, String status) {
        return selectDictData(dictType, StringUtils.isNotBlank(scene) ? Collections.singletonList(scene) : new ArrayList<>(), status);
    }

    public static List<SysDictData> selectDictData(String dictType, List<String> scene) {
        return selectDictData(Collections.singletonList(dictType), scene, "0");
    }

    public static List<SysDictData> selectDictData(String dictType, List<String> scene, String status) {
        return selectDictData(Collections.singletonList(dictType), scene, status);
    }

    /**
     * 选择字典数据
     *
     * @param dictTypeList 字典类型
     * @param sceneList    场景
     * @param status       状态
     * @return 字典数据列表
     */
    public static List<SysDictData> selectDictData(List<String> dictTypeList, List<String> sceneList, String status) {
        List<SysDictData> sysDictDataList = new ArrayList<>();

        if (StringUtils.isNotEmpty(dictTypeList)) {
            for (String dType : dictTypeList) {
                sysDictDataList.addAll(Objects.requireNonNull(DictUtils.getDictCache(dType)));
            }
        } else {
            sysDictDataList = DictUtils.getDictCache(dictTypeList.get(0));
        }
        if (StringUtils.isEmpty(sysDictDataList)) {
            return new ArrayList<>();
        }

        List<SysSceneDict> sysSceneList = new ArrayList<>();
        sceneList.forEach(scene -> {
            List<SysSceneDict> sceneDictCache = getSceneDictCache(scene);
            if (StringUtils.isNotEmpty(sceneDictCache)) {
                sysSceneList.addAll(sceneDictCache);
            }
        });
        Map<Long, SysSceneDict> sysSceneDict = sysSceneList.stream().collect(Collectors.toMap(SysSceneDict::getDictCode, Function.identity(), (p1, p2) -> p2));

        List<SysDictData> dataList = new ArrayList<>();
        Set<Long> dictCodeList = new HashSet<>();

        for (SysDictData data : sysDictDataList) {
            if (StringUtils.isNotBlank(status)){
                if (!StringUtils.equals(data.getStatus(), status)){
                    // 如果有状态要求, 判断不等
                    continue;
                }
            }
            if (!dictCodeList.contains(data.getDictCode())) {
                dictCodeList.add(data.getDictCode());
                if (StringUtils.isNotEmpty(sceneList)) {
                    SysSceneDict sceneData = sysSceneDict.get(data.getDictCode());
                    if (StringUtils.isNull(sceneData)){
                        continue;
                    }
                    data.setJoinId(sceneData.getJoinId());
                    data.setScene(sceneData.getScene());
                    if (sceneData.getDictLabel() != null) {
                        data.setDictLabel(sceneData.getDictLabel());
                    }
                }
                dataList.add(data);
            }
        }

        return dataList;
    }

}
