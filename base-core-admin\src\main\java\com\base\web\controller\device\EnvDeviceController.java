package com.base.web.controller.device;

import com.alibaba.fastjson2.JSONObject;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.page.TableDataInfoSnake;
import com.base.common.enums.BusinessType;
import com.base.common.exception.ServiceException;
import com.base.common.utils.DictUtils;
import com.base.common.utils.ServletUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.TreeUtils;
import com.base.device.domain.EnvDevice;
import com.base.device.domain.dto.EnvDeviceBatchEditDTO;
import com.base.device.domain.dto.EnvDeviceDTO;
import com.base.device.service.IEnvDeviceService;
import com.base.system.service.ISysFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 设备基础信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/device/envDevice")
public class EnvDeviceController extends BaseController {
    @Autowired
    private IEnvDeviceService envDeviceService;

    @Autowired
    private ISysFieldService sysFieldService;

    /**
     * 分页查询设备基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:device:list')")
    @RequestMapping("/page")
    public TableDataInfoSnake page(@RequestBody EnvDeviceDTO envDeviceDTO) {
        startPage();
        List<EnvDevice> list = envDeviceService.selectEnvDeviceListByDTO(envDeviceDTO);
        list = sysFieldService.setFormatJsonByMenu(list, ServletUtils.getParameter("menu_id"), "device_");
        return getDataTableSnake(list);
    }

    /**
     * 查询设备基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:device:list')")
    @RequestMapping("/list")
    public Object list(@RequestBody EnvDeviceDTO envDeviceDTO) {
        boolean pageFlag = StringUtils.isNotBlank(ServletUtils.getParameter("page_num")) && StringUtils.isNotBlank(ServletUtils.getParameter("page_size"));
        if (pageFlag) {
            startPage();
        }
        String menuId = StringUtils.isNotBlank(envDeviceDTO.getMenuId()) ? envDeviceDTO.getMenuId() : ServletUtils.getParameter("menu_id");
        List<EnvDevice> list = envDeviceService.selectEnvDeviceListByDTO(envDeviceDTO);
        list = sysFieldService.setFormatJsonByMenu(list, menuId, "device_");
        if (pageFlag) {
            return getDataTableSnake(list);
        }
        return AjaxResultSnake.success(list);
    }

    @PreAuthorize("@ss.hasPermi('device:device:list')")
    @RequestMapping("/device_page")
    public TableDataInfoSnake device_page(@RequestBody EnvDeviceDTO envDeviceDTO) {
        startPage();
        envDeviceDTO.getDeviceItem().setDelFlagList(Arrays.asList(0L, 1L));
        List<EnvDevice> list = envDeviceService.selectDevicePointByDTO(envDeviceDTO);
        String menuId = StringUtils.isNotBlank(envDeviceDTO.getMenuId()) ? envDeviceDTO.getMenuId() : ServletUtils.getParameter("menu_id");
        list = sysFieldService.setFormatJsonByMenu(list, menuId, "device_");
        return getDataTableSnake(list);
    }

    /**
     * 查询设备基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:device:list')")
    @RequestMapping("/device_list")
    public Object device_list(@RequestBody EnvDeviceDTO envDeviceDTO) {
        boolean pageFlag = StringUtils.isNotBlank(ServletUtils.getParameter("page_num")) && StringUtils.isNotBlank(ServletUtils.getParameter("page_size"));
        if (pageFlag) {
            startPage();
        }
        envDeviceDTO.getDeviceItem().setDelFlagList(Arrays.asList(0L, 1L));
        List<EnvDevice> list = envDeviceService.selectDevicePointByDTO(envDeviceDTO);
        String menuId = StringUtils.isNotBlank(envDeviceDTO.getMenuId()) ? envDeviceDTO.getMenuId() : ServletUtils.getParameter("menu_id");
        list = sysFieldService.setFormatJsonByMenu(list, menuId, "device_");
        if (pageFlag) {
            return getDataTableSnake(list);
        }
        return AjaxResultSnake.success(list);
    }

    /**
     * 导出设备基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:device:export')")
    @Log(title = "设备基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody EnvDeviceDTO envDeviceDTO) {
        if(StringUtils.isBlank(envDeviceDTO.getDeviceItem().getSubType()) && StringUtils.isBlank(envDeviceDTO.getDeviceItem().getMainType())){
            throw new ServiceException("请选择设备类型后再导出");
        }
        String template = ServletUtils.getParameter("template");
        if (StringUtils.isNotBlank(template)){
            envDeviceDTO.setTemplate(Boolean.valueOf(template));
        }
        if (Boolean.FALSE.equals(envDeviceDTO.getTemplate())){
            if (StringUtils.isNotEmpty(envDeviceDTO.getDeviceIds())){
                envDeviceDTO.getDeviceItem().setDeviceIdList(envDeviceDTO.getDeviceIds());
            }
        }
        envDeviceService.export(response, envDeviceDTO.getDeviceItem(), envDeviceDTO.getTemplate());
    }

    /**
     * 获取设备基础信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:device:query')")
    @GetMapping(value = "get")
    public AjaxResultSnake getInfo(@RequestParam("device_id") Long deviceId) {
        String menuId = ServletUtils.getParameter("menu_id");
        EnvDevice envDevice = envDeviceService.selectEnvDeviceByDeviceId(deviceId);
        envDevice = sysFieldService.setFormatJsonByMenu(envDevice, menuId, "device_");
        return AjaxResultSnake.success(envDevice);
    }

    /**
     * 新增设备基础信息
     */
    @PreAuthorize("@ss.hasPermi('device:device:add')")
    @Log(title = "设备基础信息", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResultSnake add(@RequestBody @Validated(EnvDevice.EnvDeviceAdd.class) EnvDeviceDTO envDeviceDTO) {
        return AjaxResultSnake.success(envDeviceService.insertEnvDevice(envDeviceDTO.getDeviceItem()));
    }

    /**
     * 修改设备基础信息
     */
    @PreAuthorize("@ss.hasPermi('device:device:edit')")
    @Log(title = "设备基础信息", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResultSnake edit(@RequestBody @Validated(EnvDevice.EnvDeviceEdit.class) EnvDeviceDTO envDeviceDTO) {
        return AjaxResultSnake.success(envDeviceService.updateEnvDevice(envDeviceDTO.getDeviceItem()));
    }

    /**
     * 批量修改设备状态
     */
    @PreAuthorize("@ss.hasPermi('device:device:edit')")
    @Log(title = "批量更新设备显示状态", businessType = BusinessType.UPDATE)
    @PostMapping("edit/show")
    public AjaxResultSnake editShow(@RequestBody EnvDeviceBatchEditDTO envDeviceDTO) {
        if (StringUtils.isNull(envDeviceDTO.getProcessId())) {
            return AjaxResultSnake.error("要更新的状态不能为空");
        }
        EnvDevice envDevice = new EnvDevice().setDeviceIdList(envDeviceDTO.getDeviceIds()).setShowFlag(envDeviceDTO.getDelFlag());
        envDeviceService.batchUpdateEnvDevice(envDevice);
        return AjaxResultSnake.success();
    }

    /**
     * 批量绑定区域
     */
    @PreAuthorize("@ss.hasPermi('device:device:edit')")
    @Log(title = "批量更新设备所属区域", businessType = BusinessType.UPDATE)
    @PostMapping("bind/process")
    public AjaxResultSnake editProcess(@RequestBody EnvDeviceBatchEditDTO envDeviceDTO) {
        if (StringUtils.isNull(envDeviceDTO.getProcessId())) {
            return AjaxResultSnake.error("要更新的区域不能为空");
        }
        EnvDevice envDevice = new EnvDevice().setDeviceIdList(envDeviceDTO.getDeviceIds()).setProcessId(envDeviceDTO.getProcessId());
        envDeviceService.batchUpdateEnvDevice(envDevice);
        return AjaxResultSnake.success();
    }

    /**
     * 删除设备基础信息
     */
    @PreAuthorize("@ss.hasPermi('device:device:remove')")
    @Log(title = "设备基础信息", businessType = BusinessType.DELETE)
    @GetMapping("delete")
    public AjaxResultSnake remove(@RequestParam("device_id") Long deviceId) {
        return AjaxResultSnake.success(envDeviceService.deleteEnvDeviceByDeviceId(deviceId));
    }

    @PostMapping("getSubType")
    public AjaxResultSnake getSubType(@RequestBody JSONObject jsonObject) {
        String mainType = jsonObject.getString("main_type");
        if (StringUtils.isBlank(mainType)){
            return AjaxResultSnake.error("请选择设备类别");
        }
        List<SysDictData> deviceSubType = DictUtils.selectDictData("device_sub_type", mainType);
        List<JSONObject> subTypeList = new ArrayList<>();
        for (SysDictData sysDictData : deviceSubType) {
            JSONObject jsonObj = new JSONObject();
            jsonObj.put("sub_type", sysDictData.getDictValue());
            jsonObj.put("dict_label", sysDictData.getDictLabel());
            subTypeList.add(jsonObj);
        }
        return AjaxResultSnake.success(subTypeList);
    }

    /**
     * 设备树筛选接口
     * @param scene
     * @param queryNum
     * @param queryDevice
     * @param queryMain
     * @param querySub
     * @param deviceItem
     * @return
     */
    @RequestMapping("screen")
    public AjaxResultSnake deviceScreen(@RequestParam(value = "scene", required = false) String scene,
                                        @RequestParam(value = "query_num", required = false, defaultValue = "false") Boolean queryNum,
                                        @RequestParam(value = "query_device", required = false, defaultValue = "false") Boolean queryDevice,
                                        @RequestParam(value = "queryMain", required = false, defaultValue = "true") Boolean queryMain,
                                        @RequestParam(value = "querySub", required = false, defaultValue = "true") Boolean querySub,
                                        @RequestParam(value = "device_item", required = false) EnvDevice deviceItem) {
        List<SysDictData> data;

        if (Boolean.TRUE.equals(queryMain)) {
            // 查询主类+子类树
            data = envDeviceService.selectScreenTree(
                    scene,
                    Boolean.TRUE.equals(queryNum),
                    Boolean.TRUE.equals(queryDevice),
                    Boolean.TRUE.equals(querySub),
                    deviceItem
            );
        } else if (!Boolean.TRUE.equals(queryMain) && Boolean.TRUE.equals(querySub)) {
            // 仅查询子类
            data = envDeviceService.selectSubType(
                    scene,
                    Boolean.TRUE.equals(queryNum),
                    Boolean.TRUE.equals(queryDevice),
                    deviceItem
            );
        } else {
            return AjaxResultSnake.error("请选择查询类型");
        }

        // 添加 uuid 到树形结构中（模拟 tree_util.add_uuid）
        List<SysDictData> result = TreeUtils.addUuid(data, "children", "uuid");

        return AjaxResultSnake.success(result);
    }

    @RequestMapping("screenAll")
    public AjaxResultSnake deviceScreenAll(@RequestParam(value = "scene", required = false) String scene,
                                           @RequestParam(value = "query_num", required = false, defaultValue = "false") Boolean queryNum,
                                           @RequestParam(value = "query_device", required = false, defaultValue = "false") Boolean queryDevice,
                                           @RequestParam(value = "queryMain", required = false, defaultValue = "true") Boolean queryMain,
                                           @RequestParam(value = "querySub", required = false, defaultValue = "true") Boolean querySub,
                                           @RequestParam(value = "device_item", required = false) EnvDevice deviceItem) {
        if (StringUtils.isNotNull(deviceItem)){
            deviceItem.setDelFlagList(Arrays.asList(0L, 1L));
        }
        return this.deviceScreen(scene, queryNum, queryDevice, queryMain, querySub, deviceItem);
    }

    @RequestMapping("screen/list")
    public AjaxResultSnake deviceScreenList(@RequestParam(value = "scene", required = false) String scene,
                                            @RequestParam(value = "query_num", required = false, defaultValue = "false") Boolean queryNum,
                                            @RequestParam(value = "query_device", required = false, defaultValue = "false") Boolean queryDevice,
                                            @RequestParam(value = "status", required = false) Integer status,
                                            @RequestParam(value = "device_item", required = false) EnvDevice deviceItem) {

        List<SysDictData> screenList = envDeviceService.selectScreenList(scene, status, queryNum, queryDevice, deviceItem);
        return AjaxResultSnake.success(screenList);
    }

    /**
     * 查询设备子类型列表
     *
     * @param scene        场景标识
     * @param queryNum     是否查询编号
     * @param queryDevice  是否按设备查询
     * @return AjaxResultSnake 响应结果
     */
    @PreAuthorize("@ss.hasPermi('device:device:screen')")
    @RequestMapping(value = "sub_type/list", method = {RequestMethod.GET, RequestMethod.POST})
    public AjaxResultSnake deviceSubTypeList(@RequestParam("scene") String scene,
                                             @RequestParam(value = "query_num", defaultValue = "false") Boolean queryNum,
                                             @RequestParam(value = "query_device", defaultValue = "false") Boolean queryDevice) {
        List<SysDictData> subTypeList = envDeviceService.selectSubType(scene, queryNum, queryDevice, null);
        return AjaxResultSnake.success(subTypeList);
    }

    @GetMapping("run_state/count")
    public AjaxResultSnake runStateCount(@RequestParam(value = "scene") String scene,
                                         @RequestParam(value = "main_type", required = false) String mainType,
                                         @RequestParam(value = "sub_type", required = false) String subType){

        return AjaxResultSnake.success(envDeviceService.runStateCount(scene, mainType, subType));
    }

    /**
     * 将设备置顶或取消置顶
     *
     * @param deviceId 设备ID
     * @param top      置顶状态（true 表示置顶，false 表示取消置顶）
     * @return AjaxResultSnake 响应结果
     */
    @PreAuthorize("@ss.hasPermi('env:device:edit')")
    @GetMapping("to_top")
    public AjaxResultSnake deviceToTop(@RequestParam("device_id") Long deviceId,
                                       @RequestParam(value = "top", defaultValue = "true") Boolean top) {
        envDeviceService.toTop(deviceId, top);
        return AjaxResultSnake.success();
    }

    /**
     * 修改设备排序
     *
     * @param deviceSortList 设备排序列表，包含 device_id 和 sort 字段
     * @return AjaxResultSnake 响应结果
     */
    @PreAuthorize("@ss.hasPermi('env:device:edit')")
    @PostMapping("sort")
    @Transactional
    public AjaxResultSnake deviceSort(@RequestBody List<EnvDevice> deviceSortList) {
        envDeviceService.sortDeviceList(deviceSortList);
        return AjaxResultSnake.success();
    }



}
