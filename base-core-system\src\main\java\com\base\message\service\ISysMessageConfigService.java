package com.base.message.service;

import com.base.message.domain.SysMessageConfig;

import java.util.List;

/**
 * 消息通知配置Service接口
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface ISysMessageConfigService {
    /**
     * 查询消息通知配置
     *
     * @param id 消息通知配置主键
     * @return 消息通知配置
     */
    public SysMessageConfig selectSysMessageConfigById(Long id);

    /**
     * 查询消息通知配置列表
     *
     * @param sysMessageConfig 消息通知配置
     * @return 消息通知配置集合
     */
    public List<SysMessageConfig> selectSysMessageConfigList(SysMessageConfig sysMessageConfig);

    /**
     * 新增消息通知配置
     *
     * @param sysMessageConfig 消息通知配置
     * @return 结果
     */
    public int insertSysMessageConfig(SysMessageConfig sysMessageConfig);

    /**
     * 修改消息通知配置
     *
     * @param sysMessageConfig 消息通知配置
     * @return 结果
     */
    public int updateSysMessageConfig(SysMessageConfig sysMessageConfig);

    /**
     * 批量更新消息通知配置，添加、修改和删除
     *
     * @param sysMessageConfigList 消息通知配置列表
     * @return 结果
     */
    public int batchSave(List<SysMessageConfig> sysMessageConfigList);

    /**
     * 批量删除消息通知配置
     *
     * @param ids 需要删除的消息通知配置主键集合
     * @return 结果
     */
    public int deleteSysMessageConfigByIds(Long[] ids);

    /**
     * 删除消息通知配置信息
     *
     * @param id 消息通知配置主键
     * @return 结果
     */
    public int deleteSysMessageConfigById(Long id);
}
