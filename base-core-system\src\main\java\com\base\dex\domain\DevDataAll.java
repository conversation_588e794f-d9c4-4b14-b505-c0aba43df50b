package com.base.dex.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 所有设备数据对象 dev_data_all
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
public class DevDataAll extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 所属设备ID */
    @Excel(name = "所属设备ID")
    private String deviceId;

    /** 点位编号 */
    @Excel(name = "点位编号")
    private String pointCode;

    /** 点位地址 */
    @Excel(name = "点位地址")
    private String pointAddress;

    /** 中文点位名称 */
    @Excel(name = "中文点位名称")
    private String pointCnName;

    /** 点位数值 */
    @Excel(name = "点位数值")
    private String pointValue;

    /** 点位质量（sys_dict） */
    @Excel(name = "点位质量", readConverterExp = "s=ys_dict")
    private String pointQuality;

    /** 数据更新时间 */
    private String monitorTime;

    /** 点位id */
    private String pointId;

    /** 计算后数值 */
    @Excel(name = "计算后数值")
    private String formulaValue;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deviceId", getDeviceId())
            .append("pointCode", getPointCode())
            .append("pointAddress", getPointAddress())
            .append("pointCnName", getPointCnName())
            .append("pointValue", getPointValue())
            .append("pointQuality", getPointQuality())
            .append("monitorTime", getMonitorTime())
            .append("pointId", getPointId())
            .append("formulaValue", getFormulaValue())
            .toString();
    }
}
