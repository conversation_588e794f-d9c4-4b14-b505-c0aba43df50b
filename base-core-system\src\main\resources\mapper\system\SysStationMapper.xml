<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.SysStationMapper">

    <resultMap type="SysStation" id="SysStationResult">
        <result property="stationId"    column="station_id"    />
        <result property="stationName"    column="station_name"    />
        <result property="stationLocation"    column="station_location"    />
        <result property="stationType"    column="station_type"    />
        <result property="status"    column="status"    />
        <result property="posX"    column="pos_x"    />
        <result property="posY"    column="pos_y"    />
        <result property="posZ"    column="pos_z"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysStationVo">
        select station_id, station_name, station_location, station_type, status, pos_x, pos_y, pos_z, create_time, create_by, update_time, update_by, del_flag, remark from sys_station
    </sql>

    <select id="selectSysStationList" parameterType="SysStation" resultMap="SysStationResult">
        <include refid="selectSysStationVo"/>
        <where>
            del_flag = '0'
            <if test="stationName != null  and stationName != ''"> and station_name like concat('%', #{stationName}, '%')</if>
            <if test="stationLocation != null  and stationLocation != ''"> and station_location = #{stationLocation}</if>
            <if test="stationType != null  and stationType != ''"> and station_type = #{stationType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="posX != null  and posX != ''"> and pos_x = #{posX}</if>
            <if test="posY != null  and posY != ''"> and pos_y = #{posY}</if>
            <if test="posZ != null  and posZ != ''"> and pos_z = #{posZ}</if>
        </where>
        order by station_id
    </select>

    <select id="selectSysStationByStationId" parameterType="Long" resultMap="SysStationResult">
        <include refid="selectSysStationVo"/>
        where station_id = #{stationId}
    </select>

    <insert id="insertSysStation" parameterType="SysStation" useGeneratedKeys="true" keyProperty="stationId">
        insert into sys_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stationName != null">station_name,</if>
            <if test="stationLocation != null">station_location,</if>
            <if test="stationType != null">station_type,</if>
            <if test="status != null">status,</if>
            <if test="posX != null">pos_x,</if>
            <if test="posY != null">pos_y,</if>
            <if test="posZ != null">pos_z,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stationName != null">#{stationName},</if>
            <if test="stationLocation != null">#{stationLocation},</if>
            <if test="stationType != null">#{stationType},</if>
            <if test="status != null">#{status},</if>
            <if test="posX != null">#{posX},</if>
            <if test="posY != null">#{posY},</if>
            <if test="posZ != null">#{posZ},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysStation" parameterType="SysStation">
        update sys_station
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationName != null">station_name = #{stationName},</if>
            <if test="stationLocation != null">station_location = #{stationLocation},</if>
            <if test="stationType != null">station_type = #{stationType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="posX != null">pos_x = #{posX},</if>
            <if test="posY != null">pos_y = #{posY},</if>
            <if test="posZ != null">pos_z = #{posZ},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where station_id = #{stationId}
    </update>

    <delete id="deleteSysStationByStationId" parameterType="Long">
        delete from sys_station where station_id = #{stationId}
    </delete>

    <delete id="deleteSysStationByStationIds" parameterType="String">
        delete from sys_station where station_id in
        <foreach item="stationId" collection="array" open="(" separator="," close=")">
            #{stationId}
        </foreach>
    </delete>

    <insert id="batchSysStation">
        insert into sys_station(station_name, station_location, station_type, status, pos_x, pos_y, pos_z, create_time, create_by, update_time, update_by, del_flag, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.stationName}, #{item.stationLocation}, #{item.stationType}, #{item.status}, #{item.posX}, #{item.posY}, #{item.posZ}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy}, #{item.delFlag}, #{item.remark})
        </foreach>
    </insert>

    <delete id="clear">
        delete from sys_station
    </delete>
</mapper>
