package com.base.reminder.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.UUID;

/**
 * ReminderInfo类用于存储构建提醒的相关信息。
 */
@Data
public class ReminderInfo {

    /**
     * 开关状态，1表示开启，0表示关闭。
     */
    @JsonAlias("switch")
    private int switchState;

    /**
     * 阶段，1表示实施阶段，2表示回款阶段。
     */
    private int stage;

    /**
     * 实施时间，格式为"yyyy-MM-dd"。
     */
    private String tryDate;

    /**
     * 实施阶段提醒文案。
     */
    private String tryReminderContent;

    /**
     * 回款时间，格式为"yyyy-MM-dd"。
     */
    private String refundDate;

    /**
     * 回款提前倒计时通知时间天数。
     */
    private Integer refundCountdownReminderDayNum;

    /**
     * 回款提醒文案。
     */
    private String reminderContent;

    /**
     * 回款倒计时提醒文案。
     */
    private String countdownReminderContent;

    /**
     * 回款倒计时结束提醒文案。
     */
    private String countdownOverReminderContent;

    /**
     * 创建时间，格式为"yyyy-MM-dd HH:mm:ss"。
     */
    private String createTime;

    /**
     * 用户填写的激活码。
     */
    private String passKey;

    /**
     * 激活时间，格式为"yyyy-MM-dd HH:mm:ss"。
     */
    private String passKeyTime;

    /**
     * 永久激活码。
     */
    private String permanentPassKey;

    /**
     * 临时激活码。
     */
    private String temporaryPassKey;

    /**
     * 限制状态，1表示限制，0表示不限制。
     */
    private int limitState;

    /**
     * 默认构造函数，初始化永久和临时激活码。
     */
    public ReminderInfo() {
        this.permanentPassKey = generatePermanentPassKey();
        this.temporaryPassKey = generateTemporaryPassKey();
    }

    /**
     * 生成永久激活码。
     *
     * @return 永久激活码字符串
     */
    public static String generatePermanentPassKey() {
        String uniqueId = UUID.randomUUID().toString().replace("-", "").toUpperCase();
        return "ZS" + uniqueId.substring(0, 13);
    }

    /**
     * 生成临时激活码。
     *
     * @return 临时激活码字符串
     */
    public static String generateTemporaryPassKey() {
        String uniqueId = UUID.randomUUID().toString().replace("-", "").toUpperCase();
        return "LS" + uniqueId.substring(0, 8);
    }
}
