package com.base.web.controller.system;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.page.TableDataInfoSnake;
import com.base.common.enums.BusinessType;
import com.base.common.exception.ServiceException;
import com.base.common.utils.StringUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.system.domain.SysProcess;
import com.base.system.domain.dto.SysProcessDTO;
import com.base.system.service.ISysProcessService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 区域信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@RestController
@RequestMapping("/sys/process")
public class SysProcessController extends BaseController
{
    @Autowired
    private ISysProcessService sysProcessService;

    /**
     * 查询区域信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:process:list')")
    @RequestMapping("/list")
    public AjaxResultSnake list(@RequestBody SysProcessDTO sysProcessDTO)
    {
        List<SysProcess> list = sysProcessService.selectSysProcessList(sysProcessDTO.getProcessItem());
        return AjaxResultSnake.success(list);
    }

    /**
     * 分页查询区域信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:process:list')")
    @RequestMapping("/page")
    public TableDataInfoSnake page(@RequestBody SysProcessDTO sysProcessDTO)
    {
        startPage();
        List<SysProcess> list = sysProcessService.selectSysProcessList(sysProcessDTO.getProcessItem());
        return getDataTableSnake(list);
    }

    @PreAuthorize("@ss.hasPermi('system:process:list')")
    @RequestMapping("/selectChildren")
    public AjaxResultSnake selectChildren(Long ancestorId)
    {
        List<SysProcess> sysProcessList = sysProcessService.selectChildren(ancestorId);
        return AjaxResultSnake.success(sysProcessList);
    }

    /**
     * 导出区域信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:process:export')")
    @Log(title = "区域信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysProcess sysProcess)
    {
        List<SysProcess> list = sysProcessService.selectSysProcessList(sysProcess);
        ExcelUtil<SysProcess> util = new ExcelUtil<SysProcess>(SysProcess.class);
        util.exportExcel(response, list, "区域信息数据");
    }

    /**
     * 获取区域信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:process:query')")
    @GetMapping(value = "/get")
    public AjaxResultSnake getInfo(@RequestParam("process_id") Long processId)
    {
        return AjaxResultSnake.success(sysProcessService.selectSysProcessByProcessId(processId));
    }

    /**
     * 新增区域信息
     */
    @PreAuthorize("@ss.hasPermi('system:process:add')")
    @Log(title = "区域信息", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResultSnake add(@RequestBody @Validated(SysProcess.SysProcessAdd.class) SysProcessDTO sysProcessDTO)
    {
        return AjaxResultSnake.success(sysProcessService.insertSysProcess(sysProcessDTO.getProcessItem()));
    }

    /**
     * 修改区域信息
     */
    @PreAuthorize("@ss.hasPermi('system:process:edit')")
    @Log(title = "区域信息", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResultSnake edit(@RequestBody @Validated(SysProcess.SysProcessEdit.class) SysProcessDTO sysProcessDTO)
    {
        return AjaxResultSnake.success(sysProcessService.updateSysProcess(sysProcessDTO.getProcessItem()));
    }

    /**
     * 删除区域信息
     */
    @PreAuthorize("@ss.hasPermi('system:process:remove')")
    @Log(title = "区域信息", businessType = BusinessType.DELETE)
    @GetMapping("delete")
    public AjaxResultSnake remove(@RequestParam(value = "process_id") Long processId)
    {
        return AjaxResultSnake.success(sysProcessService.deleteSysProcessByProcessId(processId));
    }

    /**
     * 获取流程及其祖先名称列表
     */
    @PreAuthorize("@ss.hasPermi('system:process:list')")
    @GetMapping("/get_ancestors_name")
    public AjaxResultSnake getProcessAncestorsName(@RequestParam(value = "process_id") Long processId)
    {
        String ancestorsName = sysProcessService.getAncestorsName(processId);
        return AjaxResultSnake.success("查询成功", ancestorsName);
    }

    /**
     * 获取流程及其祖先名称列表
     */
    @PreAuthorize("@ss.hasPermi('system:process:list')")
    @GetMapping("/list/ancestors_name")
    public AjaxResultSnake getProcessListAncestorsName()
    {
        List<SysProcess> sysProcessList = sysProcessService.getListAncestorsName();
        return AjaxResultSnake.success(sysProcessList);
    }

    @PreAuthorize("@ss.hasPermi('system:process:list')")
    @RequestMapping("/tree")
    public AjaxResultSnake tree(@RequestBody(required = false) SysProcessDTO sysProcessDTO)
    {
        if (StringUtils.isNull(sysProcessDTO)){
            sysProcessDTO = new SysProcessDTO();
        }
        List<SysProcess> list = sysProcessService.selectSysProcessTree(sysProcessDTO.getProcessItem());
        return AjaxResultSnake.success(list);
    }

    // 定义一个方法，用于导入区域信息数据
    @PreAuthorize("@ss.hasPermi('system:process:add')")
    @PostMapping("/import_data")
    @Log(title = "区域信息", businessType = BusinessType.IMPORT)
    @Transactional
    public AjaxResultSnake importData(@RequestParam("file") MultipartFile file) {
        try {
            // 创建一个映射，用于存储和快速检索已经存在的工序信息
            Map<String, SysProcess> sysProcessMap = sysProcessService.selectSysProcessList(new SysProcess()).stream().
                    collect(Collectors.toMap(p -> StringUtils.format("{}-{}", p.getProcessType(), p.getProcessName()), Function.identity(), (p1, p2) -> p2));

            // 通过Excel文件输入流创建Workbook对象
            Workbook workbook = new XSSFWorkbook(file.getInputStream());

            // 获取Excel文件中的“第一级”工作表
            Sheet sheetLevel1 = workbook.getSheet("第一级");
            // 检查“第一级”工作表是否存在
            if(StringUtils.isNull(sheetLevel1)){
                return AjaxResultSnake.error("第一级sheet不存在");
            }
            // 遍历“第一级”工作表中的每一行
            for (Row row : sheetLevel1) {
                // 跳过第一行（标题行）
                if (row.getRowNum() == 0) continue;
                // 检查当前行的第一列是否存在
                if (StringUtils.isNull(row.getCell(0))) continue;
                // 获取当前行的工序名称
                String processName = row.getCell(1).getStringCellValue();
                // 创建一个新的SysProcess对象，并设置相关属性
                SysProcess process = new SysProcess().setProcessName(processName).setProcessType("proce").setDelFlag(0L).setParentId(0L).setE9Code(StringUtils.format("{}-{}", "proce", processName));
                // 检查当前工序是否已存在于映射中
                SysProcess oldProcess = sysProcessMap.get(StringUtils.format("{}-{}", process.getProcessType(), process.getProcessName()));
                // 如果当前工序不存在于映射中，则插入到数据库并更新映射
                if (StringUtils.isNull(oldProcess)) {
                    sysProcessService.insertSysProcess(process);
                    sysProcessMap.put(StringUtils.format("{}-{}", process.getProcessType(), process.getProcessName()), process);
                }
            }

            // 获取Excel文件中的“第二级”工作表
            Sheet sheetLevel2 = workbook.getSheet("第二级");
            // 检查“第二级”工作表是否存在
            if (StringUtils.isNotNull(sheetLevel2)){
                // 遍历“第二级”工作表中的每一行
                for (Row row : sheetLevel2) {
                    // 跳过第一行（标题行）
                    if (row.getRowNum() == 0) continue;
                    // 检查当前行的第一列是否存在
                    if (StringUtils.isNull(row.getCell(0))) continue;
                    // 获取当前行的工序名称和父工序名称
                    String processName = row.getCell(1).getStringCellValue();
                    String parentName = row.getCell(2).getStringCellValue();
                    // 根据父工序名称获取父工序对象
                    SysProcess parentProcess = sysProcessMap.get(StringUtils.format("{}-{}", "proce", parentName));
                    // 如果父工序不存在，则抛出异常
                    if (StringUtils.isNull(parentProcess)){
                        throw new ServiceException("二级区域中【" + processName + "】所属上级【" + parentName + "】工序未在一级区域中发现，请检查数据对应关系！");
                    }
                    // 创建一个新的SysProcess对象，并设置相关属性
                    SysProcess process = new SysProcess().setProcessName(processName).setProcessType("plant").setParentId(parentProcess.getProcessId()).setE9Code(StringUtils.format("{}-{}", "plant", processName));
                    // 插入到数据库并更新映射
                    sysProcessService.insertSysProcess(process);
                    sysProcessMap.put(StringUtils.format("{}-{}", process.getProcessType(), process.getProcessName()), process);
                }
            }

            // 获取Excel文件中的“第三级”工作表
            Sheet sheetLevel3 = workbook.getSheet("第三级");
            // 检查“第三级”工作表是否存在
            if (StringUtils.isNotNull(sheetLevel3)){
                // 遍历“第三级”工作表中的每一行
                for (Row row : sheetLevel3) {
                    // 跳过第一行（标题行）
                    if (row.getRowNum() == 0) continue;
                    // 检查当前行的第一列是否存在
                    if (StringUtils.isNull(row.getCell(0))) continue;
                    // 获取当前行的工序名称和父工序名称
                    String processName = row.getCell(1).getStringCellValue();
                    String parentName = row.getCell(2).getStringCellValue();
                    // 根据父工序名称获取父工序对象
                    SysProcess parentProcess = sysProcessMap.get(StringUtils.format("{}-{}", "plant", parentName));
                    // 如果父工序不存在，则抛出异常
                    if (StringUtils.isNull(parentProcess)){
                        throw new ServiceException("三级区域中【"+ processName +"】所属上级【" + parentName + "】分厂未在二级区域中发现，请检查数据对应关系！");
                    }
                    // 创建一个新的SysProcess对象，并设置相关属性
                    SysProcess process = new SysProcess().setProcessName(processName).setProcessType("section").setParentId(parentProcess.getProcessId()).setE9Code(StringUtils.format("{}-{}", "section", processName));
                    // 插入到数据库并更新映射
                    sysProcessService.insertSysProcess(process);
                    sysProcessMap.put(StringUtils.format("{}-{}", process.getProcessType(), process.getProcessName()), process);
                }
            }

            // 返回成功消息
            return AjaxResultSnake.success("导入成功");
        } catch (IOException e) {
            // 记录错误日志
            logger.error("区域导入失败", e);
            // 返回错误消息
            return AjaxResultSnake.error("导入失败, 错误原因:" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('system:process:add')")
    @PostMapping("/import_data_check")
    public AjaxResultSnake importDataCheck(@RequestParam("file") MultipartFile file) {
        try {
            // 定义需要读取的sheet页
            String[] sheetsToRead = {"第一级", "第二级", "第三级"};
            Workbook workbook = new XSSFWorkbook(file.getInputStream());

            // 检查是否包含所有需要的sheet页
            for (String sheetName : sheetsToRead) {
                if (workbook.getSheet(sheetName) == null) {
                    return AjaxResultSnake.error("Sheet名称和模板不一致");
                }
            }

            // 读取数据
            Sheet sheetLevel1 = workbook.getSheet("第一级");
            if (sheetLevel1 == null || sheetLevel1.getLastRowNum() == 0) {
                return AjaxResultSnake.error("一级区域名称不能为空");
            }
            Sheet sheetLevel2 = workbook.getSheet("第二级");
            Sheet sheetLevel3 = workbook.getSheet("第三级");

            int sheetCount = sheetsToRead.length;  // 区域数
            int firstGradeRowCount = sheetLevel1.getLastRowNum();  // 一级区域条数
            int secondGradeRowCount = sheetLevel2 == null ? 0 : sheetLevel2.getLastRowNum();  // 二级区域条数
            int thirdGradeRowCount = sheetLevel3 == null ? 0 : sheetLevel3.getLastRowNum();  // 三级区域条数
            List<Map<String, String>> abnormalReasonList = new ArrayList<>();  // 存放异常原因列表

            // 检查第一级数据中的第二列是否重复
            checkDuplicateRows(sheetLevel1, 1, "第一级区域", abnormalReasonList);

            // 检查每个第一级sheet页中的单元格数据是否为空
            checkEmptyCells(sheetLevel1, 1, "第一级区域", abnormalReasonList);

            // 检查第二级数据中的第二列和第三列组合是否重复
            if (sheetLevel2 != null) {
                checkDuplicateRows(sheetLevel2, 1, "第二级区域", abnormalReasonList);
                checkDuplicateRows(sheetLevel2, 2, "第二级区域", abnormalReasonList);
                checkEmptyCells(sheetLevel2, 1, "第二级区域", abnormalReasonList);
                checkEmptyCells(sheetLevel2, 2, "第二级区域", abnormalReasonList);
            }

            // 检查第三级数据中的第二列和第三列组合是否重复
            if (sheetLevel3 != null) {
                checkDuplicateRows(sheetLevel3, 1, "第三级区域", abnormalReasonList);
                checkDuplicateRows(sheetLevel3, 2, "第三级区域", abnormalReasonList);
                checkEmptyCells(sheetLevel3, 1, "第三级区域", abnormalReasonList);
                checkEmptyCells(sheetLevel3, 2, "第三级区域", abnormalReasonList);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("sheet_count", sheetCount);
            result.put("first_grade_row_count", firstGradeRowCount);
            result.put("second_grade_row_count", secondGradeRowCount);
            result.put("third_grade_row_count", thirdGradeRowCount);
            result.put("abnormal_reason_list", abnormalReasonList);

            return AjaxResultSnake.success(result);
        } catch (IOException e) {
            logger.error("文件读取失败", e);
            return AjaxResultSnake.error("文件读取失败, 错误原因:" + e.getMessage());
        } catch (Exception e) {
            logger.error("数据检查失败", e);
            return AjaxResultSnake.error("数据检查失败, 错误原因:" + e.getMessage());
        }
    }

    private void checkDuplicateRows(Sheet sheet, int columnIndex, String sheetName, List<Map<String, String>> abnormalReasonList) {
        Map<String, Integer> cellValueCount = new LinkedHashMap<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || row.getCell(columnIndex) == null) continue;
            String cellValue = row.getCell(columnIndex).getStringCellValue();
            if (StringUtils.isNotEmpty(cellValue)) {
                cellValueCount.put(cellValue, cellValueCount.getOrDefault(cellValue, 0) + 1);
            }
        }
        int i = 1;
        for (Map.Entry<String, Integer> entry : cellValueCount.entrySet()) {
            i++;
            if (entry.getValue() > 1) {
                Map<String, String> abnormalReason = new HashMap<>();
                abnormalReason.put("sheet_name", sheetName);
                abnormalReason.put("row_no", String.valueOf(i));
                abnormalReason.put("content", entry.getKey());
                abnormalReason.put("reason", "数据重复");
                abnormalReasonList.add(abnormalReason);
            }
        }
    }

    private void checkEmptyCells(Sheet sheet, int columnIndex, String sheetName, List<Map<String, String>> abnormalReasonList) {
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || row.getCell(columnIndex) == null || StringUtils.isEmpty(row.getCell(columnIndex).getStringCellValue())) {
                Map<String, String> abnormalReason = new HashMap<>();
                abnormalReason.put("sheet_name", sheetName);
                abnormalReason.put("row_no", String.valueOf(i + 1));
                abnormalReason.put("content", "");
                abnormalReason.put("reason", "中间行数据不能为空");
                abnormalReasonList.add(abnormalReason);
            }
        }
    }

    @PreAuthorize("@ss.hasPermi('system:process:remove')")
    @GetMapping("clear")
    public AjaxResultSnake clear() {
        sysProcessService.clear();
        return AjaxResultSnake.success();
    }



}
