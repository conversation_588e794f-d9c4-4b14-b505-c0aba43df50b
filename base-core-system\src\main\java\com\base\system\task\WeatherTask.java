package com.base.system.task;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.core.redis.RedisCache;
import com.base.common.utils.StringUtils;
import com.base.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component("WeatherTask")
@Slf4j
public class WeatherTask {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    RedisCache redisCache;

    private static final RestTemplate restTemplate = new RestTemplate();
    // 模拟 headers
    private static final HttpHeaders headers = new HttpHeaders();

    static {
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Referer", "http://www.weather.com.cn");
    }

    public void weatherTask() {
        String cityCode = sysConfigService.selectConfigByKey("sys.corp.city.code");
        if (cityCode == null || cityCode.isEmpty()) {
            log.error("未找到城市编码，无法获取天气情况");
            return;
        }

        Map<String, String> weatherDict = new HashMap<>();
        weatherDict.put("weather_css", "");
        weatherDict.put("weather_power", "");
        weatherDict.put("weather_direction", "");
        weatherDict.put("weather", "");
        weatherDict.put("weather_temperature", "");
        weatherDict.put("aqi", "");
        weatherDict.put("province", "");
        weatherDict.put("city", "");
        weatherDict.put("county", "");

        try {
            String locationJson = sysConfigService.selectConfigByKey("sys.corp.location");
            JSONArray cityDetail = JSONArray.parse(locationJson);

            if (StringUtils.isNotEmpty(cityDetail)) {
                weatherDict.put("province", cityDetail.getString(0));
                weatherDict.put("city", cityDetail.getString(1));
                weatherDict.put("county", cityDetail.getString(2));
            }

            String weatherUrl = "https://www.weather.com.cn/weather1d/" + cityCode + ".shtml#input";

            String exchange = this.getHtml(weatherUrl);
            Document doc = Jsoup.parse(exchange.toString());
            Elements scripts = doc.getElementsByTag("script");

            for (org.jsoup.nodes.Element script : scripts) {
                String scriptData = script.html();
                if (scriptData.contains("hour3data")) {
                    if (scriptData.contains("=")) {
                        String resStr = scriptData.split("=")[1].trim();
                        JSONObject resNode = JSONObject.parseObject(resStr);
                        JSONArray hour3Data = resNode.getJSONArray("1d");

                        if (StringUtils.isNotEmpty(hour3Data)) {
                            String[] spl = hour3Data.getString(0).split(",");
                            if (spl.length >= 7) {
                                weatherDict.put("weather_css", "png40 " + spl[1] + " lv" + spl[6]);
                                weatherDict.put("weather_power", spl[5]);
                                weatherDict.put("weather_direction", spl[4]);
                                weatherDict.put("weather", spl[2]);
                                weatherDict.put("weather_temperature", spl[3]);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取天气失败：{}", e.getMessage(), e);
        }

        try {
            String aqiUrl = "http://d1.weather.com.cn/sk_2d/" + cityCode + ".html?_=" + Instant.now().toEpochMilli();
            ResponseEntity<String> aqiResponse = restTemplate.exchange(aqiUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
            String aqiBody = aqiResponse.getBody();

            if (aqiBody != null && aqiBody.contains("=")) {
                String aqiJson = aqiBody.split("=")[1];
//                JsonNode aqiNode = objectMapper.readTree(aqiJson);
                JSONObject aqiNode = JSONObject.parse(aqiJson);
                String aqi = aqiNode.containsKey("aqi") ? aqiNode.getString("aqi") : "";
                weatherDict.put("aqi", aqi);
                log.info("aqi = {}", aqi);
            }

        } catch (Exception e) {
            log.error("获取AQI失败：{}", e.getMessage(), e);
        }

        log.info("{} 天气情况为: {}", cityCode, weatherDict);

        if (!weatherDict.isEmpty()) {
            redisCache.setCacheObject("weather_now", weatherDict, 3, TimeUnit.HOURS);
        }

    }

    public String getHtml(String urlStr) throws NoSuchAlgorithmException, KeyManagementException, IOException {
        try {
            // 创建一个信任所有证书的 TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }
                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        }
                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        }
                    }
            };

            // 初始化 SSLContext
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());

            // 创建一个验证所有主机名的 HostnameVerifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // 设置默认的 SSLSocketFactory 和 HostnameVerifier
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // 发送 HTTPS 请求
            URL url = new URL(urlStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法
            connection.setRequestMethod("GET");

            // 获取响应码
            int responseCode = connection.getResponseCode();

            // 读取响应内容
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            StringBuilder content = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            reader.close();
            return content.toString();
        } catch (NoSuchAlgorithmException | KeyManagementException | IOException e) {
            e.printStackTrace();
        }
        return "";
    }


}
