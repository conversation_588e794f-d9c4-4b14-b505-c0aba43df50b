package com.base.system.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 待办事项已读状态对象 todo_read_status
 *
 * <AUTHOR>
 * @date 2023-10-01
 */
public class TodoReadStatus extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 已读状态ID
     */
    @Excel(name = "已读状态ID")
    private Long readStatusId;

    /**
     * 用户ID，关联sys_user表
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 待办事项ID，关联todo_items表
     */
    @Excel(name = "待办事项ID")
    private Long todoId;

    /**
     * 阅读状态（0未读 1已读）
     */
    @Excel(name = "阅读状态", readConverterExp = "0=未读,1=已读")
    private Integer readStatus;

    /**
     * 阅读时间
     */
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    public void setReadStatusId(Long readStatusId) {
        this.readStatusId = readStatusId;
    }

    public Long getReadStatusId() {
        return readStatusId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setTodoId(Long todoId) {
        this.todoId = todoId;
    }

    public Long getTodoId() {
        return todoId;
    }

    public void setReadStatus(Integer readStatus) {
        this.readStatus = readStatus;
    }

    public Integer getReadStatus() {
        return readStatus;
    }

    public void setReadTime(Date readTime) {
        this.readTime = readTime;
    }

    public Date getReadTime() {
        return readTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("readStatusId", getReadStatusId())
                .append("userId", getUserId())
                .append("todoId", getTodoId())
                .append("readStatus", getReadStatus())
                .append("readTime", getReadTime())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
