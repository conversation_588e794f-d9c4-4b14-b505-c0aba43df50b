package com.base.common.heatmap;

/**
 * 高德坐标转换GPS坐标 <br>
 * 调用：转经度 GCJ2WGSUtils.WGSLon(纬度,经度) <br>
 * 调用：转纬度 GCJ2WGSUtils.WGSLat(纬度,经度)
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
public class Gcj2WgsUtils {
    /**
     * 输入GCJ经纬度 转WGS纬度
     *
     * @param lat 纬度
     * @param lon 经度
     * @return
     */
    public static double wgsLat(double lat, double lon) {
        // 圆周率
        double PI = 3.14159265358979324;
        // 克拉索夫斯基椭球参数长半轴a
        double a = 6378245.0;
        // 克拉索夫斯基椭球参数第一偏心率平方
        double ee = 0.00669342162296594323;
        double dLat = transformLat(lon - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * PI;
        double magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * PI);
        return (lat - dLat);
    }

    /**
     * 输入GCJ经纬度 转WGS经度 xlon
     *
     * @param lat 纬度
     * @param lon 经度
     * @return
     */
    public static double wgsLon(double lat, double lon) {
        // 圆周率
        double PI = 3.14159265358979324;
        // 克拉索夫斯基椭球参数长半轴a
        double a = 6378245.0;
        // 克拉索夫斯基椭球参数第一偏心率平方
        double ee = 0.00669342162296594323;
        double dLon = transformLon(lon - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * PI;
        double magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * PI);
        return (lon - dLon);
    }

    /**
     * 转换经度所需
     *
     * @param x
     * @param y
     * @return
     */
    public static double transformLon(double x, double y) {
        // 圆周率
        double PI = 3.14159265358979324;
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
        return ret;
    }

    /**
     * 转换纬度所需
     *
     * @param x
     * @param y
     * @return
     */
    public static double transformLat(double x, double y) {
        // 圆周率
        double PI = 3.14159265358979324;
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
        return ret;
    }
}
