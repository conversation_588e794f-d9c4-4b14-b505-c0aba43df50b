package com.base.common.core.page;

import com.base.common.config.SnakeCaseDataSerializer;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表格分页数据对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TableDataInfoSnake implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 列表数据 */
    @JsonSerialize(using = SnakeCaseDataSerializer.class)
    private Map<String, Object> data = new HashMap<>();

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String msg;

    /**
     * 表格数据对象
     */
    public TableDataInfoSnake()
    {
    }

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     */
    public TableDataInfoSnake(List<?> list, int total)
    {
        this.data.put("rows", list);
        this.data.put("total", total);
    }

    public void setTotal(long total)
    {
        this.data.put("total", total);
    }

    public void setRows(List<?> rows)
    {

        this.data.put("rows", rows);
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public void setPageNum(int pageNum) {
        this.data.put("page_num", pageNum);
    }

    public void setPageSize(int pageSize){
        this.data.put("page_size", pageSize);
}
}
