package com.base.dex.service;

import com.alibaba.fastjson2.JSONObject;
import com.base.common.heatmap.HeatPoint;
import com.base.dex.domain.DevDataPointHour;

import java.util.Date;
import java.util.List;

/**
 * 点位小时数据Service接口
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IDevDataPointHourService {
    /**
     * 查询点位小时数据
     *
     * @param pointId 点位小时数据主键
     * @return 点位小时数据
     */
    public DevDataPointHour selectDevDataPointHourByPointId(String pointId);

    /**
     * 查询点位小时数据列表
     *
     * @param devDataPointHour 点位小时数据
     * @return 点位小时数据集合
     */
    public List<DevDataPointHour> selectDevDataPointHourList(DevDataPointHour devDataPointHour);

    /**
     * 新增点位小时数据
     *
     * @param devDataPointHour 点位小时数据
     * @return 结果
     */
    public int insertDevDataPointHour(DevDataPointHour devDataPointHour);

    /**
     * 修改点位小时数据
     *
     * @param devDataPointHour 点位小时数据
     * @return 结果
     */
    public int updateDevDataPointHour(DevDataPointHour devDataPointHour);

    /**
     * 批量删除点位小时数据
     *
     * @param pointIds 需要删除的点位小时数据主键集合
     * @return 结果
     */
    public int deleteDevDataPointHourByPointIds(String[] pointIds);

    /**
     * 删除点位小时数据信息
     *
     * @param pointId 点位小时数据主键
     * @return 结果
     */
    public int deleteDevDataPointHourByPointId(String pointId);

    /**
     * 获取点位小时数据列表
     *
     * @param devDataPointHour 点位小时数据
     * @return 点位小时数据集合
     */
    public List<DevDataPointHour> getDevDataPointHourList(DevDataPointHour devDataPointHour);

    /**
     * 获取企业厂界范围坐标列表
     *
     * @return 厂界范围坐标列表
     */
    public List<HeatPoint> getEnvDataPointList();

    /**
     * 生成热力图图片路径
     *
     * @param hourList   生成图片的小时数据
     * @param pixelWide  像素宽度
     * @param type       监测指标类型：pm2_5、pm10
     * @param date       日期，只能传一天
     * @param filePrefix 生成文件名的前缀，如:hour_，hour_20200712.png
     * @return
     */
    public JSONObject getHeatMapImg(List<DevDataPointHour> hourList, int pixelWide, String type, Date date, String filePrefix);

    /**
     * 获取环境质量监测微站-小型空气质量监测站14和厂界监测微站21的最近平均值
     *
     * @param devDataPointHour 点位小时数据
     * @return 环境质量监测微站-小型空气质量监测站14和厂界监测微站21的最近平均值
     */
    public DevDataPointHour getDevDataPointHourByAvg(DevDataPointHour devDataPointHour);
}
