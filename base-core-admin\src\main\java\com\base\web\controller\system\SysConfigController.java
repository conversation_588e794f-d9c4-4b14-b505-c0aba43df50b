package com.base.web.controller.system;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.AjaxResultSnake;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.StringUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.system.domain.SysConfig;
import com.base.system.domain.dto.SysConfigDTO;
import com.base.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 参数配置 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController
{
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysConfig config)
    {
        startPage();
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    @Log(title = "参数管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysConfig config)
    {
        List<SysConfig> list = configService.selectConfigList(config);
        ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable Long configId)
    {
        return success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey)
    {
        SysConfig configByKey = configService.getConfigByKey(configKey);
        return success(StringUtils.isNotNull(configByKey) ? configByKey.getConfigValue() : StringUtils.EMPTY, configByKey);
    }

    /**
     * 新增参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated(SysConfig.SysConfigAdd.class) @RequestBody SysConfig config)
    {
        if (!configService.checkConfigKeyUnique(config))
        {
            return error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setCreateBy(getUsername());
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @RequestMapping("edit")
    public AjaxResult edit(@Validated(SysConfig.SysConfigEdit.class) @RequestBody SysConfig config)
    {
//        if (!configService.checkConfigKeyUnique(config))
//        {
//            return error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
//        }
        config.setUpdateBy(getUsername());
        return toAjax(configService.updateConfig(config));
    }

    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @RequestMapping("editList")
    public AjaxResult editList(@Validated(SysConfig.SysConfigEdit.class) @RequestBody List<SysConfig> configList)
    {
        int res = 0;
        for (SysConfig config : configList) {
            config.setUpdateBy(getUsername());
            res += configService.updateConfig(config);
        }
        return toAjax(res);
    }

    /**
     * 删除参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.DELETE)
    @RequestMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        configService.deleteConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN)
    @RequestMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        configService.resetConfigCache();
        return success();
    }

    @PostMapping("/add")
    @Log(title = "新增sys_config", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    public AjaxResultSnake sysConfigAdd(@RequestBody SysConfigDTO sysDataDTO) {
        SysConfig sysDataItem = sysDataDTO.getSysDataItem();

        String configId = configService.selectConfigByKey(sysDataItem.getConfigKey());

        if (StringUtils.isNotBlank(configId)) {
            configService.deleteConfigByIds(new Long[]{Long.parseLong(configId)});
        }

        return AjaxResultSnake.success(configService.insertConfig(sysDataItem));
    }

//    @GetMapping("/query")
//    @PreAuthorize("@ss.hasPermi('system:config:query')")
//    public AjaxResultSnake sysConfigQuery(@RequestParam(required = false) String typeId) {
//        // 等待设备服务
//        // 等待数采服务
//        return AjaxResultSnake.success(configService.getConfigFactorByTypeId(typeId));
//    }

    /**
     * 查询3D地图虚拟参观配置文件信息
     */
    @GetMapping("/get_3d_config_file_info")
    public AjaxResultSnake get3DConfigFileInfo() {
        SysConfig configValue = configService.getConfigByKey("sys.3d:virtual:tour:config:file");
        return AjaxResultSnake.success("成功", configValue);
    }
}
