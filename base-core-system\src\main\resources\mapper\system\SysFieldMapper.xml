<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.SysFieldMapper">

    <resultMap type="SysField" id="SysFieldResult">
        <result property="fieldKey"    column="field_key"    />
        <result property="fieldLabel"    column="field_label"    />
        <result property="fieldValue"    column="field_value"    />
        <result property="fieldType"    column="field_type"    />
        <result property="fieldDefault"    column="field_default"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="fieldRequired"    column="field_required"    />
        <result property="fieldStatic"    column="field_static"    />
        <result property="fieldScene"    column="field_scene"    />
        <result property="dictType"    column="dict_type"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="fieldId"    column="field_id"    />
        <result property="remark"    column="remark"    />
        <result property="fieldDefaultShowFlag"    column="field_default_show_flag"    />
        <result property="fieldConfigVersion"    column="field_config_version"    />
        <result property="fieldFilter"    column="field_filter"    />
    </resultMap>

    <sql id="selectSysFieldVo">
        select field_key, field_label, field_value, field_type, field_default, del_flag, create_by, create_time, update_by, update_time, field_required, field_static, field_scene, dict_type, sort, status, field_id, remark, field_default_show_flag, field_config_version, field_filter from sys_field
    </sql>

    <select id="selectSysFieldList" parameterType="SysField" resultMap="SysFieldResult">
        <include refid="selectSysFieldVo"/>
        <where>
            del_flag = '0'
            <if test="fieldKey != null  and fieldKey != ''"> and field_key = #{fieldKey}</if>
            <if test="fieldLabel != null  and fieldLabel != ''"> and field_label = #{fieldLabel}</if>
            <if test="fieldValue != null  and fieldValue != ''"> and field_value = #{fieldValue}</if>
            <if test="fieldType != null  and fieldType != ''"> and field_type = #{fieldType}</if>
            <if test="fieldDefault != null  and fieldDefault != ''"> and field_default = #{fieldDefault}</if>
            <if test="fieldRequired != null "> and field_required = #{fieldRequired}</if>
            <if test="fieldStatic != null "> and field_static = #{fieldStatic}</if>
            <if test="fieldScene != null  and fieldScene != ''"> and field_scene = #{fieldScene}</if>
            <if test="dictType != null  and dictType != ''"> and dict_type = #{dictType}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="fieldDefaultShowFlag != null  and fieldDefaultShowFlag != ''"> and field_default_show_flag = #{fieldDefaultShowFlag}</if>
            <if test="fieldConfigVersion != null  and fieldConfigVersion != ''"> and field_config_version = #{fieldConfigVersion}</if>
            <if test="fieldFilter != null "> and field_filter = #{fieldFilter}</if>
        </where>
        order by sort asc
    </select>

    <select id="selectSysFieldByFieldId" parameterType="Long" resultMap="SysFieldResult">
        <include refid="selectSysFieldVo"/>
        where field_id = #{fieldId} and del_flag = '0'
    </select>
    <select id="selectByFieldKeyLike" parameterType="String" resultMap="SysFieldResult">
        <include refid="selectSysFieldVo"/>
        where field_key like concat(#{fieldKey},'%') and del_flag = '0'
        order by sort
    </select>

    <insert id="insertSysField" parameterType="SysField" useGeneratedKeys="true" keyProperty="fieldId">
        insert into sys_field
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fieldKey != null">field_key,</if>
            <if test="fieldLabel != null">field_label,</if>
            <if test="fieldValue != null">field_value,</if>
            <if test="fieldType != null">field_type,</if>
            <if test="fieldDefault != null">field_default,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="fieldRequired != null">field_required,</if>
            <if test="fieldStatic != null">field_static,</if>
            <if test="fieldScene != null">field_scene,</if>
            <if test="dictType != null">dict_type,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="fieldDefaultShowFlag != null">field_default_show_flag,</if>
            <if test="fieldConfigVersion != null">field_config_version,</if>
            <if test="fieldFilter != null">field_filter,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fieldKey != null">#{fieldKey},</if>
            <if test="fieldLabel != null">#{fieldLabel},</if>
            <if test="fieldValue != null">#{fieldValue},</if>
            <if test="fieldType != null">#{fieldType},</if>
            <if test="fieldDefault != null">#{fieldDefault},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="fieldRequired != null">#{fieldRequired},</if>
            <if test="fieldStatic != null">#{fieldStatic},</if>
            <if test="fieldScene != null">#{fieldScene},</if>
            <if test="dictType != null">#{dictType},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fieldDefaultShowFlag != null">#{fieldDefaultShowFlag},</if>
            <if test="fieldConfigVersion != null">#{fieldConfigVersion},</if>
            <if test="fieldFilter != null">#{fieldFilter},</if>
         </trim>
    </insert>

    <update id="updateSysField" parameterType="SysField">
        update sys_field
        <trim prefix="SET" suffixOverrides=",">
            <if test="fieldKey != null">field_key = #{fieldKey},</if>
            <if test="fieldLabel != null">field_label = #{fieldLabel},</if>
            <if test="fieldValue != null">field_value = #{fieldValue},</if>
            <if test="fieldType != null">field_type = #{fieldType},</if>
            <if test="fieldDefault != null">field_default = #{fieldDefault},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="fieldRequired != null">field_required = #{fieldRequired},</if>
            <if test="fieldStatic != null">field_static = #{fieldStatic},</if>
            <if test="fieldScene != null">field_scene = #{fieldScene},</if>
            <if test="dictType != null">dict_type = #{dictType},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fieldDefaultShowFlag != null">field_default_show_flag = #{fieldDefaultShowFlag},</if>
            <if test="fieldConfigVersion != null">field_config_version = #{fieldConfigVersion},</if>
            <if test="fieldFilter != null">field_filter = #{fieldFilter},</if>
        </trim>
        where field_id = #{fieldId}
    </update>

    <update id="deleteSysFieldByFieldId" parameterType="Long">
        update sys_field set del_flag = '2' where field_id = #{fieldId}
    </update>

    <update id="deleteSysFieldByFieldIds" parameterType="String">
        update sys_field set del_flag = '2' where field_id in
        <foreach item="fieldId" collection="array" open="(" separator="," close=")">
            #{fieldId}
        </foreach>
    </update>

    <insert id="batchSysField">
        insert into sys_field( field_key, field_label, field_value, field_type, field_default, del_flag, create_by, create_time, update_by, update_time, field_required, field_static, field_scene, dict_type, status, remark, field_default_show_flag, field_config_version, field_filter) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.fieldKey}, #{item.fieldLabel}, #{item.fieldValue}, #{item.fieldType}, #{item.fieldDefault}, #{item.delFlag}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.fieldRequired}, #{item.fieldStatic}, #{item.fieldScene}, #{item.dictType}, #{item.status}, #{item.remark}, #{item.fieldDefaultShowFlag}, #{item.fieldConfigVersion}, #{item.fieldFilter})
        </foreach>
    </insert>
</mapper>
