package com.base.device.domain.dto;

import com.base.device.domain.EnvDevice;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

@Data
public class EnvDeviceDTO {

    @JsonAlias("device_item")
    @Valid
    private EnvDevice deviceItem = new EnvDevice();

    @JsonAlias("menu_id")
    private String menuId;

    @JsonAlias("template")
    private Boolean template;

    @JsonAlias("device_ids")
    private List<Long> deviceIds;
}
