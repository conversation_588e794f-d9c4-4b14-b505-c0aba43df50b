package com.base.dex.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 设备点对象 dev_base_point
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public class DevBasePoint extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String pointId;

    /** 点位名称 */
    @Excel(name = "点位名称")
    private String pointCode;

    /** 点位名称中文 */
    @Excel(name = "点位名称中文")
    private String pointCodeCn;

    /** 点位数据类型(dict_data) */
    @Excel(name = "点位数据类型(dict_data)")
    private String pointDataType;

    /** 点位读写方式(dict_data) */
    @Excel(name = "点位读写方式(dict_data)")
    private String pointRdType;

    /** 点位地址 */
    @Excel(name = "点位地址")
    private String pointAddress;

    /** 数据单位 */
    @Excel(name = "数据单位")
    private String pointUnit;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private String createDate;

    /** 更新时间 */
    @Excel(name = "更新时间")
    private String updateDate;

    /** 所属设备ID */
    @Excel(name = "所属设备ID")
    private String deviceId;

    /** 数据计算公式 示例：${value} * 1000 (${value}代表输入值) */
    @Excel(name = "数据计算公式 示例：${value} * 1000 (${value}代表输入值)")
    private String pointFormula;

    /** 是否为虚拟点位 0否 1是 */
    @Excel(name = "是否为虚拟点位 0否 1是")
    private String isVirtual;

    /** 设备删除标记 */
    private String delFlag;

    /** 演示数据计算公式，示例1-100表示生成该范围的值 */
    @Excel(name = "演示数据计算公式，示例1-100表示生成该范围的值")
    private String pointFormulaDemo;

    /** 读取顺序（0：正序，1:反序） */
    @Excel(name = "读取顺序", readConverterExp = "0=：正序，1:反序")
    private String readSequence;

    /** 读取位置 布尔类型使用 */
    @Excel(name = "读取位置 布尔类型使用")
    private String readPosition;

    /** 部分数据展示配置项（配置调用的方法名） */
    @Excel(name = "部分数据展示配置项", readConverterExp = "配=置调用的方法名")
    private String viewFormula;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pointId", getPointId())
            .append("pointCode", getPointCode())
            .append("pointCodeCn", getPointCodeCn())
            .append("pointDataType", getPointDataType())
            .append("pointRdType", getPointRdType())
            .append("pointAddress", getPointAddress())
            .append("pointUnit", getPointUnit())
            .append("remark", getRemark())
            .append("createDate", getCreateDate())
            .append("updateDate", getUpdateDate())
            .append("deviceId", getDeviceId())
            .append("pointFormula", getPointFormula())
            .append("isVirtual", getIsVirtual())
            .append("delFlag", getDelFlag())
            .append("pointFormulaDemo", getPointFormulaDemo())
            .append("readSequence", getReadSequence())
            .append("readPosition", getReadPosition())
            .append("viewFormula", getViewFormula())
            .toString();
    }
}
