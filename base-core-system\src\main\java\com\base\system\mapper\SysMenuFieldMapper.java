package com.base.system.mapper;

import com.base.system.domain.SysMenuField;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 菜单-动态字段 关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Repository
public interface SysMenuFieldMapper
{
    /**
     * 查询菜单-动态字段 关联关系
     *
     * @param joinId 菜单-动态字段 关联关系主键
     * @return 菜单-动态字段 关联关系
     */
    public SysMenuField selectSysMenuFieldByJoinId(Long joinId);

    /**
     * 查询菜单-动态字段 关联关系列表
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 菜单-动态字段 关联关系集合
     */
    public List<SysMenuField> selectSysMenuFieldList(SysMenuField sysMenuField);

    /**
     * 新增菜单-动态字段 关联关系
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 结果
     */
    public int insertSysMenuField(SysMenuField sysMenuField);

    /**
     * 修改菜单-动态字段 关联关系
     *
     * @param sysMenuField 菜单-动态字段 关联关系
     * @return 结果
     */
    public int updateSysMenuField(SysMenuField sysMenuField);

    /**
     * 删除菜单-动态字段 关联关系
     *
     * @param joinId 菜单-动态字段 关联关系主键
     * @return 结果
     */
    public int deleteSysMenuFieldByJoinId(Long joinId);

    /**
     * 批量删除菜单-动态字段 关联关系
     *
     * @param joinIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysMenuFieldByJoinIds(Long[] joinIds);

    /**
     * 批量新增菜单-动态字段 关联关系
     *
     * @param sysMenuFieldList 菜单-动态字段 关联关系列表
     * @return 结果
     */
    public int batchSysMenuField(List<SysMenuField> sysMenuFieldList);

    int deleteByMenuIdAndFieldKey(Long menuId, String fieldKey);
}
