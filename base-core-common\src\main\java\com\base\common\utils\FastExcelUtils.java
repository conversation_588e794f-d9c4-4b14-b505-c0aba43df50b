package com.base.common.utils;

import cn.idev.excel.FastExcel;
import com.base.common.core.domain.entity.FastExcelDTO;
import lombok.SneakyThrows;
import org.apache.poi.ss.formula.functions.T;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class FastExcelUtils {

    @SneakyThrows
    public static void exportExcel(HttpServletResponse response, String fileName, String sheetName, List<List<String>> headerList, List<List<Object>> dataList) {
        configureResponseHeaders(response, fileName);
        sheetName = sanitizeSheetName(sheetName);
        FastExcel.write(response.getOutputStream()).head(headerList).sheet(sheetName).doWrite(dataList);
    }


    @SneakyThrows
    public static void exportExcel(HttpServletResponse response, String fileName, String sheetName, List<Map<String, Object>> dataList) {
        configureResponseHeaders(response, fileName);
        sheetName = sanitizeSheetName(sheetName);
        List<String> headerList = new ArrayList<>();
        List<List<Object>> writeDataList = new ArrayList<>();
        if (StringUtils.isNotEmpty(dataList)) {
            headerList = new ArrayList<>(dataList.get(0).keySet());
            for (Map<String, Object> data : dataList) {
                List<Object> writeData = new ArrayList<>();
                for (String header : headerList) {
                    writeData.add(data.get(header));
                }
                writeDataList.add(writeData);
            }
        }
        FastExcel.write(response.getOutputStream()).head(Collections.singletonList(headerList)).sheet(sheetName).doWrite(writeDataList);
    }

    @SneakyThrows
    public static void exportExcel(HttpServletResponse response, FastExcelDTO<T> fastExcelDTO) {
        configureResponseHeaders(response, fastExcelDTO.getFileName());
        String sheetName = sanitizeSheetName(fastExcelDTO.getSheetName());
        FastExcel.write(response.getOutputStream()).head(fastExcelDTO.getHeaderList()).sheet(sheetName).doWrite(fastExcelDTO.getDataList());
    }

    private static void configureResponseHeaders(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        if (!(StringUtils.endsWith(fileName, ".xls") || StringUtils.endsWith(fileName, ".xlsx"))) {
            fileName += ".xlsx";
        }
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
    }

    /**
     * 清理并修复Excel工作表名称，使其符合规范
     *
     * @param originalName 原始工作表名称
     * @return 符合Excel规范的工作表名称
     */
    public static String sanitizeSheetName(String originalName) {
        if (originalName == null || originalName.isEmpty()) {
            return "Sheet"; // 默认名称
        }

        // 替换特殊字符为下划线
        String cleanedName = originalName
                .replaceAll("[\\\\/:*?\"<>|']", "_") // 替换所有非法字符
                .replaceAll("^'+|'+$", "_"); // 替换首尾单引号

        // 限制长度为31个字符
        if (cleanedName.length() > 31) {
            cleanedName = cleanedName.substring(0, 31);
        }

        // 确保处理后的名称不为空
        if (cleanedName.isEmpty()) {
            return "Sheet";
        }

        // 处理首尾为下划线的情况（可能由首尾单引号替换而来）
        if (cleanedName.startsWith("_")) {
            cleanedName = "S" + cleanedName.substring(1);
        }
        if (cleanedName.endsWith("_")) {
            cleanedName = cleanedName.substring(0, cleanedName.length() - 1) + "S";
        }

        return cleanedName;
    }

}
