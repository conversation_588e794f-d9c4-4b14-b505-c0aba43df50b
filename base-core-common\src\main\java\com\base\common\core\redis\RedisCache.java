package com.base.common.core.redis;

import com.base.common.config.RedisTemplateContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundSetOperations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * spring redis 工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Component
public class RedisCache {

    @Autowired
    private RedisTemplateContext redisTemplateContext;



    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplateContext.getRedisTemplate().opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        if (timeout > 0){
            redisTemplateContext.getRedisTemplate().opsForValue().set(key, value, timeout, timeUnit);
        }else{
            redisTemplateContext.getRedisTemplate().opsForValue().set(key, value);
        }
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return redisTemplateContext.getRedisTemplate().expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key) {
        return redisTemplateContext.getRedisTemplate().getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        return redisTemplateContext.getRedisTemplate().hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplateContext.getRedisTemplate().opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key) {
        return redisTemplateContext.getRedisTemplate().delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection) {
        return redisTemplateContext.getRedisTemplate().delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        Long count = redisTemplateContext.getRedisTemplate().opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        return redisTemplateContext.getRedisTemplate().opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        BoundSetOperations<String, T> setOperation = redisTemplateContext.getRedisTemplate().boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext()) {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        return redisTemplateContext.getRedisTemplate().opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (dataMap != null) {
            redisTemplateContext.getRedisTemplate().opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        return redisTemplateContext.getRedisTemplate().opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        redisTemplateContext.getRedisTemplate().opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        HashOperations<String, String, T> opsForHash = redisTemplateContext.getRedisTemplate().opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        return redisTemplateContext.getRedisTemplate().opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        return redisTemplateContext.getRedisTemplate().opsForHash().delete(key, hKey) > 0;
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        return redisTemplateContext.getRedisTemplate().keys(pattern);
    }

    /**
     * 设置缓存对象（仅当 key 不存在时）
     *
     * @param key     缓存的键值
     * @param value   缓存的值
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return 是否设置成功（true 表示 key 不存在并已成功设置）
     */
    public <T> boolean setIfAbsent(final String key, final Object value, final long timeout, final TimeUnit unit) {
        if (timeout <= 0) {
            return this.setIfAbsent(key,value);
        }
        return Boolean.TRUE.equals(redisTemplateContext.getRedisTemplate().opsForValue().setIfAbsent(key, value, timeout, unit));
    }

    public <T> boolean setIfAbsent(final String key, final Object value, Duration duration) {
        if (duration.isZero()){
            return this.setIfAbsent(key, value);
        }
        return Boolean.TRUE.equals(redisTemplateContext.getRedisTemplate().opsForValue().setIfAbsent(key, value, duration));
    }

    public <T> boolean setIfAbsent(final String key, final Object value) {
        return Boolean.TRUE.equals(redisTemplateContext.getRedisTemplate().opsForValue().setIfAbsent(key, value));
    }

    /**
     * 对缓存的数值型 key 递增
     *
     * @param key   缓存的键值
     * @param delta 递增值（必须为正数）
     * @return 新值
     */
    public Long increment(final String key, final long delta) {
        return redisTemplateContext.getRedisTemplate().opsForValue().increment(key, delta);
    }

    /**
     * 对缓存的数值型 key 递增
     *
     * @param key   缓存的键值
     * @return 新值
     */
    public Long increment(final String key) {
        return this.increment(key, 1);
    }

    /**
     * 对缓存的数值型 key 递减
     *
     * @param key 缓存的键值
     * @param delta 递减值（必须为正数）
     * @return 新值
     */
    public Long decrement(final String key, final long delta)
    {
        return this.increment(key, -delta);
    }

    /**
     * 对缓存的数值型 key 递减
     *
     * @param key 缓存的键值
     * @return 新值
     */
    public Long decrement(final String key)
    {
        return this.decrement(key, 1);
    }

}
