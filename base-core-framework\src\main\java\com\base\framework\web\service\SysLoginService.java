package com.base.framework.web.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.config.CasConfig;
import com.base.common.config.OAuth2Config;
import com.base.common.constant.CacheConstants;
import com.base.common.constant.Constants;
import com.base.common.constant.UserConstants;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.core.domain.model.LoginUser;
import com.base.common.core.redis.RedisCache;
import com.base.common.exception.ServiceException;
import com.base.common.exception.user.*;
import com.base.common.utils.*;
import com.base.common.utils.http.HttpUtils;
import com.base.common.utils.ip.IpUtils;
import com.base.framework.manager.AsyncManager;
import com.base.framework.manager.factory.AsyncFactory;
import com.base.framework.security.context.AuthenticationContextHolder;
import com.base.system.service.ISysConfigService;
import com.base.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.security.auth.login.LoginException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysPermissionService permissionService;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            redisCache.deleteObject(verifyKey);
            if (captcha == null) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            if (!code.equalsIgnoreCase(captcha)) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    public String createToken(String username) {
        SysUser sysUser = userService.selectUserByUserName(username);
        Set<String> menuPermission = permissionService.getMenuPermission(sysUser);
        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(), sysUser, menuPermission);
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    public String casLogin(String ticket) throws UnsupportedEncodingException {
        String url = StringUtils.format("{}?ticket={}&service={}", CasConfig.getServerValidate(), ticket, URLEncoder.encode(CasConfig.getServiceUrl(), StandardCharsets.UTF_8.toString()));
        String xmlStr = HttpUtils.sendGet(url);
        Map<String, Object> xmlMap = XmlUtils.multilayerXmlToMap(xmlStr);
        HashMap<String, Object> map1 = (HashMap<String, Object>) xmlMap.get("serviceResponse");
        HashMap<String, Object> map2 = (HashMap<String, Object>) map1.get("authenticationSuccess");
        String user = map2.get("user").toString();
        HashMap<String, String> map4 = (HashMap<String, String>) map2.get("attributes");
        String certificationDate = map4.get("certificationDate");
        String credentialType = map4.get("credentialType");
        String realName = map4.get("realName");
        String isFromNewLogin = map4.get("isFromNewLogin");
        String authenticationDate = map4.get("authenticationDate");
        String authenticationMethod = map4.get("authenticationMethod");
        String expiredDate = map4.get("expiredDate");
        String successfulAuthenticationHandlers = map4.get("successfulAuthenticationHandlers");
        String appId = map4.get("appId");
        String longTermAuthenticationRequestTokenUsed = map4.get("longTermAuthenticationRequestTokenUsed");
        String userId = map4.get("userId");
        String username = map4.get("username");

        // 生成token
        return this.createToken(username);
    }

    public String oauth2Login(String code) throws LoginException {
        // 通过code clientId clientSecret 换取accessToken
        String serverLoginUrl = OAuth2Config.getServerTokenUrl() + code;
        String accessTokenRes = HttpUtils.sendGet(serverLoginUrl);
        JSONObject jsonObject = JSON.parseObject(accessTokenRes);
        String accessToken = jsonObject.get("access_token").toString();

        String username = jsonObject.get("user_id").toString();
        if (StringUtils.isBlank(accessToken)) {
            throw new LoginException("登录失败, 请联系管理员");
        }
        //与文档描写不一致, 通过token接口已可以获取登录账号, 故注释下方内容
//        // 通过accessToken 获取用户信息
//        String userInfoUrl = OAuth2Config.getServerUserInfo() + accessToken;
//        String userInfoRes = HttpUtils.sendGet(userInfoUrl);
//        JSONObject userInfo = JSON.parseObject(userInfoRes);
//        if (StringUtils.isNotBlank(userInfo.get("Message").toString())) {
//            throw new LoginException("登录失败, 请联系管理员");
//        }
//        // 生成自己的token
//        String username = userInfo.get("sAMAccountName").toString();

        return this.createToken(username);
    }

    /**
     * 验证用户信息
     *
     * @param username 用户名
     * @param password 密码
     * @param openId   OpenId
     * @return
     */
    public boolean validateUser(String username, String password, String openId) {
        boolean flag = false;
        SysUser user = userService.selectUserByUserName(username);
        if (StringUtils.isNotNull(user)) {
            boolean result = SecurityUtils.matchesPassword(password, user.getPassword());
            if (result) {
                SysUser sysUser = new SysUser();
                sysUser.setUserId(user.getUserId());
                sysUser.setUpdateTime(DateUtils.getNowDate());
                sysUser.setOpenId(openId);
                userService.updateUserOpenId(sysUser);
                flag = true;
            }
        }
        return flag;
    }

}
