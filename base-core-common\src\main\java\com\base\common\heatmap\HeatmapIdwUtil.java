package com.base.common.heatmap;

import com.alibaba.fastjson2.JSONObject;
import com.base.common.config.BaseConfig;
import com.base.common.constant.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.*;
import java.util.Map.Entry;

/**
 * 热力图数据清洗
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
public class HeatmapIdwUtil {
    private static final Logger log = LoggerFactory.getLogger(HeatmapIdwUtil.class);
    // 插值点横向像素点55
    public static int m_PixelWide = 55;
    // 权重
    public static final int m_PointWeight = -2;
    // 计算点数
    public static final int m_CalculatePointCount = 12;
    // 计算点数
    public static final int m_HaveValue = 1;
    // 计算点数
    public static final int m_NoValue = -1;

    public static int alpha = 150;
    public static Color green = new Color(0, 217, 33, alpha);
    public static Color yellow = new Color(147, 217, 35, alpha);
    public static Color orange = new Color(242, 242, 39, alpha);
    public static Color red = new Color(255, 153, 0, alpha);
    public static Color purple = new Color(255, 81, 0, alpha);
    public static Color brownRed = new Color(179, 0, 0, alpha);
    public static Color black = new Color(99, 0, 0, alpha);
    public static Color white = new Color(255, 255, 255, 0);

    /**
     * 主入口生图片方法
     *
     * @param pointList     厂界范围坐标
     * @param pointDataList 点位数据和值
     * @param pixelWide     生成图片宽度
     * @param type          监测指标：pm25、pm10、tsp
     * @param fileName      文件名称，包括路径
     * @return
     */
    public static JSONObject idwInterpolationImg(List<HeatPoint> pointList, List<HeatPointData> pointDataList, int pixelWide, String type, String fileName) {
        // List<CPoint> pointList = initCPoints(); // 范围线
        List<HeatPoint> lstScopeNew = convexPolygon(pointList);
//        List<HeatPoint> lstScope = scopeExpandByDistance(lstScopeNew, 0.002);
        List<HeatPoint> lstScope = scopeExpandByDistance(lstScopeNew, 0.0001);
        List<HeatPoint> lstScope1 = new ArrayList<HeatPoint>();
        lstScope1.addAll(lstScope);
        // 多边形，四个角坐标 小x-y 大x-y
        HeatRectangle rectangle = getRectangle(lstScope1);
        m_PixelWide = 350;
        int m_Pixelheight = (int) Math.round((rectangle.getMaxY() - rectangle.getMinY()) * m_PixelWide
                / (rectangle.getMaxX() - rectangle.getMinX()));
        int[][] matrix = new int[m_PixelWide][m_Pixelheight];
        // 裁剪图片成行政区划图的样子
        pointIsInPolygon(rectangle, lstScope, matrix);
        addPointData(pointDataList, rectangle);
        List<HeatPointData> lstOutputImg = geo2OutputImg(pointDataList, rectangle, m_PixelWide, m_Pixelheight);
        for (int i = 0; i < matrix.length; i++) {
            for (int j = 0; j < matrix[0].length; j++) {
                if (matrix[i][j] == m_NoValue) {
                    continue;
                }
                // 以图片上的距离来计算插值
                matrix[i][j] = (int) setValueByImg(i, j, lstOutputImg);
            }
        }
        // 最终的像素值
        int nWidthEx = pixelWide;
        int nHeightEx = (int) Math.round(
                (rectangle.getMaxY() - rectangle.getMinY()) * nWidthEx / (rectangle.getMaxX() - rectangle.getMinX()));
        int[][] matrixEx = imgExpand(matrix, nWidthEx, nHeightEx);

        String filePath = genNewImg(matrixEx, type, fileName);

        JSONObject result = new JSONObject();
        // 热力图生成的图片路径
        result.put("filePath", Constants.RESOURCE_PREFIX + filePath);
        // 高德坐标-左下角 X
        //result.put("leftBottomGdX", Gcj2WgsUtils.wgsLon(rectangle.getMinY(), rectangle.getMinX()));
        // 高德坐标-左下角 Y
        //result.put("leftBottomGdY", Gcj2WgsUtils.wgsLat(rectangle.getMinY(), rectangle.getMinX()));
        // 左下角 X
        result.put("leftBottomX", rectangle.getMinX());
        // 左下角 Y
        result.put("leftBottomY", rectangle.getMinY());
        // 高德坐标-右上角 X
        //result.put("topRightGdX", Gcj2WgsUtils.wgsLon(rectangle.getMaxY(), rectangle.getMaxX()));
        // 高德坐标-右上角 Y
        //result.put("topRightGdY", Gcj2WgsUtils.wgsLat(rectangle.getMaxY(), rectangle.getMaxX()));
        // 右上角 X
        result.put("topRightX", rectangle.getMaxX());
        // 右上角 Y
        result.put("topRightY", rectangle.getMaxY());

        return result;
    }

    /**
     * 生成图片
     *
     * @param matrix
     * @param type
     * @param fileName
     * @return
     */
    private static String genNewImg(int[][] matrix, String type, String fileName) {
        int imageWidth = matrix.length;
        int imageHeight = matrix[0].length;
        int nValue = -1;

        // 得到图片缓冲区，INT精确度达到一定,RGB三原色，
        BufferedImage bi = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D gd = bi.createGraphics();

        bi = gd.getDeviceConfiguration().createCompatibleImage(imageWidth, imageHeight, Transparency.TRANSLUCENT);

        for (int i = 0; i < imageWidth; i++) {
            for (int j = 0; j < imageHeight; j++) {
                nValue = matrix[i][j];
                if (nValue == m_NoValue) {
                    bi.setRGB(i, j, white.getRGB());
                    continue;
                }
                if ("pm2_5".equals(type)) {
                    // PM2.5 浓度范围
                    if (nValue <= 35) {
                        bi.setRGB(i, j, green.getRGB());
                    } else if (nValue <= 75) {
                        bi.setRGB(i, j, yellow.getRGB());
                    } else if (nValue <= 115) {
                        bi.setRGB(i, j, orange.getRGB());
                    } else if (nValue <= 150) {
                        bi.setRGB(i, j, red.getRGB());
                    } else if (nValue <= 250) {
                        bi.setRGB(i, j, purple.getRGB());
                    } else if (nValue <= 500) {
                        bi.setRGB(i, j, brownRed.getRGB());
                    } else if (nValue > 500) {
                        bi.setRGB(i, j, black.getRGB());
                    } else {
                        bi.setRGB(i, j, white.getRGB());
                    }

                } else if ("pm10".equals(type)) {
                    // PM10浓度范围
                    if (nValue <= 50) {
                        bi.setRGB(i, j, green.getRGB());
                    } else if (nValue <= 150) {
                        bi.setRGB(i, j, yellow.getRGB());
                    } else if (nValue <= 250) {
                        bi.setRGB(i, j, orange.getRGB());
                    } else if (nValue <= 350) {
                        bi.setRGB(i, j, red.getRGB());
                    } else if (nValue <= 420) {
                        bi.setRGB(i, j, purple.getRGB());
                    } else if (nValue <= 600) {
                        bi.setRGB(i, j, brownRed.getRGB());
                    } else if (nValue > 600) {
                        bi.setRGB(i, j, black.getRGB());
                    } else {
                        bi.setRGB(i, j, white.getRGB());
                    }
                } else if ("tsp".equals(type)) {
                    // TSP浓度范围
                    if (nValue <= 100) {
                        bi.setRGB(i, j, green.getRGB());
                    } else if (nValue <= 200) {
                        bi.setRGB(i, j, yellow.getRGB());
                    } else if (nValue <= 400) {
                        bi.setRGB(i, j, orange.getRGB());
                    } else if (nValue <= 600) {
                        bi.setRGB(i, j, red.getRGB());
                    } else if (nValue <= 800) {
                        bi.setRGB(i, j, purple.getRGB());
                    } else if (nValue <= 1000) {
                        bi.setRGB(i, j, brownRed.getRGB());
                    } else if (nValue > 1000) {
                        bi.setRGB(i, j, black.getRGB());
                    } else {
                        bi.setRGB(i, j, white.getRGB());
                    }
                }
            }
        }
        // 路径 /home/<USER>/code/profile
        String realPath = BaseConfig.getProfile();

        try {
            File file = new File(realPath + fileName);
            // 保存图片 JPEG表示保存格式
            ImageIO.write(bi, "PNG", new FileOutputStream(file));
        } catch (FileNotFoundException e) {
            log.error("===> 生成图片-文件路径出错", e);
        } catch (Exception e) {
            log.error("===> 生成图片出错", e);
        }
        return fileName;
    }

    /**
     * 图片重采样
     *
     * @param matrix    原始图片
     * @param nWidthEx  新图片的尺寸-宽
     * @param nHeightEx 新图片的尺寸-高
     * @return
     */
    private static int[][] imgExpand(int[][] matrix, int nWidthEx, int nHeightEx) {
        int nWidth = matrix.length;
        int nHeight = matrix[0].length;
        int[][] matrixEx = new int[nWidthEx][nHeightEx];
        int x, y = 0;
        for (int i = 0; i < nWidthEx; i++) {
            for (int j = 0; j < nHeightEx; j++) {
                x = nWidth * i / nWidthEx;
                y = nHeight * j / nHeightEx;
                // 最近距离重采样
                matrixEx[i][j] = matrix[x][y];
            }
        }
        int[][] matrixRe = new int[nWidthEx][nHeightEx];
        for (int i = 1; i < nWidthEx - 1; i++) {
            for (int j = 1; j < nHeightEx - 1; j++) {
                // 九像素求均值;平滑
                matrixRe[i][j] = (matrixEx[i - 1][j - 1] + matrixEx[i - 1][j] + matrixEx[i - 1][j + 1]
                        + matrixEx[i][j - 1] + matrixEx[i][j] + matrixEx[i][j + 1] + matrixEx[i + 1][j - 1]
                        + matrixEx[i + 1][j] + matrixEx[i + 1][j + 1]) / 9;
            }
        }
        return matrixEx;
    }

    /**
     * 增加一圈点的数据
     *
     * @param lstData
     * @param Rectangle
     */
    private static void addPointData(List<HeatPointData> lstData, HeatRectangle Rectangle) {
        if (lstData.size() <= 0) {
            return;
        }
        // lstData[0].data;
        double data = 1;
        double centX = (Rectangle.getMaxX() + Rectangle.getMinX()) / 2;
        double centY = (Rectangle.getMaxY() + Rectangle.getMinY()) / 2;
        double length = 0.5 * Math.sqrt(Math.pow(Rectangle.getMaxX() - Rectangle.getMinX(), 2)
                + Math.pow(Rectangle.getMaxY() - Rectangle.getMinY(), 2));
        HeatPointData p;
        int nPointCount = 100;
        for (int i = 0; i < nPointCount; i++) {
            p = new HeatPointData();
            p.setX(centX + length * Math.sin(2 * i * Math.PI / nPointCount));
            p.setY(centY + length * Math.cos(2 * i * Math.PI / nPointCount));
            p.setValue(data);
            lstData.add(p);
        }
    }

    /**
     * 经纬度坐标 转 输出图片的坐标
     *
     * @param lstPoint
     * @param rect
     * @param width
     * @param height
     * @return
     */
    private static List<HeatPointData> geo2OutputImg(List<HeatPointData> lstPoint, HeatRectangle rect, int width, int height) {
        double offsetGeoX = rect.getMaxX() - rect.getMinX();
        double offsetGeoY = rect.getMaxY() - rect.getMinY();
        List<HeatPointData> lstPointImg = new ArrayList<>();
        HeatPointData p;
        for (int i = 0; i < lstPoint.size(); i++) {
            p = new HeatPointData();
            p.setX((lstPoint.get(i).getX() - rect.getMinX()) * width / offsetGeoX);
            p.setY(height - 1 - (lstPoint.get(i).getY() - rect.getMinY()) * height / offsetGeoY);
            p.setValue(lstPoint.get(i).getValue());
            lstPointImg.add(p);
        }
        return lstPointImg;
    }

    /**
     * 输入 求值点已知值点串
     *
     * @param x
     * @param y
     * @param lstPT
     * @return
     */
    private static double setValueByImg(int x, int y, List<HeatPointData> lstPT) {
        // Maps.newHashMap()
        Map<HeatPointData, Double> mapData = new HashMap<>();
        for (int i = 0; i < lstPT.size(); i++) {
            double distance = Math.sqrt(Math.pow(x - lstPT.get(i).getX(), 2) + Math.pow(y - lstPT.get(i).getY(), 2));
            mapData.put(lstPT.get(i), distance);
        }
        // 按value
        // 升序排列
        List<Entry<HeatPointData, Double>> lstMap = new ArrayList<Entry<HeatPointData, Double>>(mapData.entrySet());
        Collections.sort(lstMap, new Comparator<Entry<HeatPointData, Double>>() {
            public int compare(Entry<HeatPointData, Double> p1, Entry<HeatPointData, Double> p2) {
                if (p1 == null && p2 == null) {
                    return 0;
                }
                if (p1 == null) {
                    return -1;
                }
                if (p2 == null) {
                    return 1;
                }
                if (p1.getValue() > p2.getValue()) {
                    return 1;
                }
                if (p2.getValue() > p1.getValue()) {
                    return -1;
                }
                return 0;
                // return p1.getValue() > p2.getValue() ? 1 : -1;
            }
        });
        int k = 0;
        double valueDis = 0;
        double dSensorValue = 0;

        for (Entry<HeatPointData, Double> item : lstMap) {
            k++;
            if (k > m_CalculatePointCount) {
                break;
            }
            valueDis += Math.pow(item.getValue(), m_PointWeight);
        }
        k = 0;
        for (Entry<HeatPointData, Double> item : lstMap) {
            k++;
            if (k > m_CalculatePointCount) {
                break;
            }
            dSensorValue += (item.getKey().getValue() * Math.pow(item.getValue(), m_PointWeight)) / valueDis;

        }
        BigDecimal bigDecimal = new BigDecimal(dSensorValue);
        double dValue = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
        return dValue;
    }

    private static void pointIsInPolygon(HeatRectangle rect, List<HeatPoint> lstScope, int[][] matrix) {
        // List<CPointData> lstInputData = new ArrayList<CPointData>();
//		CPointData pdata;
//		CPoint pt1;
        HeatPoint ptMin = new HeatPoint(rect.getMinX(), rect.getMinY());
        HeatPoint ptMax = new HeatPoint(rect.getMaxX(), rect.getMaxY());
        int wide = matrix.length;
        int high = matrix[0].length;
        int nTime = 0;
        int kNextIndex = 0;
        for (int i = 0; i < wide; i++) {
            for (int j = 0; j < high; j++) {
                nTime = 0;
                for (int k = 0; k < lstScope.size(); k++) {
                    kNextIndex = k + 1;
                    if (kNextIndex == lstScope.size()) {
                        kNextIndex = 0;
                    }
                    if (twoLineIsIntersect(matrix2Geo(i, j, wide, high, ptMin, ptMax), lstScope.get(k), lstScope.get(kNextIndex))) {
                        nTime++;
                    }
                }
                if (nTime % 2 == 0) {
                    // 范围外
                    matrix[i][j] = m_NoValue;
                } else {
                    // 范围内
                    matrix[i][j] = m_HaveValue;
                }
            }
        }
    }

    private static HeatPoint matrix2Geo(int i, int j, int wide, int high, HeatPoint minPoint, HeatPoint maxPoint) {
        double x = (minPoint.getX() + i * (maxPoint.getX() - minPoint.getX()) / wide);
        double y = (minPoint.getY() + (high - j) * (maxPoint.getY() - minPoint.getY()) / high);
        BigDecimal bigDecimal = new BigDecimal(x);
        double X = bigDecimal.setScale(8, BigDecimal.ROUND_HALF_UP).doubleValue();
        bigDecimal = new BigDecimal(y);
        double Y = bigDecimal.setScale(8, BigDecimal.ROUND_HALF_UP).doubleValue();
        return new HeatPoint(X, Y);
    }

    /**
     * 判断点PT0的右射线是否与直线PT1，PT2相交
     *
     * @param pt0
     * @param pt1
     * @param pt2
     * @return
     */
    private static boolean twoLineIsIntersect(HeatPoint pt0, HeatPoint pt1, HeatPoint pt2) {
        // pt0的Y值在两点之间。
        if (((pt1.getY() > pt0.getY() && pt2.getY() < pt0.getY())
                || (pt1.getY() < pt0.getY() && pt2.getY() > pt0.getY()))) {
            // 过点pt0的水平直线与线的交点X值
            if (pt2.getX() + (pt1.getX() - pt2.getX()) * (pt0.getY() - pt2.getY()) / (pt1.getY() - pt2.getY()) > pt0
                    .getX()) {
                return true;
            }
        }
        return false;

    }

    /**
     * Get Convex Polygon 获得外包凸多边形
     *
     * @param lstDataIn 点集
     * @return 返回多边形
     */
    private static List<HeatPoint> convexPolygon(List<HeatPoint> lstDataIn) {
        Map<Integer, HeatPoint> dicData = new HashMap<>();
        List<Integer> lstKey = new ArrayList<Integer>();
        int nKey = 0;
        for (int k = 0; k < lstDataIn.size(); k++) {
            dicData.put(nKey, lstDataIn.get(k));
            nKey++;
        }
        List<HeatPoint> lstConvex = new ArrayList<>();
        double yMin = dicData.get(0).getY();
        // first point index
        int nFirstPointIndex = 0;

        // foreach (KeyValuePair<int, envPointData> item in dicData)
        for (Integer key : dicData.keySet()) {
            if (dicData.get(key).getY() < yMin) {
                yMin = dicData.get(key).getY();
                nFirstPointIndex = key;
            }
        }
        // 获得Y值最小的一个点，也就是凸多边形的第一个点
        lstKey.add(nFirstPointIndex);
        // second point index
        int secPointIndex = 0;
        // A向量为（X,Y）B为单位向量(1,0)；
        // cos(A,B)=X/|A|
        double X, Y;
        double cos = 0;
        double maxCos = 0;
        for (Integer key : dicData.keySet()) {
            if (key == nFirstPointIndex) {
                continue;
            }
            X = dicData.get(key).getX() - dicData.get(nFirstPointIndex).getX();
            Y = dicData.get(key).getY() - dicData.get(nFirstPointIndex).getY();
            cos = Math.abs(X / Math.sqrt(X * X + Y * Y));
            // 求得向量与（1，0）向量的夹角的cos值的绝对值，越接近1，表示夹角越接近0度。
            if (cos > maxCos) {
                maxCos = cos;
                secPointIndex = key;
            }
        }
        // 获得凸多边形的第二个点，与第一个点的连成的直线跟水平线夹角最小
        lstKey.add(secPointIndex);
        // 迭代寻找下一个点
        iteration(dicData, lstKey, nFirstPointIndex, nFirstPointIndex, secPointIndex);
        for (int i = 0; i < lstKey.size(); i++) {
            // first point
            lstConvex.add(dicData.get(lstKey.get(i)));
        }
        // 加入第一个点first point的索引
        lstConvex.add(dicData.get(nFirstPointIndex));
        return lstConvex;
    }

    /**
     * iterate next point迭代下一个点
     *
     * @param dicData
     * @param lstKey
     * @param nFirstIndex 每一个点
     * @param nIndex      初始点的索引
     * @param nNextIndex  下一个点的索引
     */
    private static void iteration(Map<Integer, HeatPoint> dicData, List<Integer> lstKey, int nFirstIndex, int nIndex, int nNextIndex) {
        double X1, Y1, X2, Y2;
        double cos, maxCos = 1;
        int nextNextIndex = 0;
        // foreach (KeyValuePair<int, envPointData> item in dicData)
        for (Integer key : dicData.keySet()) {
            X1 = dicData.get(key).getX() - dicData.get(nNextIndex).getX();
            Y1 = dicData.get(key).getY() - dicData.get(nNextIndex).getY();
            X2 = dicData.get(nIndex).getX() - dicData.get(nNextIndex).getX();
            Y2 = dicData.get(nIndex).getY() - dicData.get(nNextIndex).getY();
            cos = (X1 * X2 + Y1 * Y2) / (Math.sqrt(X1 * X1 + Y1 * Y1) * Math.sqrt(X2 * X2 + Y2 * Y2));
            // cos夹角的余弦值 。向量a与向量b的内积。cos值越接近-1，表示夹角越大。
            if (cos < maxCos) {
                maxCos = cos;
                nextNextIndex = key;
            }
        }
        if (nextNextIndex == nFirstIndex) {
            return;
        }
        lstKey.add(nextNextIndex);
        iteration(dicData, lstKey, nFirstIndex, nNextIndex, nextNextIndex);
    }

    private static List<HeatPoint> scopeExpandByDistance(List<HeatPoint> lstScope, double dExDistance) {
        // ------|(x,y)要求的点
        // | / |
        // ---> | / |
        // 向量b(a2,b2) | /<- |(xs,ys)两个单位向量的和向量的端点。
        // ___________|/____|
        // pt2(x2,y2) |pt1(x1,y1)
        // |
        // |
        // |向量a(a1,b1) (方向从点0指向点1)
        // |pt1(x0,y0)
        List<HeatPoint> lstScopeEx = new ArrayList<HeatPoint>();// 返回的列表
        List<HeatPoint> lstScopeNew = new ArrayList<HeatPoint>();// 增加一些方便计算的
        HeatPoint pt;
        pt = new HeatPoint(lstScope.get(lstScope.size() - 2).getX(), lstScope.get(lstScope.size() - 2).getY());// 增加倒数第二个点
        lstScopeNew.add(pt);
        lstScopeNew.addAll(lstScope);
        for (int i = 1; i < lstScopeNew.size() - 1; i++) {
            double x0 = lstScopeNew.get(i - 1).getX(); // 点0
            double y0 = lstScopeNew.get(i - 1).getY();
            double x1 = lstScopeNew.get(i).getX(); // 点1
            double y1 = lstScopeNew.get(i).getY();
            double x2 = lstScopeNew.get(i + 1).getX(); // 点2
            double y2 = lstScopeNew.get(i + 1).getY();

            double a1 = x1 - x0;
            double b1 = y1 - y0;// 向量A(a1,b1)
            double a2 = x2 - x1;
            double b2 = y2 - y1;// 向量B(a2,b2)

            double a1Dis = Math.sqrt(a1 * a1 + b1 * b1);
            double a2Dis = Math.sqrt(a2 * a2 + b2 * b2);
            double xe1 = a1 / a1Dis;
            double ye1 = b1 / a1Dis;// A的单位向量（xe1,ye1）
            double xe2 = a2 / a2Dis;
            double ye2 = b2 / a2Dis;// B的单位向量（xe2,ye2）

            double a3 = xe1 - xe2;
            double b3 = ye1 - ye2;// 向量a和向量b的角平分线，向量3(a3,b3)
            if (getVectorAngle(a1, b1, a2, b2) > 0)// 两个向量的夹角
            {
                a3 = xe2 - xe1;
                b3 = ye2 - ye1;
            }
            double xs = a3 + x1;
            double ys = b3 + y1;
            // 点0和点1的方程 AX+BY+C= 0;
            // 即：b1 * x - a1 * y + a1 * y0 - b1 * x0 = 0;
            double A = b1;
            double B = -1 * a1;
            double C = a1 * y0 - b1 * x0;
            double dDis = Math.abs(A * xs + B * ys + C) / Math.sqrt(A * A + B * B);// 两个单位向量的和向量的那个点到直线的距离。

            double x = x1 + dExDistance * (xs - x1) / dDis;// 根据比例求得需要的距离的坐标
            double y = y1 + dExDistance * (ys - y1) / dDis;
            pt = new HeatPoint(x, y);
            lstScopeEx.add(pt);
        }
        pt = new HeatPoint(lstScopeEx.get(0).getX(), lstScopeEx.get(0).getY());
        lstScopeEx.add(pt);// 增加初始点
        return lstScopeEx;
    }

    /**
     * 获得两个向量的夹角是大于90度还是小于，返回值小于0则大于90度
     *
     * @param x1
     * @param y1
     * @param x2
     * @param y2
     * @return
     */
    private static double getVectorAngle(double x1, double y1, double x2, double y2) {
        double dZ = x1 * y2 - x2 * y1;
        return dZ;
    }

    /**
     * 范围线
     *
     * @param lstPoint
     * @return 外包矩形
     */
    private static HeatRectangle getRectangle(List<HeatPoint> lstPoint) {
        HeatRectangle rect = new HeatRectangle(); // 升序排序
        Collections.sort((List<HeatPoint>) lstPoint, new Comparator<HeatPoint>() {
            public int compare(HeatPoint p1, HeatPoint p2) {
                return p1.getX() > p2.getX() ? 1 : -1;
            }
        });
        rect.setMinX(lstPoint.get(0).getX());
        rect.setMaxX(lstPoint.get(lstPoint.size() - 1).getX());
        // 升序排序
        Collections.sort(lstPoint, new Comparator<HeatPoint>() {
            public int compare(HeatPoint p1, HeatPoint p2) {
                return p1.getY() > p2.getY() ? 1 : -1;
            }
        });
        rect.setMinY(lstPoint.get(0).getY());
        rect.setMaxY(lstPoint.get(lstPoint.size() - 1).getY());
        return rect;
    }

    private static List<HeatPoint> initCPoints() {
        List<HeatPoint> lstScope = new ArrayList<HeatPoint>(); // 范围线
//		CPoint p = new CPoint(110.7750208690923, 35.70708888257197);
//		lstScope.add(p);
//		p = new CPoint(110.7737681934411, 35.69627533309789);
//		lstScope.add(p);
//		p = new CPoint(110.7833305127361, 35.69723851084507);
//		lstScope.add(p);
//		p = new CPoint(110.7935250640436, 35.69371700774645);
//		lstScope.add(p);
//		p = new CPoint(110.7950488675775, 35.70465069456753);
//		lstScope.add(p);
//		p = new CPoint(110.7750208690923, 35.70708888257197);
//		lstScope.add(p);
//		return lstScope;

        HeatPoint p = new HeatPoint(110.7723560820904, 35.70313591930481);
        lstScope.add(p);
        p = new HeatPoint(110.7727519621049, 35.69707217301536);
        lstScope.add(p);
        p = new HeatPoint(110.7947827321384, 35.69466040035297);
        lstScope.add(p);
        p = new HeatPoint(110.7970225164515, 35.70209790664435);
        lstScope.add(p);
        p = new HeatPoint(110.784670226259, 35.70801722217904);
        lstScope.add(p);
        p = new HeatPoint(110.7755279793136, 35.70738270795962);
        lstScope.add(p);
        p = new HeatPoint(110.7723560820904, 35.70313591930481);
        lstScope.add(p);
        return lstScope;
    }

    private static List<HeatPointData> initCPointData() {
        // 监测数据
        List<HeatPointData> lstAirData = new ArrayList<HeatPointData>();
        HeatPointData p = new HeatPointData();
        p.setX(110.787126932333);
        p.setY(35.69895086509219);
        p.setValue(12);
        lstAirData.add(p);
        p = new HeatPointData();
        p.setX(110.7868751271302);
        p.setY(35.70006280064337);
        p.setValue(50);
        lstAirData.add(p);
        p = new HeatPointData();
        p.setX(110.7886947133439);
        p.setY(35.70014594991933);
        p.setValue(80);
        lstAirData.add(p);
        p = new HeatPointData();
        p.setX(110.7887631583557);
        p.setY(35.69891717345004);
        p.setValue(150);
        lstAirData.add(p);
        p = new HeatPointData();
        p.setX(110.7898484359897);
        p.setY(35.70110551205571);
        p.setValue(26);
        lstAirData.add(p);
        p = new HeatPointData();
        p.setX(110.7898937199077);
        p.setY(35.70111738743701);
        p.setValue(180);
        lstAirData.add(p);
        p = new HeatPointData();
        p.setX(110.7874860699438);
        p.setY(35.70236326802993);
        p.setValue(110);
        lstAirData.add(p);
        p = new HeatPointData();
        p.setX(110.7845747122301);
        p.setY(35.70309194827924);
        p.setValue(140);
        lstAirData.add(p);
        return lstAirData;
    }

    public static void main(String[] args) {
        // 范围线
        List<HeatPointData> lstAirData = initCPointData();
        idwInterpolationImg(initCPoints(), lstAirData, 1024, "pm25", "test.png");
    }
}
