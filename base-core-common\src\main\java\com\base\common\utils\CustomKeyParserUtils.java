package com.base.common.utils;

import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomKeyParserUtils {

    private static final Pattern PATTERN = Pattern.compile("\\$\\{([^}]+)}");

    public static String parse(String keyTemplate, Method method, Object[] args) {
        Matcher matcher = PATTERN.matcher(keyTemplate);

        // 解析参数名
        ParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
        String[] paramNames = discoverer.getParameterNames(method);

        Map<String, Object> paramValueMap = new HashMap<>();
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                paramValueMap.put(paramNames[i], args[i]);
            }
        }

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String expression = matcher.group(1);  // 如 user.id

            Object value = getValueFromExpression(expression, paramValueMap);
            matcher.appendReplacement(result, value != null ? value.toString() : "null");
        }
        matcher.appendTail(result);
        return result.toString();
    }

    private static Object getValueFromExpression(String expression, Map<String, Object> paramMap) {
        String[] parts = expression.split("\\.");
        Object value = paramMap.get(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            if (value == null) return null;
            try {
                Field field = value.getClass().getDeclaredField(parts[i]);
                field.setAccessible(true);
                value = field.get(value);
            } catch (Exception e) {
                return null;
            }
        }
        return value;
    }
}
