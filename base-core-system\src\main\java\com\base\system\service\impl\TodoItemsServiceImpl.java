package com.base.system.service.impl;

import com.base.common.core.domain.entity.SysMenu;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.utils.SecurityUtils;
import com.base.system.domain.TodoItems;
import com.base.system.domain.TodoReadStatus;
import com.base.system.domain.vo.TodoItemsQueryVO;
import com.base.system.domain.vo.TodoItemsVO;
import com.base.system.mapper.SysMenuMapper;
import com.base.system.mapper.TodoItemsMapper;
import com.base.system.mapper.TodoReadStatusMapper;
import com.base.system.service.ISysUserService;
import com.base.system.service.ITodoItemsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 待办事项 服务层实现
 */
@Service
public class TodoItemsServiceImpl implements ITodoItemsService {
    @Autowired
    private TodoItemsMapper todoItemsMapper;

    @Autowired
    private TodoReadStatusMapper todoReadStatusMapper;

    // 新增：注入 ISysUserService
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private SysMenuMapper sysMenuMapper;

    /**
     * 查询待办事项
     *
     * @param todoId 待办事项ID
     * @return 待办事项
     */
    @Override
    public TodoItems selectTodoItemsById(Long todoId) {
        return todoItemsMapper.selectTodoItemsById(todoId);
    }

    /**
     * 查询待办事项列表
     *
     * @param todoItems 待办事项
     * @return 待办事项集合
     */
    @Override
    public List<TodoItems> selectTodoItemsList(TodoItems todoItems) {
        return todoItemsMapper.selectTodoItemsList(todoItems);
    }

    /**
     * 新增待办事项
     *
     * @param todoItems 待办事项
     * @return 结果
     */
    @Override
    public int insertTodoItems(TodoItems todoItems) {
        return todoItemsMapper.insertTodoItems(todoItems);
    }

    /**
     * 修改待办事项
     *
     * @param todoItems 待办事项
     * @return 结果
     */
    @Override
    public int updateTodoItems(TodoItems todoItems) {
        return todoItemsMapper.updateTodoItems(todoItems);
    }

    /**
     * 查询待办事项详情
     *
     * @param todoId 待办事项ID
     * @return 待办事项详情
     */
    @Override
    public TodoItemsVO selectTodoItemsDetailById(Long todoId) {
        // 查询待办事项详情
        TodoItems todoItems = todoItemsMapper.selectTodoItemsById(todoId);
        if (todoItems == null) {
            return null;
        }

        // 查询当前用户的已读状态
        Long userId = SecurityUtils.getUserId();
        TodoReadStatus readStatus = todoReadStatusMapper.selectTodoReadStatusList(new TodoReadStatus() {{
            setUserId(userId);
            setTodoId(todoId);
        }}).stream().findFirst().orElse(null);

        // 构造 VO 对象
        TodoItemsVO todoItemsVO = new TodoItemsVO();
        todoItemsVO.setTodoId(todoItems.getTodoId());
        todoItemsVO.setTitle(todoItems.getTitle());
        todoItemsVO.setStatus(todoItems.getStatus());
        todoItemsVO.setContent(todoItems.getContent());
        todoItemsVO.setTag(todoItems.getTag());
        todoItemsVO.setSenderId(todoItems.getSenderId());
        todoItemsVO.setReceiverId(todoItems.getReceiverId());
        todoItemsVO.setRemark(todoItems.getRemark());
        todoItemsVO.setSceneType(todoItems.getSceneType());
        todoItemsVO.setProductId(todoItems.getProductId());
        todoItemsVO.setCreateTime(todoItems.getCreateTime());
        todoItemsVO.setUpdateTime(todoItems.getUpdateTime());
        todoItemsVO.setUrlParams(todoItems.getUrlParams());
        // 设置已读状态，默认为未读（0）
        todoItemsVO.setReadStatus(readStatus != null ? readStatus.getReadStatus() : 0);

        return todoItemsVO;
    }

    /**
     * 根据ID删除待办事项
     *
     * @param todoId 待办事项ID
     * @return 结果
     */
    @Override
    public int deleteTodoItemsById(Long todoId) {
        return todoItemsMapper.deleteTodoItemsById(todoId);
    }

    /**
     * 批量删除待办事项
     *
     * @param todoIds 需要删除的待办事项ID集合
     * @return 结果
     */
    @Override
    public int deleteTodoItemsByIds(Long[] todoIds) {
        return todoItemsMapper.deleteTodoItemsByIds(todoIds);
    }

    /**
     * 查询待办事项列表及其已读状态
     *
     * @return 待办事项列表及其已读状态
     */
    @Override
    public List<TodoItemsVO> selectTodoItemsListWithReadStatus(TodoItemsQueryVO queryVO) {
        Long userId = SecurityUtils.getUserId();
        queryVO.setReceiverId(userId); // 设置接收人ID

        // 查询待办事项列表（新增：传递排序字段）
        List<TodoItems> todoItemsList = todoItemsMapper.selectTodoItemsByQuery(queryVO);
        List<TodoReadStatus> readStatusList = todoReadStatusMapper.selectTodoReadStatusList(new TodoReadStatus() {{
            setUserId(userId);
        }});

        Map<Long, Integer> readStatusMap = new HashMap<>();
        for (TodoReadStatus readStatus : readStatusList) {
            readStatusMap.put(readStatus.getTodoId(), readStatus.getReadStatus());
        }

        // 查询所有菜单
        List<SysMenu> allMenus = sysMenuMapper.selectMenuTreeAll(null, null);
        Map<Long, SysMenu> menuMap = new HashMap<>();
        for (SysMenu menu : allMenus) {
            menuMap.put(menu.getMenuId(), menu);
        }

        List<TodoItemsVO> todoItemsVOList = new ArrayList<>();
        for (TodoItems items : todoItemsList) {
            TodoItemsVO todoItemsVO = new TodoItemsVO();
            todoItemsVO.setTodoId(items.getTodoId());
            todoItemsVO.setTitle(items.getTitle());
            todoItemsVO.setStatus(items.getStatus());
            todoItemsVO.setContent(items.getContent());
            todoItemsVO.setTag(items.getTag());
            todoItemsVO.setSenderId(items.getSenderId());
            todoItemsVO.setReceiverId(items.getReceiverId());
            todoItemsVO.setRemark(items.getRemark());
            todoItemsVO.setSceneType(items.getSceneType());
            todoItemsVO.setProductId(items.getProductId());
            todoItemsVO.setCreateTime(items.getCreateTime());
            todoItemsVO.setUpdateTime(items.getUpdateTime());
            todoItemsVO.setMenuId(items.getMenuId());
            todoItemsVO.setUrlParams(items.getUrlParams());
            todoItemsVO.setReadStatus(readStatusMap.getOrDefault(items.getTodoId(), 0));

            // 修改：通过注入的 ISysUserService 获取发送人名称
            SysUser senderUser = sysUserService.selectUserById(items.getSenderId());
            if (senderUser != null) {
                todoItemsVO.setSenderName(senderUser.getNickName()); // 假设用户昵称为发送人名称
            }

            // 构建完整路径
            if (items.getMenuId() != null) {
                SysMenu menu = menuMap.get(items.getMenuId());
                if (menu != null) {
                    StringBuilder pathBuilder = new StringBuilder();
                    buildFullPath(menu, menuMap, pathBuilder);
//                    if (items.getUrlParams() != null && !items.getUrlParams().isEmpty()) {
//                        pathBuilder.append("?").append(items.getUrlParams());
//                    }
                    todoItemsVO.setPath(pathBuilder.toString());
                }
            }

            todoItemsVOList.add(todoItemsVO);
        }

        return todoItemsVOList;
    }

    private void buildFullPath(SysMenu menu, Map<Long, SysMenu> menuMap, StringBuilder pathBuilder) {
        if (menu.getParentId() != null && menu.getParentId() != 0) {
            SysMenu parentMenu = menuMap.get(menu.getParentId());
            if (parentMenu != null) {
                buildFullPath(parentMenu, menuMap, pathBuilder);
            }
        }
        if (pathBuilder.length() > 0) {
            pathBuilder.append("/");
        }
        pathBuilder.append(menu.getPath());
    }

    @Override
    public List<TodoItems> selectTodoItemsByQuery(TodoItemsQueryVO queryVO) {
        return todoItemsMapper.selectTodoItemsByQuery(queryVO);
    }

    /**
     * 统计待办事项状态的任务数量
     *
     * @return 各状态任务数量统计结果
     */
    @Override
    public Map<String, Integer> getTodoStatusStatistics() {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        // 调用 Mapper 查询各状态的任务数量（传入用户ID）
        List<Map<String, Object>> result = todoItemsMapper.countTodoItemsByStatus(userId);

        // 构造返回结果
        Map<String, Integer> statistics = new HashMap<>();
        for (Map<String, Object> row : result) {
            String status = (String) row.get("status");
            Integer count = ((Number) row.get("count")).intValue();
            statistics.put(status, count);
        }

        // 如果某些状态没有数据，则补充默认值为 0
        statistics.putIfAbsent("1", 0); // 待办
        statistics.putIfAbsent("2", 0); // 在办
        statistics.putIfAbsent("3", 0); // 办结

        return statistics;
    }

}
