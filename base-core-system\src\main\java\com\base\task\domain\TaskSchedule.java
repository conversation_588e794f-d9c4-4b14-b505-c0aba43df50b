package com.base.task.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 任务进度对象 task_schedule
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@ApiModel("任务进度对象")
public class TaskSchedule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 任务Id
     */
    @Excel(name = "任务Id")
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 描述
     */
    @Excel(name = "描述")
    @ApiModelProperty("描述")
    private String description;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     * 状态：0进行中，1已完成，2未解决
     */
    @ApiModelProperty("状态：0进行中，1已完成，2未解决")
    private Integer status;

    /**
     * 执行人名称
     */
    @ApiModelProperty("执行人名称")
    private String userName;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String phoneNumber;

    /**
     * 执行人所属部门名称
     */
    @ApiModelProperty("执行人所属部门名称")
    private String deptName;

    /**
     * 任务进度文件列表
     */
    private List<TaskScheduleFile> taskScheduleFileList;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public List<TaskScheduleFile> getTaskScheduleFileList() {
        return taskScheduleFileList;
    }

    public void setTaskScheduleFileList(List<TaskScheduleFile> taskScheduleFileList) {
        this.taskScheduleFileList = taskScheduleFileList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("taskId", getTaskId())
                .append("description", getDescription())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("remark", getRemark())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
