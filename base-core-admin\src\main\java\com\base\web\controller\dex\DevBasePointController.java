package com.base.web.controller.dex;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.poi.ExcelUtil;
import com.base.dex.domain.DevBasePoint;
import com.base.dex.service.IDevBasePointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备点Controller
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@RestController
@RequestMapping("/system/point")
public class DevBasePointController extends BaseController
{
    @Autowired
    private IDevBasePointService devBasePointService;

    /**
     * 分页查询设备点列表
     */
    @PreAuthorize("@ss.hasPermi('system:point:list')")
    @RequestMapping("/page")
    public TableDataInfo page(@RequestBody DevBasePoint devBasePoint)
    {
        startPage();
        List<DevBasePoint> list = devBasePointService.selectDevBasePointList(devBasePoint);
        return getDataTable(list);
    }

    /**
     * 查询设备点列表
     */
    @PreAuthorize("@ss.hasPermi('system:point:list')")
    @RequestMapping("/list")
    public AjaxResult list(@RequestBody DevBasePoint devBasePoint)
    {
        List<DevBasePoint> list = devBasePointService.selectDevBasePointList(devBasePoint);
        return success(list);
    }

    /**
     * 导出设备点列表
     */
    @PreAuthorize("@ss.hasPermi('system:point:export')")
    @Log(title = "设备点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DevBasePoint devBasePoint)
    {
        List<DevBasePoint> list = devBasePointService.selectDevBasePointList(devBasePoint);
        ExcelUtil<DevBasePoint> util = new ExcelUtil<DevBasePoint>(DevBasePoint.class);
        util.exportExcel(response, list, "设备点数据");
    }

    /**
     * 获取设备点详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:point:query')")
    @GetMapping(value = "get")
    public AjaxResult getInfo(@RequestParam("pointId") String pointId)
    {
        return success(devBasePointService.selectDevBasePointByPointId(pointId));
    }

    /**
     * 新增设备点
     */
    @PreAuthorize("@ss.hasPermi('system:point:add')")
    @Log(title = "设备点", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody DevBasePoint devBasePoint)
    {
        return toAjax(devBasePointService.insertDevBasePoint(devBasePoint));
    }

    /**
     * 修改设备点
     */
    @PreAuthorize("@ss.hasPermi('system:point:edit')")
    @Log(title = "设备点", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody DevBasePoint devBasePoint)
    {
        return toAjax(devBasePointService.updateDevBasePoint(devBasePoint));
    }

    /**
     * 删除设备点
     */
    @PreAuthorize("@ss.hasPermi('system:point:remove')")
    @Log(title = "设备点", businessType = BusinessType.DELETE)
	@GetMapping("delete")
    public AjaxResult remove(@RequestParam("pointId") String pointId)
    {
        return toAjax(devBasePointService.deleteDevBasePointByPointId(pointId));
    }
}
