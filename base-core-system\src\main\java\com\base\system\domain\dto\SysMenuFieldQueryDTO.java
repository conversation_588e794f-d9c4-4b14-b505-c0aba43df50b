package com.base.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class SysMenuFieldQueryDTO {

    @JsonAlias("menu_id")
    @NotNull(message = "查询菜单不能为空")
    private Long menuId;

    @JsonAlias("field_key")
    @NotNull(message = "查询内容不能为空")
    private Object fieldKey;

    @JsonAlias("query_all")
    private boolean queryAll = false;
}
