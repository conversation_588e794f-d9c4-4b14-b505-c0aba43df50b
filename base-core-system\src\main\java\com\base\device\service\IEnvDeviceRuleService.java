package com.base.device.service;

import java.util.List;
import com.base.device.domain.EnvDeviceRule;

/**
 * 设备运行规则(运行状态、报警等判断逻辑)Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IEnvDeviceRuleService
{
    /**
     * 查询设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param ruleId 设备运行规则(运行状态、报警等判断逻辑)主键
     * @return 设备运行规则(运行状态、报警等判断逻辑)
     */
    public EnvDeviceRule selectEnvDeviceRuleByRuleId(Long ruleId);

    /**
     * 查询设备运行规则(运行状态、报警等判断逻辑)列表
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 设备运行规则(运行状态、报警等判断逻辑)集合
     */
    public List<EnvDeviceRule> selectEnvDeviceRuleList(EnvDeviceRule envDeviceRule);

    /**
     * 新增设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 结果
     */
    public int insertEnvDeviceRule(EnvDeviceRule envDeviceRule);

    /**
     * 批量新增设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 结果
     */
    public int batchEnvDeviceRule(List<EnvDeviceRule> envDeviceRuleList);

    /**
     * 修改设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param envDeviceRule 设备运行规则(运行状态、报警等判断逻辑)
     * @return 结果
     */
    public int updateEnvDeviceRule(EnvDeviceRule envDeviceRule);

    /**
     * 批量删除设备运行规则(运行状态、报警等判断逻辑)
     *
     * @param ruleIds 需要删除的设备运行规则(运行状态、报警等判断逻辑)主键集合
     * @return 结果
     */
    public int deleteEnvDeviceRuleByRuleIds(Long[] ruleIds);

    /**
     * 删除设备运行规则(运行状态、报警等判断逻辑)信息
     *
     * @param ruleId 设备运行规则(运行状态、报警等判断逻辑)主键
     * @return 结果
     */
    public int deleteEnvDeviceRuleByRuleId(Long ruleId);

    List<EnvDeviceRule> selectByDeviceIdIn(List<Long> deviceIds);
}
