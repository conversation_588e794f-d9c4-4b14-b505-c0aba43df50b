package com.base.common.annotation;

import com.base.common.enums.HttpMethod;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2024/10/16
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface HttpClient {
    String path();         // 请求路径

    HttpMethod method() default HttpMethod.GET; // 请求类型，默认是 GET
}
