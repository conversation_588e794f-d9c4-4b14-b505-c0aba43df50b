package com.base.system.mapper;

import com.base.common.core.domain.entity.SysSceneDict;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 菜单-字典 关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Repository
public interface SysSceneDictMapper {
    /**
     * 查询菜单-字典 关联关系
     *
     * @param joinId 菜单-字典 关联关系主键
     * @return 菜单-字典 关联关系
     */
    public SysSceneDict selectSysSceneDictByJoinId(Long joinId);

    /**
     * 查询菜单-字典 关联关系列表
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 菜单-字典 关联关系集合
     */
    public List<SysSceneDict> selectSysSceneDictList(SysSceneDict sysSceneDict);

    /**
     * 新增菜单-字典 关联关系
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 结果
     */
    public int insertSysSceneDict(SysSceneDict sysSceneDict);

    /**
     * 修改菜单-字典 关联关系
     *
     * @param sysSceneDict 菜单-字典 关联关系
     * @return 结果
     */
    public int updateSysSceneDict(SysSceneDict sysSceneDict);

    /**
     * 删除菜单-字典 关联关系
     *
     * @param joinId 菜单-字典 关联关系主键
     * @return 结果
     */
    public int deleteSysSceneDictByJoinId(Long joinId);

    /**
     * 批量删除菜单-字典 关联关系
     *
     * @param joinIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysSceneDictByJoinIds(Long[] joinIds);

    /**
     * 根据场景和字典类型删除记录
     *
     * @param scene    场景标识
     * @param dictType 字典类型
     * @return 受影响的行数
     */
    int deleteBySceneAndDictType(@Param("scene") String scene, @Param("dictType") String dictType);

    /**
     * 批量新增菜单-字典 关联关系
     *
     * @param sysSceneDictList 菜单-字典 关联关系列表
     * @return 结果
     */
    public int batchSysSceneDict(List<SysSceneDict> sysSceneDictList);


    /**
     * 根据scene数组查询菜单-字典 关联关系列表
     *
     * @param scenes 场景数组
     * @return 菜单-字典 关联关系集合
     */
    public List<SysSceneDict> selectSysSceneDictListByScenes(@Param("scenes") List<String> scenes);

    List<SysSceneDict> findByChildTypeAndValue(@Param("childType") String childType,@Param("childValue") String childValue);
}
