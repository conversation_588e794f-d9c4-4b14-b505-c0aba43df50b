package com.base.web.controller.todo;

import com.base.common.core.domain.AjaxResult;
import com.base.common.utils.SecurityUtils;
import com.base.system.domain.TodoReadStatus;
import com.base.system.domain.vo.TodoItemsQueryVO;
import com.base.system.domain.vo.TodoItemsVO;
import com.base.system.service.ITodoItemsService;
import com.base.system.service.ITodoReadStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 待办事项 控制层
 */
@RestController
@RequestMapping("/todo")
public class TodoController {

    @Autowired
    private ITodoItemsService todoItemsService;

    @Autowired
    private ITodoReadStatusService todoReadStatusService;

    /**
     * 查询当前用户的待办事项列表
     *
     * @return 待办事项列表
     */
    @GetMapping("/list")
    public AjaxResult list(String status, String keyword, String orderBy) {
        TodoItemsQueryVO queryVO = new TodoItemsQueryVO();
        queryVO.setStatus(status);
        queryVO.setKeyword(keyword);
        queryVO.setOrderBy(orderBy); // 新增：设置排序字段
        List<TodoItemsVO> list = todoItemsService.selectTodoItemsListWithReadStatus(queryVO);
        return AjaxResult.success(list);
    }

    /**
     * 查看待办事项详情
     *
     * @param todoId 待办事项ID
     * @return 待办事项详情
     */
    @GetMapping("/detail/{todoId}")
    public AjaxResult getTodoItemDetail(@PathVariable Long todoId) {
        TodoItemsVO detail = todoItemsService.selectTodoItemsDetailById(todoId);
        return detail != null ? AjaxResult.success(detail) : AjaxResult.error("未找到待办事项");
    }

    /**
     * 新增待办事项
     *
     * @param todoItems 待办事项信息
     * @return 结果
     */
    @PostMapping("/add")
    public AjaxResult addTodoItem(@RequestBody TodoItemsVO todoItems) {
        // 设置默认值
        todoItems.setStatus("0"); // 默认状态为未完成
        todoItems.setSenderId(SecurityUtils.getUserId()); // 默认发送人为当前用户

        // 调用服务层新增方法
        int result = todoItemsService.insertTodoItems(todoItems);

        return result > 0 ? AjaxResult.success("新增成功") : AjaxResult.error("新增失败");
    }

    /**
     * 根据ID删除待办事项
     *
     * @param todoId 待办事项ID
     * @return 操作结果
     */
    @PostMapping("/delete/{todoId}")
    public AjaxResult deleteTodoItemById(@PathVariable Long todoId) {
        int result = todoItemsService.deleteTodoItemsById(todoId);
        return result > 0 ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
    }

    /**
     * 标记待办事项为已读
     *
     * @param todoId 待办事项ID
     * @return 操作结果
     */
    @PostMapping("/markAsRead/{todoId}")
    public AjaxResult markTodoItemAsRead(@PathVariable Long todoId) {
        Long userId = SecurityUtils.getUserId();
        TodoReadStatus readStatus = todoReadStatusService.selectTodoReadStatusList(new TodoReadStatus() {{
            setUserId(userId);
            setTodoId(todoId);
        }}).stream().findFirst().orElse(null);

        if (readStatus == null) {
            // 如果没有记录，则新增一条已读记录
            readStatus = new TodoReadStatus();
            readStatus.setUserId(userId);
            readStatus.setTodoId(todoId);
            readStatus.setReadStatus(1);
            readStatus.setReadTime(new Date());
            todoReadStatusService.insertTodoReadStatus(readStatus);
        } else {
            // 如果已有记录，则更新为已读状态
            readStatus.setReadStatus(1);
            readStatus.setReadTime(new Date());
            todoReadStatusService.updateTodoReadStatus(readStatus);
        }

        return AjaxResult.success("标记为已读成功");
    }

    /**
     * 统计待办事项状态的任务数量
     *
     * @return 各状态任务数量统计结果
     */
    @GetMapping("/statistics/status")
    public AjaxResult getTodoStatusStatistics() {
        Map<String, Integer> statistics = todoItemsService.getTodoStatusStatistics();
        return AjaxResult.success(statistics);
    }
}
