package com.base.web.controller.system;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.domain.entity.SysSceneDict;
import com.base.common.enums.BusinessType;
import com.base.common.utils.DictUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.system.domain.dto.SysSceneRefreshDTO;
import com.base.system.service.ISysDictDataService;
import com.base.system.service.ISysSceneDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 菜单-字典 关联关系Controller
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@RestController
@RequestMapping("/system/scene/dict")
public class SysSceneDictController extends BaseController
{
    @Autowired
    private ISysSceneDictService sysSceneDictService;

    @Autowired
    ISysDictDataService sysDictDataService;


    /**
     * 查询菜单-字典 关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/list")
    public AjaxResult list(SysSceneDict sysSceneDict)
    {
        List<SysSceneDict> list = sysSceneDictService.selectSysSceneDictList(sysSceneDict);
        return AjaxResult.success(list);
    }

    /**
     * 导出菜单-字典 关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:dict:export')")
    @Log(title = "菜单-字典 关联关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSceneDict sysSceneDict)
    {
        List<SysSceneDict> list = sysSceneDictService.selectSysSceneDictList(sysSceneDict);
        ExcelUtil<SysSceneDict> util = new ExcelUtil<SysSceneDict>(SysSceneDict.class);
        util.exportExcel(response, list, "菜单-字典 关联关系数据");
    }

    /**
     * 获取菜单-字典 关联关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{joinId}")
    public AjaxResult getInfo(@PathVariable("joinId") Long joinId)
    {
        return success(sysSceneDictService.selectSysSceneDictByJoinId(joinId));
    }

    /**
     * 新增菜单-字典 关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "菜单-字典 关联关系", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(SysSceneDict sysSceneDict)
    {
        return toAjax(sysSceneDictService.insertSysSceneDict(sysSceneDict));
    }

    /**
     * 修改菜单-字典 关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "菜单-字典 关联关系", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody SysSceneDict sysSceneDict)
    {
        return toAjax(sysSceneDictService.updateSysSceneDict(sysSceneDict));
    }

    /**
     * 删除菜单-字典 关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "菜单-字典 关联关系", businessType = BusinessType.DELETE)
	@GetMapping("delete/{joinIds}")
    public AjaxResult remove(@PathVariable Long[] joinIds)
    {
        return toAjax(sysSceneDictService.deleteSysSceneDictByJoinIds(joinIds));
    }

    /**
     * 刷新关联数据接口
     * 该接口用于刷新场景与字典项之间的关联关系，通过删除旧的关联关系并建立新的关联
     * 主要用于同步字典项的更新到特定的场景中
     *
     * @param refreshDTO 包含刷新所需信息的数据传输对象，包括场景标识、字典类型和字典代码列表
     * @return 返回操作结果，成功则返回成功信息
     */
    @PostMapping("refresh_data")
    @Log(title = "菜单-字典 关联关系", businessType = BusinessType.DELETE)
    public AjaxResult refreshData(@RequestBody SysSceneRefreshDTO refreshDTO){
        // 调用服务层方法刷新数据，传入场景标识、字典类型和字典代码列表
        sysSceneDictService.refreshData(refreshDTO.getScene(), refreshDTO.getDictType(), refreshDTO.getDicCodeList());
        // 返回成功结果
        return AjaxResult.success();
    }

    /**
     * 查询数据字典关联列表
     */
    @GetMapping("/joinList")
    public AjaxResult dictDataList(@RequestParam(required = false) String scene) {
        List<SysDictData> dictAllList = DictUtils.selectDictData(Arrays.asList("car_std_type", "out_transport"), scene);

        if (!"clean_material_ratio".equals(scene)) {
            List<SysDictData> dictSceneList = DictUtils.selectDictData(Arrays.asList("car_std_type", "out_transport"), "clean_material_ratio");

            // 创建场景字典的join_id映射表 {dict_code: join_id}
            Map<Long, Long> sceneJoinMap = new HashMap<>();
            for (SysDictData item : dictSceneList) {
                if (item.getJoinId() != null) {
                    sceneJoinMap.put(item.getDictCode(), item.getJoinId());
                }
            }

            // 合并join_id到主列表
            for (SysDictData item : dictAllList) {
                if (sceneJoinMap.containsKey(item.getDictCode())) {
                    // 优先使用场景指定的join_id
                    item.setJoinId(sceneJoinMap.get(item.getDictCode()));
                }
            }
        }

        return AjaxResult.success(dictAllList);
    }
}
