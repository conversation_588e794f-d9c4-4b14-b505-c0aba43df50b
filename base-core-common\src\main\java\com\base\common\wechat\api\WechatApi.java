package com.base.common.wechat.api;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.base.common.constant.Constants;
import com.base.common.core.redis.RedisCache;
import com.base.common.utils.DateUtils;
import com.base.common.utils.StringUtils;
import com.base.common.utils.http.HttpUtils;
import com.base.common.wechat.config.WechatConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 微信接口
 *
 * <AUTHOR>
 * @date 2024-09-23 16:08
 */
@Component
public class WechatApi {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 微信接口token
     */
    private static String apiToken = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";

    /**
     * 企业微信发送应用消息
     */
    private static String qySendMsgUrl = "https://qyapi.weixin.qq.com/cgi-bin/message/send";

    @Autowired
    private WechatConfig wechatConfig;

    @Autowired
    private RedisCache redisService;

    /**
     * 获取企业微信token
     * 企业微信AccessToken获取方式：1、密钥模式，2、接口，默认为1
     *
     * @return accessToken
     */
    public String getAccessToken() {
        String token = null;
        // 企业微信AccessToken获取方式：1、密钥模式，2、接口，默认为1
        if (StringUtils.isNotEmpty(wechatConfig.getAccessTokenMode()) && "2".equals(wechatConfig.getAccessTokenMode())) {
            token = getAccessTokenByUrl();
        } else {
            token = getAccessTokenBySecret();
        }
        return token;
    }

    /**
     * 通过密钥获取企业微信token
     *
     * @return accessToken
     */
    public String getAccessTokenBySecret() {
        String key = Constants.QY_WECHAT_TOKEN_KEY;
        String token = redisService.getCacheObject(key);
        if (StringUtils.isEmpty(token)) {
            String result = HttpUtils.sendGet(apiToken, "corpid=" + wechatConfig.getCorpId() + "&corpsecret=" + wechatConfig.getCorpSecret());
            String accessToken = JSONObject.parseObject(result).getString("access_token");
            redisService.setCacheObject(key, accessToken, 100, TimeUnit.MINUTES);
            token = accessToken;
        }
        return token;
    }

    /**
     * 通过接口获取企业微信AccessToken
     *
     * @return accessToken
     */
    public String getAccessTokenByUrl() {
        String token = null;
        try {
            if (StringUtils.isNotEmpty(wechatConfig.getAccessTokenUrl())) {
                logger.info("==========> 通过接口获取企业微信AccessToken，接口地址：{}", wechatConfig.getAccessTokenUrl());
                String result = HttpUtils.sendGet(wechatConfig.getAccessTokenUrl());
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (StringUtils.isNotEmpty(jsonObject.getJSONObject("result"))) {
                    JSONObject obj = jsonObject.getJSONObject("result");
                    token = obj != null ? obj.getString("access_token") : null;
                }
            }
        } catch (Exception e) {
            logger.error("=====> 通过接口获取企业微信AccessToken出错", e);
        }
        logger.info("==========> 通过接口获取企业微信AccessToken，token={}", token);
        return token;
    }

    /**
     * 微信公众号发送任务消息
     *
     * @param openId     OpenId
     * @param taskId     任务id
     * @param taskName   任务名称
     * @param endTime    截止时间
     * @param commitName 提交人名称
     * @param appWebUrl  app前端地址
     * @return
     */
    public String sendTaskMsg(String openId, Long taskId, String taskName, Date endTime, String commitName, String appWebUrl) {
        if (StringUtils.isEmpty(openId)) {
            return null;
        }
        String res = null;
        try {
            Map<String, Object> dataMap = new HashMap<>(16);
            // 项目名称，如果项目名称过20字节 微信消息发送失败
            if (StringUtils.isNotEmpty(taskName)) {
                if (taskName.length() > 20) {
                    taskName = taskName.substring(0, 19);
                }
            }
            Map<String, Object> m1 = new HashMap<>(16);
            m1.put("value", taskName);
            m1.put("color", "#173177");
            dataMap.put("thing17", m1);
            // 提交人
            Map<String, Object> m2 = new HashMap<>(16);
            m2.put("value", commitName);
            m2.put("color", "#173177");
            dataMap.put("thing14", m2);
            // 截止时间
            Map<String, Object> m3 = new HashMap<>();
            m3.put("value", endTime == null ? "" : DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));
            m3.put("color", "#ff0000");
            dataMap.put("time18", m3);
            // 跳转页面地址-查看详情
            dataMap.put("goto_url", appWebUrl + "pages/views/task/taskDetail?taskId=" + taskId);

            Map<String, Object> paramMap = new HashMap<>(16);
            // 企业编号
            paramMap.put("corp_code", wechatConfig.getCorpId());
            // 微信-接收者openid
            paramMap.put("openids", openId);
            // 待处理工单 work_order_wait=-0eETqdIjR2Ttxz4-cmk2Yst8VNGbFqVENrH3ZKiwG0
            paramMap.put("template_type", "work_order_wait");
            // 模板数据
            paramMap.put("data", dataMap);

            String url = "https://wx.envst.cn/api/sending_template";
            String paramString = JSON.toJSONString(paramMap);
            logger.info("新增任务消息通知 body={}", paramString);
            res = HttpUtil.post(url, paramString);
            logger.info("新增任务消息通知 res={}", res);
        } catch (Exception e) {
            logger.error("新增任务消息通知出错", e);
        }
        return res;
    }

    /**
     * 企业微信-发送任务消息
     *
     * @param qyUserId   企业微信用户ID
     * @param taskId     任务id
     * @param taskName   任务名称
     * @param endTime    截止时间
     * @param commitName 提交人名称
     * @param appWebUrl  app前端地址
     * @return
     */
    public String sendTaskQyMsg(String qyUserId, Long taskId, String taskName, Date endTime, String commitName, String appWebUrl) {
        if (StringUtils.isEmpty(qyUserId)) {
            return null;
        }
        String res = null;
        try {
            Map<String, Object> textcardMap = new HashMap<>(16);
            textcardMap.put("title", "待处理工单提醒");
            String description = "项目名称：" + taskName;
            description += " \n提交人：" + commitName;
            description += " \n截止日期：";
            if (endTime != null) {
                description += DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime);
            }
            textcardMap.put("description", description);
            // 跳转页面地址-查看详情
            textcardMap.put("url", appWebUrl + "pages/views/task/taskDetail?taskId=" + taskId);
            textcardMap.put("btntxt", "查看详情");

            Map<String, Object> paramMap = new HashMap<>(16);
            // 消息类型：textcard 文本卡片消息
            paramMap.put("msgtype", "textcard");
            paramMap.put("agentid", wechatConfig.getAgentId());
            // 微信-接收者 qyUserId
            paramMap.put("touser", qyUserId);
            // 数据
            paramMap.put("textcard", textcardMap);

            String paramString = JSON.toJSONString(paramMap);
            logger.info("新增企业微信任务消息通知 body={}", paramString);
            String url = qySendMsgUrl + "?access_token=" + getAccessToken();
            res = HttpUtil.post(url, paramString);
            logger.info("新增企业任务消息通知 res={}", res);

        } catch (Exception e) {
            logger.error("新增企业任务消息通知出错", e);
        }
        return res;
    }

    /**
     * 企业微信-发送报警消息
     *
     * @param qyUserId    企业微信用户ID
     * @param title       消息标题，如：设备报警通知
     * @param description 消息内容，多个内容，使用" \n"换行，如：设备编号：京A1111 \n设备名称：京A1111 \n报警内容：排放标准未达标 \n报警时间：2024-11-25 11:57:49
     * @param detailUrl   跳转详情路径，如："http://localhost:28081/env-app/#/pages/views/alarm/alarmdetail?alarm_id=31809b33-edc3-4b57-bbba-4dde492b35e1&alarm_code=alarm_device"
     * @return
     */
    public String sendAlarmQyMsg(String qyUserId, String title, String description, String detailUrl) {
        if (StringUtils.isEmpty(qyUserId)) {
            return null;
        }
        String res = null;
        try {
            Map<String, Object> textcardMap = new HashMap<>(16);
            textcardMap.put("title", title);
            textcardMap.put("description", description);
            // 跳转页面地址-查看详情
            textcardMap.put("url", detailUrl);
            textcardMap.put("btntxt", "查看详情");
            Map<String, Object> paramMap = new HashMap<>(16);
            // 消息类型：textcard 文本卡片消息
            paramMap.put("msgtype", "textcard");
            paramMap.put("agentid", wechatConfig.getAgentId());
            // 微信-接收者 qyUserId
            paramMap.put("touser", qyUserId);
            // 数据
            paramMap.put("textcard", textcardMap);
            String paramString = JSON.toJSONString(paramMap);
            logger.info("企业微信发送报警消息参数 body={}", paramString);
            String url = qySendMsgUrl + "?access_token=" + getAccessToken();
            res = HttpUtil.post(url, paramString);
            logger.info("企业微信发送报警消息成功消息 res={}", res);
        } catch (Exception e) {
            logger.error("企业微信发送报警消息通知出错", e);
        }
        return res;
    }

    /**
     * 根据手机号获取userid
     *
     * @param mobile 手机号
     * @return 企业微信id
     */
    public String getUserId(String mobile) {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token=" + getAccessToken();
            Map<String, Object> paramMap = new HashMap<>(16);
            paramMap.put("mobile", mobile);
            String paramString = JSON.toJSONString(paramMap);
            logger.info("根据手机号获取userid body={}", paramString);
            String result = HttpUtil.post(url, paramString);
            logger.info("根据手机号获取userid result={}", result);
            if (StringUtils.isNotEmpty(result)) {
                JSONObject object = JSONObject.parseObject(result);
                if (StringUtils.isNotEmpty(object) && 0 == object.getInteger("errcode")) {
                    return object.getString("userid");
                }
            }
        } catch (Exception e) {
            logger.error("根据手机号获取userid出错", e);
        }
        return null;
    }

}
