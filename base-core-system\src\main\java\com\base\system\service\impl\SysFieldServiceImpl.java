package com.base.system.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.base.common.annotation.RedisCacheAnno;
import com.base.common.core.domain.entity.FastExcelDTO;
import com.base.common.core.domain.entity.SysDept;
import com.base.common.core.domain.entity.SysDictData;
import com.base.common.core.redis.RedisCache;
import com.base.common.exception.ServiceException;
import com.base.common.utils.*;
import com.base.common.utils.bean.BeanUtils;
import com.base.common.utils.reflect.ReflectUtils;
import com.base.common.utils.spring.SpringUtils;
import com.base.common.utils.uuid.UUID;
import com.base.device.domain.EnvDevice;
import com.base.device.mapper.EnvDeviceMapper;
import com.base.device.service.impl.EnvDeviceServiceImpl;
import com.base.system.domain.SysField;
import com.base.system.domain.SysMenuField;
import com.base.system.domain.SysProcess;
import com.base.system.mapper.SysFieldMapper;
import com.base.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 动态字段Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
@Slf4j
public class SysFieldServiceImpl implements ISysFieldService
{
    @Autowired
    private SysFieldMapper sysFieldMapper;

    @Autowired
    private ISysProcessService sysProcessService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysMenuFieldService sysMenuFieldService;

    @Autowired
    private ISysConfigService sysConfigService;

    public final String SYS_FIELD_CACHE_KEY = "SysFieldCache:";

    @Autowired
    private EnvDeviceMapper envDeviceMapper;

    /**
     * 查询动态字段
     *
     * @param fieldId 动态字段主键
     * @return 动态字段
     */
    @Override
    public SysField selectSysFieldByFieldId(Long fieldId)
    {
        return sysFieldMapper.selectSysFieldByFieldId(fieldId);
    }

    @Autowired
    private RedisCache redisCache;


    /**
     * 查询动态字段列表
     *
     * @param sysField 动态字段
     * @return 动态字段
     */
    @Override
    public List<SysField> selectSysFieldList(SysField sysField)
    {
        return sysFieldMapper.selectSysFieldList(sysField);
    }

    /**
     * 新增动态字段
     *
     * @param sysField 动态字段
     * @return 结果
     */
    @Override
    public SysField insertSysField(SysField sysField)
    {
        return this.insertSysField(sysField, "field_json.");
    }

    /**
     * 添加动态字段
     *
     * @param fieldItem 动态字段对象
     * @param fieldPrefix 字段前缀，默认为 "field_json."
     * @return 添加后的动态字段对象
     */
    @Override
    public SysField insertSysField(SysField fieldItem, String fieldPrefix) {
        if (fieldPrefix == null || fieldPrefix.isEmpty()) {
            fieldPrefix = "field_json.";
        }

        fieldItem.setFieldId(null);
        fieldItem.setFieldValue(getFieldValue(fieldItem, fieldPrefix));
        validFieldLabel(fieldItem);
        clearFieldCache(fieldItem.getFieldKey());
        fieldItem.setCreateTime(DateUtils.getNowDate());
        fieldItem.setCreateBy(SecurityUtils.getUsername());
        this.clearFieldCache(fieldItem.getFieldKey());
        return sysFieldMapper.insertSysField(fieldItem) > 0 ? fieldItem : null;
    }

    @Override
    public int updateSysField(SysField sysField)
    {
        return this.updateSysField(sysField, "field_json.");
    }

    /**
     * 修改动态字段
     *
     * @param sysField 动态字段
     * @return 结果
     */
    @Override
    public int updateSysField(SysField sysField, String fieldPrefix)
    {
        sysField.setFieldValue(getFieldValue(sysField, fieldPrefix));
        validFieldLabel(sysField);
        clearFieldCache(sysField.getFieldKey());
        sysField.setUpdateTime(DateUtils.getNowDate());
        sysField.setUpdateBy(SecurityUtils.getUsername());
        this.clearFieldCache(sysField.getFieldKey());
        return sysFieldMapper.updateSysField(sysField);
    }

    /**
     * 批量删除动态字段
     *
     * @param fieldIds 需要删除的动态字段主键
     * @return 结果
     */
    @Override
    public int deleteSysFieldByFieldIds(Long[] fieldIds)
    {
        return sysFieldMapper.deleteSysFieldByFieldIds(fieldIds);
    }

    /**
     * 删除动态字段信息
     *
     * @param fieldId 动态字段主键
     * @return 结果
     */
    @Override
    public int deleteSysFieldByFieldId(Long fieldId)
    {
        if (fieldId == null) {
            throw new IllegalArgumentException("fieldId 不能为空");
        }

        SysField sysField = this.selectSysFieldByFieldId(fieldId);
        if (sysField == null) {
            throw new ServiceException("删除失败，动态字段不存在");
        }

        if (sysField.getFieldRequired().equals(1L)) {
            throw new ServiceException("删除失败, 通用字段不支持删除");
        }

        return sysFieldMapper.deleteSysFieldByFieldId(fieldId);
    }


    /**
     * 获取字段值
     *
     * @param fieldItem 动态字段对象
     * @param fieldPrefix 字段前缀
     * @return 字段值
     */
    private String getFieldValue(SysField fieldItem, String fieldPrefix) {
        // 校验field_label是否已经有相同的, 如果有, 则返回旧的field_value
        SysField queryField = new SysField();
        queryField.setFieldLabel(fieldItem.getFieldLabel());
        List<SysField> oldFields = sysFieldMapper.selectSysFieldList(queryField);

        if (!oldFields.isEmpty()) {
            for (SysField oldField : oldFields) {
                if (oldField.getFieldKey().split("_")[0].equals(fieldItem.getFieldKey().split("_")[0])) {
                    return oldField.getFieldValue();
                }
            }
            return oldFields.get(0).getFieldValue();
        }

        if (fieldItem.getFieldId() != null) {
            // 如果是更新时, 则不改变旧值
            return sysFieldMapper.selectSysFieldByFieldId(fieldItem.getFieldId()).getFieldValue();
        }

        // 如果是新增, 还没有field_value, 则自动生成uuid字符串
        return fieldPrefix + UUID.randomUUID();
    }

    /**
     * 验证字段标签
     *
     * @param fieldItem 动态字段对象
     */
    private void validFieldLabel(SysField fieldItem) {
        // 校验field_label是否已经有相同的, 如果有则抛出异常
        SysField queryField = new SysField();
        queryField.setFieldKey(fieldItem.getFieldKey());
        queryField.setFieldLabel(fieldItem.getFieldLabel());
        List<SysField> oldFields = sysFieldMapper.selectSysFieldList(queryField);

        if (!oldFields.isEmpty()) {
            for (SysField oldField : oldFields) {
                if (!oldField.getFieldId().equals(fieldItem.getFieldId())) {
                    throw new IllegalArgumentException("不能添加相同名称的属性");
                }
            }
        }
    }

    /**
     * 清理字段缓存
     *
     * @param fieldKey 字段键
     */
    private void clearFieldCache(String fieldKey) {
        // 实现清理字段缓存的逻辑
        redisCache.deleteObject(SYS_FIELD_CACHE_KEY + fieldKey);
        redisCache.deleteObject(SYS_FIELD_CACHE_KEY + fieldKey.split("_")[0]);
        redisCache.deleteObject(SYS_FIELD_CACHE_KEY + fieldKey.split("_")[0] + "_");
    }

    /**
     * 根据 fieldKey 和 status 查询动态字段
     *
     * @param fieldKey 字段键
     * @return 动态字段列表
     */
    @RedisCacheAnno(key = SYS_FIELD_CACHE_KEY + "${fieldKey}", expire = 0)
    @Override
    public List<SysField> selectByFieldKey(String fieldKey) {
        SysField queryField = new SysField();
        queryField.setStatus(0L).setFieldKey(fieldKey);

        if (fieldKey != null && !fieldKey.isEmpty()) {
            String[] parts = StringUtils.split(fieldKey, "_", 2);
            if (parts.length > 1 && StringUtils.isNotBlank(parts[1])) {
                return sysFieldMapper.selectSysFieldList(queryField);
            } else {
                return sysFieldMapper.selectByFieldKeyLike(fieldKey);
            }
        }
        return new ArrayList<>();
    }



    @Override
    public <T> T setFormatJson(List<SysField> fieldList, T data) {
        if (fieldList == null || fieldList.isEmpty()) {
            return data;
        }
        return setFormatJson(fieldList, Collections.singletonList(data)).get(0);
    }

    @Override
    public <T> List<T> setFormatJson(List<SysField> fieldList, List<T> data) {
        if (fieldList == null || fieldList.isEmpty()) {
            return data;
        }

        Map<String, List<SysField>> fieldDict = fieldList.stream().collect(Collectors.groupingBy(SysField::getFieldKey));


        for (T obj : data) {
            List<SysField> formatJson = new ArrayList<>();
            String fieldKey = Objects.requireNonNull(ReflectUtils.getFieldValue(obj, "fieldKey")).toString();
            for (SysField item : fieldDict.getOrDefault(fieldKey, Collections.emptyList())) {
                Object value = getJsonAttribute(obj, SnakeAndCamel.toCamelCase(item.getFieldValue()));
                if ("device".equals(item.getFieldType())) {
                    if (value instanceof String) {
                        value = Arrays.asList(((String) value).split(","));
                    } else if (value instanceof Number) {
                        value = Collections.singletonList(value.toString());
                    }
                }
                SysField newItem = new SysField();
                BeanUtils.copyProperties(item, newItem);

                newItem.setSourceValue(value);
                value = formatValue(newItem, value);
                newItem.setValue(value != null ? value.toString() : "");
                formatJson.add(newItem);
            }
            ReflectUtils.setFieldValue(obj, "formatJson", formatJson);
        }

        return data;
    }

    public Object formatValue(SysField fieldItem, Object value) {

        SysField newItem = new SysField();
        BeanUtils.copyProperties(fieldItem, newItem);
        long t1 = System.currentTimeMillis();
        if (value == null || (value instanceof String && ((String) value).isEmpty()) || (value instanceof Number && ((Number) value).intValue() == 0)) {
            return value;
        }

        Object resp;
        if ("dept_id".equals(newItem.getFieldValue())) {
            resp = formatDept(value);
        } else if ("process_id".equals(newItem.getFieldValue())) {
            resp = formatProcess(value);
        } else if ("device".equals(newItem.getFieldType())) {
            resp = formatDevices(value);
        } else if ("dict".equals(newItem.getFieldType())) {
            resp = formatDict(newItem.getDictType(), value);
        } else {
            return value;
        }
        // System.out.println(newItem.getFieldValue() + " " + newItem.getFieldType() + " ---- " + (System.currentTimeMillis() - t1));
        return resp;
    }

    public String formatDict(String dictType, Object value) {
        if (value == null) {
            return "-";
        }
        String dictLabel = DictUtils.getDictLabel(dictType, value.toString());
        return StringUtils.isNotEmpty(dictLabel) ? dictLabel : "-";
    }

    public String formatDevices(Object value) {
        // 等待设备服务
        List<String> deviceIds;
        if (value instanceof String) {
            deviceIds = Arrays.asList(((String) value).split(","));
        } else if (value instanceof Integer) {
            deviceIds = Collections.singletonList(value.toString());
        } else if (value instanceof Double) {
            deviceIds = Collections.singletonList(String.valueOf(((Double) value).intValue()));
        } else {
            return "";
        }

        SysFieldServiceImpl bean = SpringUtils.getBean(SysFieldServiceImpl.class);
        List<String> formatValue = new ArrayList<>();
        for (String deviceId : deviceIds) {
            if (StringUtils.isNotEmpty(deviceId)) {

                EnvDevice envDevice = bean.getByDeviceIdCache(Long.parseLong(deviceId));
                if (envDevice != null) {
                    formatValue.add(envDevice.getDeviceName());
                }
            }
        }
        return String.join(",", formatValue);
    }


    @RedisCacheAnno(key = EnvDeviceServiceImpl.DEVICE_CACHE_KEY + "${deviceId}", expire = 60 * 60 * 24)
    public EnvDevice getByDeviceIdCache(Long deviceId) {
        return envDeviceMapper.selectEnvDeviceByDeviceId(deviceId);
    }

    public String formatProcess(Object value) {
        if (value == null) {
            return "";
        }
        SysProcess process = sysProcessService.selectCacheById(Long.parseLong(value.toString()));
        if (process != null) {
            return process.getAncestorsName();
        } else {
            return "";
        }
    }

    public String formatDept(Object value) {
        if (value == null) {
            return "";
        }
        SysDept dept = sysDeptService.selectCacheById(Long.parseLong(value.toString()));
        if (dept != null) {
            return dept.getDeptName();
        } else {
            return "";
        }
    }



    /**
     * 从给定的Map对象中获取指定路径的属性值。
     *
     * @param data          JSON解析后的Map结构。
     * @param attributePath 属性的路径，支持多层级，形如 "a.b.c"。
     * @return 属性值，如果找到的话；否则返回null。
     */
    public static Object getJsonAttribute(Object data, String attributePath) {
        try {
            String[] attributes = attributePath.split("\\.");
            Object value = data;
            for (String attr : attributes) {
                if (value instanceof Map) {
                    value = ((Map<?, ?>) value).get(attr);
                } else {
                    value = ReflectUtils.invokeGetter(value, attr);
                }
            }
            return value;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<SysField> selectByMenuAndFieldKey(Long menuId, String fieldKey, boolean queryAll) {
        return this.selectByMenuAndFieldKey(menuId, Collections.singletonList(fieldKey), queryAll);
    }


    /**
     * 查询动态字段列表
     *
     * @param menuId     菜单ID
     * @param fieldKey   字段键（可以是单个字符串或列表）
     * @param queryAll   是否查询全部字段
     * @return           合并后的动态字段列表
     */
    @Override
    public List<SysField> selectByMenuAndFieldKey(Long menuId, List<String> fieldKey, boolean queryAll) {
        List<SysField> sysFieldList = new ArrayList<>();

        // 如果 fieldKey 是字符串，则转换为列表
        if (fieldKey == null || fieldKey.isEmpty()) {
            return Collections.emptyList();
        }

        SysFieldServiceImpl bean = SpringUtils.getBean(SysFieldServiceImpl.class);
        for (String key : fieldKey) {
            sysFieldList.addAll(bean.selectByFieldKey(key));
        }

        // 查询页面动态字段
        Map<Long, SysMenuField> sysMenuFieldDict = new HashMap<>();
        if (!menuId.equals(-1L)){
            List<SysMenuField> sysMenuFieldList = sysMenuFieldService.selectByMenuId(menuId);
            sysMenuFieldDict = sysMenuFieldList.stream().collect(Collectors.toMap(SysMenuField::getFieldId, Function.identity(), (p1, p2) -> p2));
        }

        List<SysField> dataList = new ArrayList<>();
        for (SysField sysField : sysFieldList) {
            SysField newData = new SysField();
            BeanUtils.copyProperties(sysField, newData); // 深拷贝，需确保 SysField 支持复制

            SysMenuField menuField = sysMenuFieldDict.get(newData.getFieldId());

            if (!queryAll && menuField == null) {
                continue; // 非查询全部时，跳过未关联字段
            }

            if (menuField != null) {
                newData.setJoinId(menuField.getJoinId());
                newData.setSort(menuField.getSort());
            } else {
                newData.setJoinId(null);
            }

            dataList.add(newData);
        }
        return dataList;
    }

    @Override
    @Transactional
    public String replicate() {
        SysField fieldDbOld = new SysField();
        fieldDbOld.setFieldKey("material_storage_device");
        List<SysField> sysFieldOldList = selectSysFieldList(fieldDbOld);

        for (SysField sysFieldOldItem : sysFieldOldList) {
            sysFieldMapper.deleteSysFieldByFieldId(sysFieldOldItem.getFieldId());
        }

        SysField fieldDb = new SysField();
        fieldDb.setFieldKey("source_3");
        List<SysField> sysFieldMaterialStorageList = selectSysFieldList(fieldDb);

        ArrayList<SysField> batchList = new ArrayList<SysField>();
        for (SysField sysFieldMaterialStorageItem : sysFieldMaterialStorageList) {
            SysField fieldDb2 = new SysField();
            BeanUtils.copyProperties(sysFieldMaterialStorageItem, fieldDb2);
            fieldDb2.setFieldId(null);
            fieldDb2.setSort(null);
            fieldDb2.setFieldRequired(0L); // 假设 field_required 是 Long 类型
            fieldDb2.setFieldKey("material_storage_device");
            batchList.add(fieldDb2);
        }
        if (StringUtils.isNotEmpty(batchList)){
            this.batchSysField(batchList);
        }

        return "OK";
    }

    public void batchSysField(List<SysField> fieldList){
        if (StringUtils.isNotEmpty(fieldList)){
            this.sysFieldMapper.batchSysField(fieldList);
            Set<String> sysFieldKeySet = fieldList.stream().map(SysField::getFieldKey).collect(Collectors.toSet());
            for (String sysFieldKey : sysFieldKeySet) {
                this.clearFieldCache(sysFieldKey);
            }
        }
    }

    @Transactional
    @Async("threadPoolTaskExecutor")
    public void addFieldByDict(String dictType, String dictValue, String scene) {
        List<SysField> fieldList = new ArrayList<>();
        String configType = null;
        String fieldKey = null;

        if ("source_sub_type".equals(dictType)) {
            // 添加污染源默认动态字段
            fieldKey = "source_" + dictValue;
            configType = "sys.field.source.default";
        } else if ("device_sub_type".equals(dictType) && scene != null) {
            // 查询 device_main_type 对应的 env_device 类型列表
            List<SysDictData> deviceMainTypes = DictUtils.getDictCache("device_main_type");
            List<String> deviceMainTypeValues = deviceMainTypes.stream()
                    .map(SysDictData::getDictValue)
                    .collect(Collectors.toList());

            if (deviceMainTypeValues.contains(scene)) {
                // 添加设备默认动态字段
                fieldKey = "device_" + dictValue;
                configType = "sys.field.device.default";
            }
        }

        if (configType != null) {
            String configValue = sysConfigService.selectConfigByKey(configType);
            if (configValue != null && !configValue.isEmpty()) {
                List<SysField> sysFieldList = JSONArray.parseArray(configValue, SysField.class);
                for (SysField sysField : sysFieldList) {
                    sysField.setFieldKey(fieldKey);
                    fieldList.add(sysField);
                }
            }
        }

        if (!fieldList.isEmpty()) {
            try {
                this.batchSysField(fieldList);
            } catch (Exception e) {
                log.error("批量插入动态字段失败，错误内容：{}", e.getMessage(), e);
                throw new RuntimeException("批量插入动态字段失败", e);
            }
        }
    }

    @Override
    public <T> T setFormatJsonByMenu(T data, String menuId, String fieldKeyPrefix) {
        if (StringUtils.isNotNull(data)){
            List<T> dataList = this.setFormatJsonByMenu(Collections.singletonList(data), menuId, fieldKeyPrefix);
            if (StringUtils.isNotEmpty(dataList)){
                return dataList.get(0);
            }else{
                return data;
            }
        }else{
            return data;
        }
    }

    @Override
    public <T> List<T> setFormatJsonByMenu(List<T> dataList, String menuId, String fieldKeyPrefix) {
        if (StringUtils.isBlank(menuId)){
            return dataList;
        }
        Long menuIdLong = Long.parseLong(menuId);
        List<SysField> sysFieldList = this.selectByMenuAndFieldKey(menuIdLong, fieldKeyPrefix, menuIdLong.equals(-1L));
        return this.setFormatJson(sysFieldList, dataList);
    }

    /**
     * 根据 fieldKey 列表和菜单 ID 查询字段配置列表
     *
     * @param fieldKeyList 字段 Key 列表（字符串或列表）
     * @param menuId       菜单 ID
     * @return             匹配的 SysField 列表
     */
    public List<SysField> selectIn(List<String> fieldKeyList, Long menuId) {
        if (fieldKeyList == null || fieldKeyList.isEmpty()) {
            return Collections.emptyList();
        }

        List<SysField> fieldList = new ArrayList<>();

        if (menuId == null || menuId.equals(-1L)) {
            // 如果没有菜单 ID，直接根据 fieldKey 查询
            for (String fieldKey : fieldKeyList) {
                fieldList.addAll(selectByFieldKey(fieldKey));
            }
        } else {
            // 否则通过 MenuFieldService 查询关联字段
            for (String fieldKey : fieldKeyList) {
                fieldList.addAll(this.selectByMenuAndFieldKey(menuId, fieldKey, menuId.equals(-1L)));
            }
        }

        if (fieldList.isEmpty()) {
            return Collections.emptyList();
        }

        // 按照 field_value 分组
        Map<String, List<SysField>> fieldMap = fieldList.stream()
                .collect(Collectors.groupingBy(SysField::getFieldValue, LinkedHashMap::new, Collectors.toList()));

        // 筛选出每个 field_value 对应所有 fieldKey 的字段（即满足所有 key 的字段）
        List<SysField> resList = new ArrayList<>();
        for (Map.Entry<String, List<SysField>> entry : fieldMap.entrySet()) {
            List<SysField> itemList = entry.getValue();
            if (itemList != null && itemList.size() == fieldKeyList.size()) {
                resList.add(itemList.get(0));
            }
        }

        return resList;
    }

    @Override
    public FastExcelDTO exportByField(HttpServletResponse response, List<?> sourceData, List<String> fieldKeyList) {
        return this.exportByField(response, sourceData, fieldKeyList, null);
    }

    @Override
    public FastExcelDTO exportByField(HttpServletResponse response, List<?> sourceData, List<String> fieldKeyList, Long menuId) {
        List<SysField> fieldList = this.selectIn(fieldKeyList, menuId);

        // 处理字典
        Map<Long, String> processDict = sysProcessService.getListAncestorsName().stream().collect(Collectors.toMap(SysProcess::getProcessId, SysProcess::getProcessName));
        Map<Long, String> deptDict = sysDeptService.selectAllDeptList().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));

        Map<String, Map<String, String>> sysDictAll = new HashMap<>();

        for (SysField field : fieldList) {
            if ("dict".equals(field.getFieldType())) {
                sysDictAll.put(field.getDictType(), DictUtils.getDictValueLabelMap(field.getDictType()));
            }
        }


        List<Map<String, Object>> exportData = new ArrayList<>();
        int i = 1;
        List<List<String>> headerList = new ArrayList<>();
        List<List<Object>> dataList = new ArrayList<>();
        headerList.add(Collections.singletonList("序号"));
        for (SysField sysField : fieldList) {
            headerList.add(Collections.singletonList(sysField.getFieldLabel()));
        }
        for (Object item : sourceData) {
            List<Object> nowDataList = new ArrayList<>();
            nowDataList.add(i);
            for (SysField field : fieldList) {
                Object value = getJsonAttribute(item, SnakeAndCamel.toCamelCase(field.getFieldValue()));
                if (StringUtils.isNull(value) || StringUtils.isBlank(value.toString())){
                    value = "";
                }
                else if ("process_id".equals(field.getFieldValue())) {
                    value = processDict.get((Long) ReflectUtils.getFieldValue(item, "processId"));
                } else if ("dept_id".equals(field.getFieldValue())) {
                    value = deptDict.get((Long) ReflectUtils.getFieldValue(item, "deptId"));
                } else if ("dict".equals(field.getFieldType())) {
                    Map<String, String> dict = sysDictAll.get(field.getDictType());
                    if (dict != null) {
                        value = dict.get(value.toString());
                    }
                } else if ("device".equals(field.getFieldType())) {
                    // 处理设备类型字段
                    // 该数据可能出现 字符串类型 / 整型 / 浮点型 和上述三种类型的数组形式
                    List<Long> newValues = new ArrayList<>();
                    if (value instanceof Collection) {
                        for (Object v : ((Collection<?>) value)){
                            newValues.add(Long.parseLong(v.toString()));
                        }
                    } else if (value instanceof String) {
                        newValues.add(Long.parseLong(value.toString()));
                    } else if (value instanceof Number) {
                        newValues.add(((Number) value).longValue());
                    }
                    List<String> deviceNameList = new ArrayList<>();


                    for (Long deviceId : newValues) {
                        EnvDevice device = this.getByDeviceIdCache(deviceId);
                        if (StringUtils.isNotNull(device)){
                            deviceNameList.add(device.getDeviceName());
                        }
                    }

                    value = String.join(",", deviceNameList);
                }
                nowDataList.add(value);
            }
            i++;
            dataList.add(nowDataList);
        }
        return new FastExcelDTO().setDataList(dataList).setHeaderList(headerList);

    }

}
