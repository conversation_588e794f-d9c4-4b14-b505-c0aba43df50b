package com.base.task.service.impl;

import com.base.common.utils.DateUtils;
import com.base.task.domain.TaskFile;
import com.base.task.mapper.TaskFileMapper;
import com.base.task.service.ITaskFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 任务文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Service
public class TaskFileServiceImpl implements ITaskFileService {
    @Autowired
    private TaskFileMapper taskFileMapper;

    /**
     * 查询任务文件
     *
     * @param id 任务文件主键
     * @return 任务文件
     */
    @Override
    public TaskFile selectTaskFileById(Long id) {
        return taskFileMapper.selectTaskFileById(id);
    }

    /**
     * 查询任务文件列表
     *
     * @param taskFile 任务文件
     * @return 任务文件
     */
    @Override
    public List<TaskFile> selectTaskFileList(TaskFile taskFile) {
        return taskFileMapper.selectTaskFileList(taskFile);
    }

    /**
     * 新增任务文件
     *
     * @param taskFile 任务文件
     * @return 结果
     */
    @Override
    public int insertTaskFile(TaskFile taskFile) {
        taskFile.setCreateTime(DateUtils.getNowDate());
        return taskFileMapper.insertTaskFile(taskFile);
    }

    /**
     * 修改任务文件
     *
     * @param taskFile 任务文件
     * @return 结果
     */
    @Override
    public int updateTaskFile(TaskFile taskFile) {
        return taskFileMapper.updateTaskFile(taskFile);
    }

    /**
     * 批量删除任务文件
     *
     * @param ids 需要删除的任务文件主键
     * @return 结果
     */
    @Override
    public int deleteTaskFileByIds(Long[] ids) {
        return taskFileMapper.deleteTaskFileByIds(ids);
    }

    /**
     * 删除任务文件信息
     *
     * @param id 任务文件主键
     * @return 结果
     */
    @Override
    public int deleteTaskFileById(Long id) {
        return taskFileMapper.deleteTaskFileById(id);
    }
}
