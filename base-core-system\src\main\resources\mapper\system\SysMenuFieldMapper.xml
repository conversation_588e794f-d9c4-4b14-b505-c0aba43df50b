<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.system.mapper.SysMenuFieldMapper">

    <resultMap type="SysMenuField" id="SysMenuFieldResult">
        <result property="menuId"    column="menu_id"    />
        <result property="fieldId"    column="field_id"    />
        <result property="fieldName"    column="field_name"    />
        <result property="joinId"    column="join_id"    />
        <result property="sort"    column="sort"    />
        <result property="fieldKey"    column="field_key"    />
    </resultMap>

    <sql id="selectSysMenuFieldVo">
        select menu_id, field_id, field_name, join_id, sort, field_key from sys_menu_field
    </sql>

    <select id="selectSysMenuFieldList" parameterType="SysMenuField" resultMap="SysMenuFieldResult">
        <include refid="selectSysMenuFieldVo"/>
        <where>
            <if test="menuId != null "> and menu_id = #{menuId}</if>
            <if test="fieldId != null "> and field_id = #{fieldId}</if>
            <if test="fieldName != null  and fieldName != ''"> and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="fieldKey != null  and fieldKey != ''"> and field_key = #{fieldKey}</if>
        </where>
    </select>

    <select id="selectSysMenuFieldByJoinId" parameterType="Long" resultMap="SysMenuFieldResult">
        <include refid="selectSysMenuFieldVo"/>
        where join_id = #{joinId}
    </select>

    <insert id="insertSysMenuField" parameterType="SysMenuField" useGeneratedKeys="true" keyProperty="joinId">
        insert into sys_menu_field
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="menuId != null">menu_id,</if>
            <if test="fieldId != null">field_id,</if>
            <if test="fieldName != null">field_name,</if>
            <if test="sort != null">sort,</if>
            <if test="fieldKey != null">field_key,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="menuId != null">#{menuId},</if>
            <if test="fieldId != null">#{fieldId},</if>
            <if test="fieldName != null">#{fieldName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="fieldKey != null">#{fieldKey},</if>
         </trim>
    </insert>

    <update id="updateSysMenuField" parameterType="SysMenuField">
        update sys_menu_field
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuId != null">menu_id = #{menuId},</if>
            <if test="fieldId != null">field_id = #{fieldId},</if>
            <if test="fieldName != null">field_name = #{fieldName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="fieldKey != null">field_key = #{fieldKey},</if>
        </trim>
        where join_id = #{joinId}
    </update>

    <delete id="deleteSysMenuFieldByJoinId" parameterType="Long">
        delete from sys_menu_field where join_id = #{joinId}
    </delete>

    <delete id="deleteSysMenuFieldByJoinIds" parameterType="String">
        delete from sys_menu_field where join_id in
        <foreach item="joinId" collection="array" open="(" separator="," close=")">
            #{joinId}
        </foreach>
    </delete>

    <insert id="batchSysMenuField">
        insert into sys_menu_field( menu_id, field_id, field_name, field_key) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.menuId}, #{item.fieldId}, #{item.fieldName}, #{item.fieldKey})
        </foreach>
    </insert>


    <delete id="deleteByMenuIdAndFieldKey">
        delete from sys_menu_field where menu_id = #{menuId} and field_key = #{fieldKey}
    </delete>
</mapper>
