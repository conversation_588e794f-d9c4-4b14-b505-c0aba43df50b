package com.base.common.utils.mqtt;

import com.base.common.annotation.MqttSubscribe;
import org.eclipse.paho.client.mqttv3.IMqttMessageListener;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class MessageCallbackListener implements IMqttMessageListener {


    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        String messageBody = new String(message.getPayload(), StandardCharsets.UTF_8);
        System.out.println("收到消息：" + topic + ", 消息内容是：" + messageBody);
    }



    @MqttSubscribe(topic = "test/#")
    public void mqttTest02(String message){
        System.out.println("test/#");
        System.out.println(message);
    }
}
