package com.base.device.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class EnvDeviceBatchEditDTO {

    @JsonAlias("device_ids")
    @NotEmpty(message = "要更新的设备不能为空")
    private List<Long> deviceIds;

    @JsonAlias("del_flag")
    private Long delFlag;

    @JsonAlias("process_id")
    private Long processId;
}
