package com.base.dex.domain;

import com.base.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 点位小时数据对象 dev_data_point_hour
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
public class DevDataPointHour extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * pointId
     */
    private String pointId;

    /**
     * sourceValue
     */
    private Double sourceValue;

    /**
     * value
     */
    private Double value;

    /**
     * monitorTime
     */
    private String monitorTime;

    /**
     * validFlag
     */
    private String validFlag;

    /**
     * 点位状态
     */
    private String pointFlag;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * x坐标
     */
    private Double posX;

    /**
     * y坐标
     */
    private Double posY;

    /**
     * 因子名称
     */
    private String factor;

    /**
     * 小时
     */
    private String hour;

    /**
     * 设备子类型
     */
    private String subType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPointId() {
        return pointId;
    }

    public void setPointId(String pointId) {
        this.pointId = pointId;
    }

    public Double getSourceValue() {
        return sourceValue;
    }

    public void setSourceValue(Double sourceValue) {
        this.sourceValue = sourceValue;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public String getMonitorTime() {
        return monitorTime;
    }

    public void setMonitorTime(String monitorTime) {
        this.monitorTime = monitorTime;
    }

    public String getValidFlag() {
        return validFlag;
    }

    public void setValidFlag(String validFlag) {
        this.validFlag = validFlag;
    }

    public String getPointFlag() {
        return pointFlag;
    }

    public void setPointFlag(String pointFlag) {
        this.pointFlag = pointFlag;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Double getPosX() {
        return posX;
    }

    public void setPosX(Double posX) {
        this.posX = posX;
    }

    public Double getPosY() {
        return posY;
    }

    public void setPosY(Double posY) {
        this.posY = posY;
    }

    public String getFactor() {
        return factor;
    }

    public void setFactor(String factor) {
        this.factor = factor;
    }

    public String getHour() {
        return hour;
    }

    public void setHour(String hour) {
        this.hour = hour;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("pointId", getPointId())
                .append("sourceValue", getSourceValue())
                .append("value", getValue())
                .append("monitorTime", getMonitorTime())
                .append("createTime", getCreateTime())
                .append("validFlag", getValidFlag())
                .append("pointFlag", getPointFlag())
                .toString();
    }
}
