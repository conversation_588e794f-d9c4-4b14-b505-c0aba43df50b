package com.base.system.mapper;

import com.base.system.domain.SysStationData;

import java.util.Date;
import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface SysStationDataMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SysStationData selectSysStationDataById(int id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sysStationData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SysStationData> selectSysStationDataList(SysStationData sysStationData);

    /**
     * 新增【请填写功能名称】
     *
     * @param sysStationData 【请填写功能名称】
     * @return 结果
     */
    public int insertSysStationData(SysStationData sysStationData);

    /**
     * 修改【请填写功能名称】
     *
     * @param sysStationData 【请填写功能名称】
     * @return 结果
     */
    public int updateSysStationData(SysStationData sysStationData);

    /**
     * 删除【请填写功能名称】
     *
     * @param regionId 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysStationDataById(int regionId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysStationDataByIds(int[] ids);

    List<SysStationData> selectStationDataByTime(Date startTime);

    /**
     * 批量新增【请填写功能名称】
     *
     * @param sysStationDataList 【请填写功能名称】列表
     * @return 结果
     */
    public int batchSysStationData(List<SysStationData> sysStationDataList);
}
