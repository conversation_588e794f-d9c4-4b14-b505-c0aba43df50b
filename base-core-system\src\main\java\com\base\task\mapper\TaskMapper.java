package com.base.task.mapper;

import com.base.task.domain.Task;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public interface TaskMapper {
    /**
     * 查询任务
     *
     * @param id 任务主键
     * @return 任务
     */
    public Task selectTaskById(Long id);

    /**
     * 查询任务列表
     *
     * @param task 任务
     * @return 任务集合
     */
    public List<Task> selectTaskList(Task task);

    /**
     * 新增任务
     *
     * @param task 任务
     * @return 结果
     */
    public int insertTask(Task task);

    /**
     * 修改任务
     *
     * @param task 任务
     * @return 结果
     */
    public int updateTask(Task task);

    /**
     * 删除任务
     *
     * @param id 任务主键
     * @return 结果
     */
    public int deleteTaskById(Long id);

    /**
     * 批量删除任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskByIds(Long[] ids);

    /**
     * 统计进行中、我发起、已完成的数量
     *
     * @param task 任务对象
     * @return 进行中、我发起、已完成的数量
     */
    public Long getCountTask(Task task);

    /**
     * 查看任务排期
     *
     * @param taskTypeId 任务类型id
     * @return 任务排期列表
     */
    public List<Task> getTaskScheduleList(@Param("taskTypeId") Long taskTypeId);

    /**
     * 批量添加任务
     *
     * @param list 任务列表
     * @return 结果
     */
    public int batchInsertTask(@Param("list") List<Task> list);
}
